<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" viewBox="0 0 320 256">
  <defs>
    <path id="j" fill="#fff" d="M0 0h64v64H0z"/>
  </defs>
  <g clip-path="url(#a)">
    <path fill="#D8FEEE" d="M235 6v20l-4 1a21 21 0 0 0-13 26h-20c-3 0-6-2-6-6V6c0-3 3-6 6-6h31c3 0 6 3 6 6Z"/>
    <path fill="#FFC107" d="M208 17a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"/>
    <path fill="#4CAF50" d="m217 47 1 6h-20c-3 0-6-2-6-6v-8l6-7h7l3 4 13-13c2-2 4-2 6 0l4 4c-8 3-14 11-14 20Z"/>
    <path fill="#F44336" d="M256 47a17 17 0 1 1-35 0 17 17 0 0 1 35 0Z"/>
    <path fill="#fff" d="M246 49h-15a2 2 0 1 1 0-4h15a2 2 0 0 1 0 4Z"/>
  </g>
  <g clip-path="url(#b)">
    <path fill="#D8FEEE" d="M299 6v20l-4 1a21 21 0 0 0-13 26h-20c-3 0-6-2-6-6V6c0-3 3-6 6-6h31c3 0 6 3 6 6Z"/>
    <path fill="#FFC107" d="M272 17a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"/>
    <path fill="#4CAF50" d="m281 47 1 6h-20c-3 0-6-2-6-6v-8l6-7h7l3 4 13-13c2-2 4-2 6 0l4 4c-8 3-14 11-14 20Z"/>
    <path fill="#4CAF50" d="M320 47a17 17 0 1 1-35 0 17 17 0 0 1 35 0Z"/>
    <path fill="#fff" d="m301 54-2-1-4-5a2 2 0 0 1 2-2l4 3 7-9a2 2 0 0 1 3 3l-9 10-1 1Z"/>
  </g>
  <g clip-path="url(#c)">
    <path fill="#D8FEEE" d="M171 6v20l-4 1a21 21 0 0 0-13 26h-20c-3 0-6-2-6-6V6c0-3 3-6 6-6h31c3 0 6 3 6 6Z"/>
    <path fill="#FFC107" d="M144 17a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"/>
    <path fill="#4CAF50" d="m153 47 1 6h-20c-3 0-6-2-6-6v-8l6-7h7l3 4 13-13c2-2 4-2 6 0l4 4c-8 3-14 11-14 20Z"/>
    <path fill="#FFC107" d="M192 47a17 17 0 1 1-35 0 17 17 0 0 1 35 0Z"/>
  </g>
  <path fill="#FFC107" d="M99 85c1 2 3 2 4 0l4-4c2-1 2-3 0-4-1-2-3-2-4 0l-4 4c-2 1-2 3 0 4Zm-5 5c-3-4-3-10 0-13l5-5a9 9 0 1 1 13 13l-5 5c-3 3-9 3-13 0Zm-17 17c1 2 3 2 4 0l4-4c2-1 2-3 0-4-1-2-3-2-4 0l-4 4c-2 1-2 3 0 4Zm-5 5c-3-4-3-10 0-13l5-5a9 9 0 1 1 13 13l-5 5c-3 3-9 3-13 0Z"/>
  <path fill="#F8A700" fill-rule="evenodd" d="M101 83h-5L83 96v5h5l13-13v-5Z" clip-rule="evenodd"/>
  <path fill="#4CAF50" d="M127 109c0 10-7 17-17 17s-17-7-17-17c0-9 7-16 17-16s17 7 17 16Z"/>
  <path fill="#fff" d="m110 118-2-2v-14l2-2 2 2v14l-2 2Z"/>
  <path fill="#fff" d="M117 111h-14l-2-2 2-2h14l2 2-2 2Z"/>
  <path fill="#FFC107" d="M163 85c1 2 3 2 4 0l4-4c2-1 2-3 0-4-1-2-3-2-4 0l-4 4c-2 1-2 3 0 4Zm-5 5c-3-4-3-10 0-13l5-5a9 9 0 1 1 13 13l-5 5c-3 3-9 3-13 0Zm-17 17c1 2 3 2 4 0l4-4c2-1 2-3 0-4-1-2-3-2-4 0l-4 4c-2 1-2 3 0 4Zm-5 5c-3-4-3-10 0-13l5-5a9 9 0 1 1 13 13l-5 5c-3 3-9 3-13 0Z"/>
  <path fill="#F8A700" fill-rule="evenodd" d="M165 83h-5l-13 13v5h5l13-13v-5Z" clip-rule="evenodd"/>
  <path fill="#FFC107" d="M191 109c0 10-7 17-17 17s-17-7-17-17c0-9 7-16 17-16s17 7 17 16Zm100-24c1 2 3 2 4 0l4-4c2-1 2-3 0-4-1-2-3-2-4 0l-4 4c-2 1-2 3 0 4Zm-5 5c-3-4-3-10 0-13l5-5a9 9 0 1 1 13 13l-5 5c-3 3-9 3-13 0Zm-17 17c1 2 3 2 4 0l4-4c2-1 2-3 0-4-1-2-3-2-4 0l-4 4c-2 1-2 3 0 4Zm-5 5c-3-4-3-10 0-13l5-5a9 9 0 1 1 13 13l-5 5c-3 3-9 3-13 0Z"/>
  <path fill="#F8A700" fill-rule="evenodd" d="M293 83h-5l-13 13v5h5l13-13v-5Z" clip-rule="evenodd"/>
  <path fill="#4CAF50" d="M319 109c0 10-7 17-17 17s-17-7-17-17c0-9 7-16 17-16s17 7 17 16Z"/>
  <path fill="#fff" d="m300 116-1-1-5-4v-3h3l3 3 7-8h3v3l-8 9-2 1Z"/>
  <path fill="#FFC107" d="M227 85c1 2 3 2 4 0l4-4c2-1 2-3 0-4-1-2-3-2-4 0l-4 4c-2 1-2 3 0 4Zm-5 5c-3-4-3-10 0-13l5-5a9 9 0 1 1 13 13l-5 5c-3 3-9 3-13 0Zm-17 17c1 2 3 2 4 0l4-4c2-1 2-3 0-4-1-2-3-2-4 0l-4 4c-2 1-2 3 0 4Zm-5 5c-3-4-3-10 0-13l5-5a9 9 0 1 1 13 13l-5 5c-3 3-9 3-13 0Z"/>
  <path fill="#F8A700" fill-rule="evenodd" d="M229 83h-5l-13 13v5h5l13-13v-5Z" clip-rule="evenodd"/>
  <path fill="#F44336" d="M255 109c0 10-7 17-17 17s-17-7-17-17c0-9 7-16 17-16s17 7 17 16Z"/>
  <path fill="#fff" d="M245 111h-14l-2-2 2-2h14l2 2-2 2Z"/>
  <path fill="#FFC107" d="M35 86h4l5-4v-5h-5l-4 5v4Zm-5 5c-3-4-3-10 0-14l5-5c4-3 9-3 13 0 4 4 4 10 0 14l-4 5c-4 3-10 3-14 0Zm-17 18h4l5-5v-4c-2-2-3-2-5 0l-4 4v5Zm-5 4c-3-3-3-10 0-13l5-5c4-4 9-4 13 0s4 10 0 14l-4 4c-4 4-10 4-14 0Z"/>
  <path fill="#F8A700" fill-rule="evenodd" d="M37 84h-4L19 97v5h5l13-14v-4Z" clip-rule="evenodd"/>
  <path fill="#4CAF50" d="M318 173a17 17 0 1 1-35 0 17 17 0 0 1 35 0Z"/>
  <path fill="#C6E7FF" d="M279 173c0-12 10-22 22-22v-16c0-4-3-7-7-7h-30c-5 0-8 3-8 7v41c0 5 3 8 8 8h17l-2-11Z"/>
  <mask id="d" width="46" height="56" x="256" y="128" maskUnits="userSpaceOnUse" style="mask-type:luminance">
    <path fill="#fff" d="M279 173c0-12 10-22 22-22v-16c0-4-3-7-7-7h-30c-5 0-8 3-8 7v41c0 5 3 8 8 8h17l-2-11Z"/>
  </mask>
  <g fill="#91A0AA" mask="url(#d)">
    <path d="M277 141h-11a3 3 0 0 0 0 5h11a3 3 0 0 0 0-5Zm14 10h-25a3 3 0 0 0 0 6h25a3 3 0 0 0 0-6Zm0 11h-25a3 3 0 0 0 0 5h25a3 3 0 0 0 0-5Z"/>
  </g>
  <path fill="#fff" d="M298 180h-1l-5-5a2 2 0 0 1 3-3l3 3 8-8a2 2 0 0 1 3 2l-9 10-1 1h-1Z"/>
  <path fill="#F44336" d="M237 191a17 17 0 1 1 0-35 17 17 0 0 1 0 35Z"/>
  <path fill="#fff" d="M245 175h-15a2 2 0 0 1 0-4h15a2 2 0 0 1 0 4Z"/>
  <path fill="#C6E7FF" d="M215 173c0-12 10-22 22-22v-16c0-4-3-7-7-7h-30c-5 0-8 3-8 7v41c0 5 3 8 8 8h17l-2-11Z"/>
  <mask id="e" width="46" height="56" x="192" y="128" maskUnits="userSpaceOnUse" style="mask-type:luminance">
    <path fill="#fff" d="M215 173c0-12 10-22 22-22v-16c0-4-3-7-7-7h-30c-5 0-8 3-8 7v41c0 5 3 8 8 8h17l-2-11Z"/>
  </mask>
  <g fill="#91A0AA" mask="url(#e)">
    <path d="M213 141h-11a3 3 0 0 0 0 5h11a3 3 0 0 0 0-5Zm14 10h-25a3 3 0 0 0 0 6h25a3 3 0 0 0 0-6Zm0 11h-25a3 3 0 0 0 0 5h25a3 3 0 0 0 0-5Z"/>
  </g>
  <path fill="#FFC107" d="M173 191a17 17 0 1 1 0-35 17 17 0 0 1 0 35Z"/>
  <path fill="#C6E7FF" d="M151 173c0-12 10-22 22-22v-16c0-4-3-7-7-7h-30c-5 0-8 3-8 7v41c0 5 3 8 8 8h17l-2-11Z"/>
  <mask id="f" width="46" height="56" x="128" y="128" maskUnits="userSpaceOnUse" style="mask-type:luminance">
    <path fill="#fff" d="M151 173c0-12 10-22 22-22v-16c0-4-3-7-7-7h-30c-5 0-8 3-8 7v41c0 5 3 8 8 8h17l-2-11Z"/>
  </mask>
  <g fill="#91A0AA" mask="url(#f)">
    <path d="M149 141h-11a3 3 0 0 0 0 5h11a3 3 0 0 0 0-5Zm14 10h-25a3 3 0 0 0 0 6h25a3 3 0 0 0 0-6Zm0 11h-25a3 3 0 0 0 0 5h25a3 3 0 0 0 0-5Z"/>
  </g>
  <path fill="#4CAF50" d="M109 191a17 17 0 1 1 0-35 17 17 0 0 1 0 35Z"/>
  <path fill="#fff" d="m109 183-2-2v-15a2 2 0 0 1 4 0v15l-2 2Z"/>
  <path fill="#fff" d="M117 175h-15a2 2 0 0 1 0-4h15a2 2 0 0 1 0 4Z"/>
  <path fill="#C6E7FF" d="M87 173c0-12 10-22 22-22v-16c0-4-3-7-7-7H72c-5 0-8 3-8 7v41c0 5 3 8 8 8h17l-2-11Z"/>
  <mask id="g" width="46" height="56" x="64" y="128" maskUnits="userSpaceOnUse" style="mask-type:luminance">
    <path fill="#fff" d="M87 173c0-12 10-22 22-22v-16c0-4-3-7-7-7H72c-5 0-8 3-8 7v41c0 5 3 8 8 8h17l-2-11Z"/>
  </mask>
  <g fill="#91A0AA" mask="url(#g)">
    <path d="M85 141H74a3 3 0 0 0 0 5h11a3 3 0 0 0 0-5Zm14 10H74a3 3 0 0 0 0 6h25a3 3 0 0 0 0-6Zm0 11H74a3 3 0 0 0 0 5h25a3 3 0 0 0 0-5Z"/>
  </g>
  <path fill="#000" fill-opacity=".1" d="M45 255a17 17 0 1 1 0-35 17 17 0 0 1 0 35Z"/>
  <path fill="#000" fill-opacity=".1" d="M23 237c0-12 10-22 22-22v-16c0-4-3-7-7-7H8c-5 0-8 3-8 7v41c0 5 3 8 8 8h17l-2-11Zm18-2 3-3v10l1 1 2-1v-10l3 3 1 1 1-1v-2l-6-5h-2l-5 5v2h2Z"/>
  <path fill="#000" fill-opacity=".1" d="m53 238-1 1v4l-2 1H40l-1-1v-4l-2-1-1 1v4c0 2 2 4 4 4h10c3 0 5-2 5-4v-4l-2-1Zm34-1c0-12 10-22 22-22v-16c0-4-3-7-7-7H72c-5 0-8 3-8 7v41c0 5 3 8 8 8h17l-2-11Z"/>
  <path fill="#4CAF50" d="M109 255a17 17 0 1 1 0-35 17 17 0 0 1 0 35Z"/>
  <path fill="#fff" d="m109 247-2-2v-15a2 2 0 0 1 4 0v15l-2 2Z"/>
  <path fill="#fff" d="M117 239h-15a2 2 0 0 1 0-4h15a2 2 0 0 1 0 4Z"/>
  <g clip-path="url(#h)">
    <path fill="#D8FEEE" d="M107 6v20l-4 1a21 21 0 0 0-13 26H70c-3 0-6-2-6-6V6c0-3 3-6 6-6h31c3 0 6 3 6 6Z"/>
    <path fill="#FFC107" d="M80 17a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"/>
    <path fill="#4CAF50" d="m89 47 1 6H70c-3 0-6-2-6-6v-8l6-7h7l3 4 13-13c2-2 4-2 6 0l4 4c-8 3-14 11-14 20Z"/>
    <path fill="#4CAF50" d="M128 47a17 17 0 1 1-35 0 17 17 0 0 1 35 0Z"/>
    <path fill="#fff" d="m111 56-2-2V39a2 2 0 0 1 4 0v15l-2 2Z"/>
    <path fill="#fff" d="M118 49h-15a2 2 0 0 1 0-4h15a2 2 0 0 1 0 4Z"/>
  </g>
  <mask id="i" width="64" height="64" x="0" y="0" maskUnits="userSpaceOnUse" style="mask-type:luminance">
    <path fill="#fff" d="M64 0H0v64h64V0Z"/>
  </mask>
  <g mask="url(#i)">
    <path fill="#D8FEEE" d="M36 50c0-4 2-7 5-8l1-6V6c0-3-2-6-5-6H6C3 0 0 3 0 6v41c0 3 3 6 6 6h30v-3Z"/>
    <path fill="#FFC107" d="M16 17a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"/>
    <path fill="#4CAF50" d="M42 30v17c0 6-5 6-5 6H6c-3 0-6-3-6-6v-9l6-6h7l3 4 13-13c1-2 4-2 6 0l7 7Z"/>
  </g>
  <defs>
    <clipPath id="a">
      <use xlink:href="#j" transform="translate(192)"/>
    </clipPath>
    <clipPath id="b">
      <use xlink:href="#j" transform="translate(256)"/>
    </clipPath>
    <clipPath id="c">
      <use xlink:href="#j" transform="translate(128)"/>
    </clipPath>
    <clipPath id="h">
      <use xlink:href="#j" transform="translate(64)"/>
    </clipPath>
  </defs>
</svg>
