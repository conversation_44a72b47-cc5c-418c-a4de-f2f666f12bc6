.mwai-context-menu-portal .mwai-context-menu {
  background: var(--mwai-backgroundHeaderColor);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--mwai-borderRadius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-size: 13px;
  color: var(--mwai-fontColor);
}
.mwai-context-menu-portal .mwai-context-menu .mwai-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}
.mwai-context-menu-portal .mwai-context-menu .mwai-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
.mwai-context-menu-portal .mwai-context-menu .mwai-menu-item.mwai-danger {
  color: #dc3545;
}
.mwai-context-menu-portal .mwai-context-menu .mwai-menu-item.mwai-danger:hover {
  background-color: rgba(220, 53, 69, 0.1);
}
.mwai-context-menu-portal .mwai-context-menu .mwai-menu-item svg {
  flex-shrink: 0;
}

.mwai-chunks {
  padding: 8px;
  background: rgba(0, 0, 0, 0.03);
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
  font-size: 11px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}
.mwai-chunks.mwai-chunks-collapsed .mwai-chunks-header {
  margin-bottom: 0 !important;
}
.mwai-chunks .mwai-chunks-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  color: #6b7280;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
.mwai-chunks .mwai-chunks-header .mwai-chunks-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mwai-chunks .mwai-chunks-header .mwai-chunks-status {
  margin-left: 4px;
  font-weight: 500;
}
.mwai-chunks .mwai-chunks-header .mwai-chunks-toggle {
  background: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  padding: 2px;
  width: 30px;
  height: 20px;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: 4px;
}
.mwai-chunks .mwai-chunks-header .mwai-chunks-toggle:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #374151;
}
.mwai-chunks .mwai-chunk {
  margin-bottom: 4px;
  padding: 6px 8px;
  background: white;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}
.mwai-chunks .mwai-chunk .mwai-chunk-header {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}
.mwai-chunks .mwai-chunk .mwai-chunk-header .mwai-chunk-time {
  color: #9ca3af;
  font-size: 10px;
  font-variant-numeric: tabular-nums;
}
.mwai-chunks .mwai-chunk .mwai-chunk-header .mwai-chunk-type {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  color: white;
}
.mwai-chunks .mwai-chunk .mwai-chunk-header .mwai-chunk-data {
  flex: 1;
  color: #374151;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.mwai-chunks .mwai-chunk .mwai-chunk-header .mwai-chunk-expand {
  color: #9ca3af;
  transition: transform 0.2s ease;
}
.mwai-chunks .mwai-chunk .mwai-chunk-details {
  margin-top: 8px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 3px;
  overflow-x: auto;
}
.mwai-chunks .mwai-chunk .mwai-chunk-details pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  color: #4b5563;
}

.mwai-messages-theme {
  --mwai-spacing: 10px;
  --mwai-fontSize: 13px;
  --mwai-lineHeight: 1.5;
  --mwai-borderRadius: 10px;
  --mwai-width: 460px;
  --mwai-maxHeight: 40vh;
  --mwai-iconTextColor: black;
  --mwai-iconTextBackgroundColor: white;
  --mwai-fontColor: black;
  --mwai-backgroundPrimaryColor: #fafafa;
  --mwai-backgroundHeaderColor: #0084ff;
  --mwai-bubbleColor: #0084ff;
  --mwai-headerButtonsColor: white;
  --mwai-conversationsBackgroundColor: white;
  --mwai-backgroundUserColor: #0084ff;
  --mwai-backgroundAiColor: #eee;
  --mwai-backgroundAiSecondaryColor: #ddd;
  --mwai-errorBackgroundColor: #6d2f2a;
  --mwai-errorTextColor: #FFFFFF;
}
.mwai-messages-theme * {
  box-sizing: border-box;
}
.mwai-messages-theme .mwai-body {
  display: flex;
  background: var(--mwai-backgroundPrimaryColor);
  font-size: var(--mwai-fontSize);
  color: var(--mwai-fontColor);
  border-radius: var(--mwai-borderRadius);
  flex-direction: column;
}
.mwai-messages-theme .mwai-shortcuts {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.mwai-messages-theme .mwai-shortcuts .mwai-shortcut {
  margin-bottom: 5px;
  font-size: var(--mwai-fontSize);
  height: inherit;
  min-height: inherit;
  width: inherit;
  min-width: 90px;
  border-radius: var(--mwai-borderRadius);
  padding: 7px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: end;
}
.mwai-messages-theme .mwai-shortcuts .mwai-shortcut.mwai-success {
  background: #4caf50;
  color: white;
  box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.15);
}
.mwai-messages-theme .mwai-shortcuts .mwai-shortcut.mwai-danger {
  background: #f44336;
  color: white;
  box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.15);
}
.mwai-messages-theme .mwai-shortcuts .mwai-shortcut.mwai-warning {
  background: #ff9800;
  color: white;
  box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.15);
}
.mwai-messages-theme .mwai-shortcuts .mwai-shortcut.mwai-info {
  background: #2196f3;
  color: white;
  box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.15);
}
.mwai-messages-theme .mwai-shortcuts .mwai-shortcut .mwai-icon {
  margin-right: 5px;
}
.mwai-messages-theme .mwai-shortcuts .mwai-shortcut .mwai-icon img {
  max-height: 16px;
  width: auto;
}
.mwai-messages-theme .mwai-shortcuts .mwai-shortcut:hover {
  filter: brightness(1.1);
}
.mwai-messages-theme .mwai-blocks {
  display: flex;
  flex-direction: column;
  padding: var(--mwai-spacing);
}
.mwai-messages-theme .mwai-blocks .mwai-block p:first-child {
  margin-top: 0;
}
.mwai-messages-theme .mwai-blocks button {
  cursor: pointer;
}
.mwai-messages-theme .mwai-conversation {
  display: flex;
  flex-direction: column;
  overflow: auto;
  max-height: var(--mwai-maxHeight);
  padding: var(--mwai-spacing) var(--mwai-spacing) 0;
}
.mwai-messages-theme .mwai-conversation .mwai-reply {
  margin-bottom: var(--mwai-spacing);
  padding: 7px 12px;
  border-radius: 15px;
  font-size: var(--mwai-fontSize);
  color: var(--mwai-fontColor);
  position: relative;
}
.mwai-messages-theme .mwai-conversation .mwai-reply .mwai-name,
.mwai-messages-theme .mwai-conversation .mwai-reply .mwai-name-text {
  display: none;
}
.mwai-messages-theme .mwai-conversation .mwai-reply * > p:first-child {
  margin-top: 0;
}
.mwai-messages-theme .mwai-conversation .mwai-reply * > p:last-child {
  margin-bottom: 0;
}
.mwai-messages-theme .mwai-conversation .mwai-reply.mwai-ai {
  align-self: flex-start;
  background: var(--mwai-backgroundAiColor);
  margin-left: 5px;
}
.mwai-messages-theme .mwai-conversation .mwai-reply.mwai-ai::before, .mwai-messages-theme .mwai-conversation .mwai-reply.mwai-ai::after {
  content: "";
  position: absolute;
  z-index: 1;
  bottom: 0;
  left: -10px;
  width: 10px;
  height: 20px;
  background: var(--mwai-backgroundPrimaryColor);
  border-bottom-right-radius: 10px;
}
.mwai-messages-theme .mwai-conversation .mwai-reply.mwai-ai::before {
  z-index: 0;
  left: -7px;
  height: 20px;
  width: 20px;
  background: var(--mwai-backgroundAiColor);
  border-bottom-right-radius: 15px;
}
.mwai-messages-theme .mwai-conversation .mwai-reply.mwai-user {
  align-self: flex-end;
  background: var(--mwai-backgroundUserColor);
  color: white;
  margin-right: 10px;
}
.mwai-messages-theme .mwai-conversation .mwai-reply.mwai-user::before, .mwai-messages-theme .mwai-conversation .mwai-reply.mwai-user::after {
  content: "";
  position: absolute;
  z-index: 1;
  bottom: 0;
  right: -10px;
  width: 10px;
  height: 20px;
  background: var(--mwai-backgroundPrimaryColor);
  border-bottom-left-radius: 10px;
}
.mwai-messages-theme .mwai-conversation .mwai-reply.mwai-user::before {
  z-index: 0;
  right: -10px;
  height: 20px;
  width: 20px;
  background: var(--mwai-backgroundUserColor);
  background-attachment: fixed;
  border-bottom-left-radius: 15px;
}
.mwai-messages-theme .mwai-text {
  flex: auto;
}
.mwai-messages-theme .mwai-text .mwai-image {
  display: block;
  max-width: 250px;
  height: auto;
  margin: 0 0 10px 0;
  border-radius: var(--mwai-borderRadius);
}
.mwai-messages-theme .mwai-text .mwai-filename {
  display: flex;
  text-decoration: none;
  border: 1px solid var(--mwai-backgroundPrimaryColor);
  border-radius: var(--mwai-borderRadius);
  color: white;
  padding: 5px 10px;
  margin-bottom: 10px;
}
.mwai-messages-theme .mwai-text > span > p > *:first-child {
  margin-top: 0;
}
.mwai-messages-theme .mwai-text a {
  color: #2196f3;
}
.mwai-messages-theme .mwai-text h1 {
  font-size: 200%;
}
.mwai-messages-theme .mwai-text h2 {
  font-size: 160%;
}
.mwai-messages-theme .mwai-text h3 {
  font-size: 140%;
}
.mwai-messages-theme .mwai-text h4 {
  font-size: 120%;
}
.mwai-messages-theme .mwai-text p {
  font-size: var(--mwai-fontSize);
  line-height: var(--mwai-lineHeight);
}
.mwai-messages-theme .mwai-text p code {
  background: var(--mwai-backgroundAiSecondaryColor);
  padding: 2px 6px;
  border-radius: 8px;
  font-size: calc(var(--mwai-fontSize) * 0.9);
  font-family: system-ui;
}
.mwai-messages-theme .mwai-text pre {
  color: var(--mwai-fontColor);
  border-radius: var(--mwai-borderRadius);
  break-after: auto;
  white-space: pre-wrap;
  max-width: 100%;
  width: 100%;
  font-family: system-ui;
  background: var(--mwai-backgroundAiSecondaryColor);
  padding: var(--mwai-spacing);
}
.mwai-messages-theme .mwai-text pre code {
  padding: 0 !important;
  font-family: system-ui;
  background: var(--mwai-backgroundAiSecondaryColor);
}
.mwai-messages-theme .mwai-text ol {
  padding: 0;
  margin: 0 0 0 20px;
}
.mwai-messages-theme .mwai-text table {
  width: 100%;
  border: 2px solid var(--mwai-backgroundAiSecondaryColor);
  border-collapse: collapse;
}
.mwai-messages-theme .mwai-text thead {
  background: var(--mwai-backgroundAiSecondaryColor);
}
.mwai-messages-theme .mwai-text tr,
.mwai-messages-theme .mwai-text td {
  padding: 2px 5px;
}
.mwai-messages-theme .mwai-text td {
  border: 2px solid var(--mwai-backgroundAiSecondaryColor);
}
.mwai-messages-theme .mwai-text .mwai-typewriter {
  display: inline-block;
}
.mwai-messages-theme .mwai-text .mwai-typewriter > :first-child {
  margin-top: 0;
}
.mwai-messages-theme .mwai-text > *:first-child {
  margin-top: 0;
}
.mwai-messages-theme .mwai-text > *:last-child {
  margin-bottom: 0;
}
.mwai-messages-theme .mwai-input {
  display: flex;
  align-items: center;
  padding: var(--mwai-spacing);
}
.mwai-messages-theme .mwai-input .mwai-input-text {
  flex: auto;
  position: relative;
  display: flex;
  background: var(--mwai-backgroundPrimaryColor);
  border-radius: var(--mwai-borderRadius);
  border: 1px solid var(--mwai-backgroundAiSecondaryColor);
  overflow: hidden;
}
.mwai-messages-theme .mwai-input .mwai-input-text.mwai-blocked img {
  filter: grayscale(100%);
  opacity: 0.5;
}
.mwai-messages-theme .mwai-input .mwai-input-text.mwai-dragging {
  border: 1px dashed var(--mwai-backgroundAiSecondaryColor);
}
.mwai-messages-theme .mwai-input .mwai-input-text textarea {
  background: var(--mwai-backgroundPrimaryColor);
  color: var(--mwai-fontColor);
  flex: auto;
  padding: var(--mwai-spacing);
  border: none;
  font-size: var(--mwai-fontSize);
  resize: none;
  font-family: inherit;
  margin: 0;
  overflow: hidden;
  min-height: inherit;
}
.mwai-messages-theme .mwai-input .mwai-input-text textarea:focus {
  outline: none;
  box-shadow: none;
}
.mwai-messages-theme .mwai-input .mwai-input-text textarea::placeholder {
  color: var(--mwai-fontColor);
  opacity: 0.5;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-microphone {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 5px;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-microphone svg {
  opacity: 0.5;
  filter: grayscale(100%);
  transition: opacity 0.3s ease-out;
  cursor: pointer;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-microphone[active=true] svg {
  opacity: 1;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-microphone[disabled] svg {
  opacity: 0;
  cursor: not-allowed;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon {
  background: url("icons/white-icons.svg");
  background-size: 500%;
  background-position: 0px -96px;
  width: 32px;
  height: 32px;
  margin-top: calc(var(--mwai-spacing) / 2);
  margin-left: 5px;
  z-index: 100;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon.mwai-idle-add {
  background-position: -32px -96px;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon.mwai-image-add {
  background-position: -32px 0px;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon.mwai-image-up {
  background-position: -64px 0px;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon.mwai-image-del {
  background-position: -96px 0px;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon.mwai-image-ok {
  background-position: -128px 0px;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon.mwai-document-add {
  background-position: -32px -64px;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon.mwai-document-up {
  background-position: -64px -64px;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon.mwai-document-del {
  background-position: -96px -64px;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon.mwai-document-ok {
  background-position: -128px -64px;
}
.mwai-messages-theme .mwai-input .mwai-input-text .mwai-file-upload-icon .mwai-file-upload-progress {
  position: absolute;
  font-size: 8px;
  width: 21px;
  top: 24px;
  left: 23px;
  overflow: hidden;
  text-align: center;
  font-weight: bold;
  color: white;
}
.mwai-messages-theme .mwai-input .mwai-input-submit {
  width: 70px;
}
.mwai-messages-theme button {
  margin-left: var(--mwai-spacing);
  padding: 5px 15px;
  background-color: var(--mwai-backgroundUserColor);
  color: white;
  border: none;
  border-radius: var(--mwai-borderRadius);
  cursor: pointer;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mwai-messages-theme button .mwai-timer {
  margin-left: 5px;
  margin-right: 5px;
  font-size: 11px;
}
.mwai-messages-theme button:hover {
  filter: brightness(1.2);
}
.mwai-messages-theme button[disabled] {
  cursor: not-allowed;
}
.mwai-messages-theme button[disabled] span {
  opacity: 0.5;
}
.mwai-messages-theme button[disabled].mwai-busy span {
  display: none;
}
.mwai-messages-theme button[disabled].mwai-busy:before {
  content: "";
  width: 18px;
  height: 18px;
  margin: auto;
  border: 3px solid transparent;
  border-top-color: var(--mwai-fontColor);
  border-radius: 50%;
  animation: mwai-button-spinner 1s ease infinite;
}
.mwai-messages-theme .mwai-compliance {
  opacity: 0.5;
  margin-top: calc(-1 * var(--mwai-spacing));
  padding: calc(var(--mwai-spacing) / 1.5) var(--mwai-spacing);
  font-size: smaller;
  color: var(--mwai-fontColor);
  text-align: left;
}
.mwai-messages-theme .mwai-gallery {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 5px;
}
.mwai-messages-theme .mwai-gallery img {
  width: 100%;
}

.mwai-messages-theme.mwai-transition, .mwai-messages-theme .mwai-transition {
  opacity: 0;
  transition: opacity 350ms ease-in-out;
}
.mwai-messages-theme.mwai-transition-visible, .mwai-messages-theme .mwai-transition-visible {
  opacity: 1;
}
.mwai-messages-theme .mwai-text {
  overflow-wrap: anywhere;
}
.mwai-messages-theme .mwai-text img {
  max-width: 100%;
}
.mwai-messages-theme .mwai-text div p:first-child {
  margin-top: 0;
}
.mwai-messages-theme .mwai-text div p:last-child {
  margin-bottom: 0;
}
.mwai-messages-theme .mwai-trigger {
  position: absolute;
  right: 0;
  bottom: 0;
  transition: all 0.2s ease-out;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: end;
}
.mwai-messages-theme .mwai-trigger .mwai-icon-text-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.mwai-messages-theme .mwai-trigger .mwai-icon-text-container .mwai-icon-text {
  background: var(--mwai-iconTextBackgroundColor);
  color: var(--mwai-iconTextColor);
  box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
  max-width: 200px;
  font-size: 13px;
  margin-bottom: 15px;
  padding: 10px 15px;
  border-radius: 8px;
}
.mwai-messages-theme .mwai-trigger .mwai-icon-text-container .mwai-icon-text-close {
  color: var(--mwai-iconTextColor);
  background: var(--mwai-iconTextBackgroundColor);
  padding: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: none;
  justify-content: center;
  align-items: center;
  margin-bottom: 3px;
}
.mwai-messages-theme .mwai-trigger .mwai-icon-text-container:hover {
  cursor: pointer;
}
.mwai-messages-theme .mwai-trigger .mwai-icon-text-container:hover .mwai-icon-text-close {
  display: flex;
  font-size: 12px;
}
.mwai-messages-theme .mwai-trigger .mwai-icon-text-container:hover .mwai-icon-text-close:hover {
  filter: brightness(1.2);
}
@media (max-width: 760px) {
  .mwai-messages-theme .mwai-trigger .mwai-icon-text-container .mwai-icon-text-close {
    display: flex;
  }
}
.mwai-messages-theme .mwai-trigger .mwai-icon-container .mwai-icon {
  filter: drop-shadow(0px 0px 15px rgba(0, 0, 0, 0.15));
  transition: all 0.2s ease-out;
}
.mwai-messages-theme .mwai-trigger .mwai-icon-container .mwai-icon:hover {
  cursor: pointer;
  transform: scale(1.05);
}
.mwai-messages-theme.mwai-window {
  position: fixed;
  right: 30px;
  bottom: 30px;
  width: var(--mwai-width);
  z-index: 9999;
}
.mwai-messages-theme.mwai-window .mwai-header {
  display: none;
  justify-content: flex-end;
  align-items: center;
  border-radius: var(--mwai-borderRadius) var(--mwai-borderRadius) 0 0;
  background: var(--mwai-backgroundHeaderColor);
}
.mwai-messages-theme.mwai-window .mwai-header .mwai-buttons {
  display: flex;
  align-items: center;
}
.mwai-messages-theme.mwai-window .mwai-header .mwai-buttons .mwai-resize-button {
  justify-content: center;
  height: 32px;
  width: 22px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mwai-messages-theme.mwai-window .mwai-header .mwai-buttons .mwai-resize-button:before {
  transition: all 0.2s ease-out;
  content: " ";
  cursor: pointer;
  position: absolute;
  height: 13px;
  width: 13px;
  border: 1px solid var(--mwai-headerButtonsColor);
}
.mwai-messages-theme.mwai-window .mwai-header .mwai-buttons .mwai-resize-button:hover:before {
  width: 16px;
  height: 16px;
}
.mwai-messages-theme.mwai-window .mwai-header .mwai-buttons .mwai-close-button {
  justify-content: center;
  height: 32px;
  width: 33px;
  cursor: pointer;
  border-radius: var(--mwai-borderRadius);
}
.mwai-messages-theme.mwai-window .mwai-header .mwai-buttons .mwai-close-button:before {
  transition: all 0.2s ease-out;
  transform: translate(16px, 5px) rotate(45deg);
}
.mwai-messages-theme.mwai-window .mwai-header .mwai-buttons .mwai-close-button:after {
  transition: all 0.2s ease-out;
  transform: translate(16px, 5px) rotate(-45deg);
}
.mwai-messages-theme.mwai-window .mwai-header .mwai-buttons .mwai-close-button:before, .mwai-messages-theme.mwai-window .mwai-header .mwai-buttons .mwai-close-button:after {
  content: " ";
  cursor: pointer;
  position: absolute;
  height: 22px;
  width: 1px;
  background-color: var(--mwai-headerButtonsColor);
}
.mwai-messages-theme.mwai-window .mwai-header .mwai-buttons .mwai-close-button:hover:before {
  opacity: 1;
  transform: translate(16px, 5px) rotate(135deg);
}
.mwai-messages-theme.mwai-window .mwai-header .mwai-buttons .mwai-close-button:hover:after {
  opacity: 1;
  transform: translate(16px, 5px) rotate(45deg);
}
.mwai-messages-theme.mwai-window .mwai-body {
  display: none;
  opacity: 0;
  max-height: var(--mwai-maxHeight);
  border-radius: 0 0 var(--mwai-borderRadius) var(--mwai-borderRadius);
}
.mwai-messages-theme.mwai-window.mwai-bottom-left {
  bottom: 30px;
  right: inherit;
  left: 30px;
}
.mwai-messages-theme.mwai-window.mwai-bottom-left .mwai-trigger {
  right: inherit;
  left: 0;
}
.mwai-messages-theme.mwai-window.mwai-top-right {
  top: 30px;
  bottom: inherit;
  right: 30px;
}
.mwai-messages-theme.mwai-window.mwai-top-right .mwai-trigger {
  top: 0;
  bottom: inherit;
}
.mwai-messages-theme.mwai-window.mwai-top-left {
  top: 30px;
  bottom: inherit;
  right: inherit;
  left: 30px;
}
.mwai-messages-theme.mwai-window.mwai-top-left .mwai-trigger {
  top: 0;
  bottom: inherit;
  right: inherit;
  left: 0;
}
.mwai-messages-theme.mwai-window.mwai-top-left .mwai-trigger, .mwai-messages-theme.mwai-window.mwai-bottom-left .mwai-trigger {
  align-items: flex-start;
}
.mwai-messages-theme.mwai-window.mwai-top-right .mwai-trigger, .mwai-messages-theme.mwai-window.mwai-top-left .mwai-trigger {
  flex-direction: column-reverse;
}
.mwai-messages-theme.mwai-window.mwai-top-right .mwai-trigger .mwai-icon-text, .mwai-messages-theme.mwai-window.mwai-top-left .mwai-trigger .mwai-icon-text {
  margin-bottom: 0;
  margin-top: 15px;
}
.mwai-messages-theme.mwai-window.mwai-fullscreen .mwai-header .mwai-buttons {
  margin-bottom: 0px;
}
.mwai-messages-theme.mwai-window.mwai-fullscreen .mwai-header .mwai-buttons .mwai-resize-button:before {
  width: 16px;
  height: 16px;
}
.mwai-messages-theme.mwai-window.mwai-fullscreen .mwai-header .mwai-buttons .mwai-resize-button:hover:before {
  width: 13px;
  height: 13px;
}
.mwai-messages-theme.mwai-fullscreen:not(.mwai-window), .mwai-messages-theme.mwai-fullscreen.mwai-window.mwai-open {
  position: fixed;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  top: 0 !important;
  width: 100%;
  height: 100%;
  max-height: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  z-index: 999999;
  background-color: var(--mwai-backgroundSecondaryColor);
}
.mwai-messages-theme.mwai-fullscreen:not(.mwai-window) .mwai-header, .mwai-messages-theme.mwai-fullscreen.mwai-window.mwai-open .mwai-header {
  border-radius: 0;
}
.mwai-messages-theme.mwai-fullscreen:not(.mwai-window) .mwai-body, .mwai-messages-theme.mwai-fullscreen.mwai-window.mwai-open .mwai-body {
  height: 100%;
  max-height: inherit;
  border-radius: 0;
}
.mwai-messages-theme.mwai-fullscreen:not(.mwai-window) .mwai-body .mwai-conversation, .mwai-messages-theme.mwai-fullscreen.mwai-window.mwai-open .mwai-body .mwai-conversation {
  flex: auto;
  max-height: none;
}
.mwai-messages-theme.mwai-window.mwai-open .mwai-header {
  display: flex;
}
.mwai-messages-theme.mwai-window.mwai-open .mwai-body {
  display: flex;
  transition: opacity 200ms ease-in-out 0s;
  opacity: 1;
}
.mwai-messages-theme.mwai-window.mwai-open .mwai-trigger {
  display: none;
}
.mwai-messages-theme .mwai-error {
  margin: var(--mwai-spacing);
  color: white;
  background: rgba(180, 55, 55, 0.55);
  padding: var(--mwai-spacing);
  border-radius: var(--mwai-borderRadius);
}
.mwai-messages-theme .mwai-error:hover {
  cursor: pointer;
  background: rgba(180, 44, 44, 0.85);
}
.mwai-messages-theme.mwai-bubble .mwai-icon-container {
  background: var(--mwai-bubbleColor);
  width: 60px;
  height: 60px;
  border-radius: 100%;
  transition: all 0.2s ease-out;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mwai-messages-theme.mwai-bubble .mwai-icon-container .mwai-icon {
  max-width: 50%;
  max-height: 50%;
  filter: none;
}
.mwai-messages-theme.mwai-bubble .mwai-icon-container .mwai-icon:hover {
  transform: none;
}
.mwai-messages-theme.mwai-bubble .mwai-icon-container .mwai-emoji {
  font-size: 30px !important;
}
.mwai-messages-theme.mwai-bubble .mwai-icon-container:hover {
  cursor: pointer;
  filter: brightness(1.1);
}
@media (max-width: 760px) {
  .mwai-messages-theme.mwai-window.mwai-open {
    position: fixed;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    top: 0 !important;
    width: 100%;
    height: 100%;
    max-height: 100%;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    margin: 0;
    z-index: 999999;
    background-color: var(--mwai-backgroundSecondaryColor);
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-header {
    border-radius: 0;
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-body {
    height: 100%;
    max-height: inherit;
    border-radius: 0;
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-body .mwai-conversation {
    flex: auto;
    max-height: none;
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-input {
    flex-direction: column;
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-input button {
    font-size: 16px;
    margin-left: 0;
    width: 100%;
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-input .mwai-input-text {
    width: 100%;
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-input .mwai-input-text input, .mwai-messages-theme.mwai-window.mwai-open .mwai-input .mwai-input-text textarea {
    font-size: 16px;
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-body {
    display: flex;
    transition: opacity 200ms ease-in-out 0s;
    opacity: 1;
    height: 100%;
    max-height: inherit;
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-body .mwai-conversation {
    flex: auto;
    max-height: none;
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-resize-button {
    display: none !important;
  }
  .mwai-messages-theme.mwai-window.mwai-open .mwai-trigger {
    display: none;
  }
}
@keyframes mwai-button-spinner {
  from {
    transform: rotate(0turn);
  }
  to {
    transform: rotate(1turn);
  }
}
.mwai-messages-theme button:not(.mwai-busy):before {
  content: none !important;
  display: none !important;
  animation: none !important;
}
.mwai-messages-theme .admin-bar .mwai-fullscreen:not(.mwai-window),
.mwai-messages-theme .admin-bar .mwai-fullscreen.mwai-window.mwai-open {
  top: 32px;
}
.mwai-messages-theme pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
}
.mwai-messages-theme code.hljs {
  padding: 3px 5px;
}
.mwai-messages-theme .hljs {
  color: #333;
  background: #f0f0f0;
}
.mwai-messages-theme .hljs-subst {
  color: #333;
}
.mwai-messages-theme .hljs-comment {
  color: #888;
}
.mwai-messages-theme .hljs-attr, .mwai-messages-theme .hljs-doctag, .mwai-messages-theme .hljs-keyword, .mwai-messages-theme .hljs-meta .hljs-keyword, .mwai-messages-theme .hljs-section, .mwai-messages-theme .hljs-selector-tag {
  color: #0077cc;
}
.mwai-messages-theme .hljs-attribute {
  color: #aa3377;
}
.mwai-messages-theme .hljs-name, .mwai-messages-theme .hljs-number, .mwai-messages-theme .hljs-quote, .mwai-messages-theme .hljs-selector-id, .mwai-messages-theme .hljs-template-tag, .mwai-messages-theme .hljs-type {
  color: #c18401;
}
.mwai-messages-theme .hljs-selector-class {
  color: #0077cc;
}
.mwai-messages-theme .hljs-link, .mwai-messages-theme .hljs-regexp, .mwai-messages-theme .hljs-selector-attr, .mwai-messages-theme .hljs-string, .mwai-messages-theme .hljs-symbol, .mwai-messages-theme .hljs-template-variable, .mwai-messages-theme .hljs-variable {
  color: #689700;
}
.mwai-messages-theme .hljs-meta, .mwai-messages-theme .hljs-selector-pseudo {
  color: #0077cc;
}
.mwai-messages-theme .hljs-built_in, .mwai-messages-theme .hljs-literal, .mwai-messages-theme .hljs-title {
  color: #c18401;
}
.mwai-messages-theme .hljs-bullet, .mwai-messages-theme .hljs-code {
  color: #555;
}
.mwai-messages-theme .hljs-meta .hljs-string {
  color: #689700;
}
.mwai-messages-theme .hljs-deletion {
  color: #b71c1c;
}
.mwai-messages-theme .hljs-addition {
  color: #1b5e20;
}
.mwai-messages-theme .hljs-emphasis {
  font-style: italic;
}
.mwai-messages-theme .hljs-strong {
  font-weight: 700;
}
.mwai-messages-theme .mwai-reply-actions {
  position: absolute;
  border-radius: 5px;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  padding: 2px 2px;
  z-index: 100;
  background: var(--mwai-backgroundPrimaryColor);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
  z-index: 100;
}
.mwai-messages-theme .mwai-reply-actions .mwai-copy-button {
  fill: var(--mwai-fontColor);
  padding: 3px 5px;
  width: 24px;
  height: 24px;
  background: var(--mwai-backgroundPrimaryColor);
  cursor: pointer;
  border-radius: 5px;
}
.mwai-messages-theme .mwai-reply-actions .mwai-copy-button:hover {
  filter: brightness(1.2);
}
.mwai-messages-theme .mwai-reply-actions.mwai-hidden {
  opacity: 0;
}
.mwai-messages-theme .mwai-realtime {
  padding: var(--mwai-spacing);
}
.mwai-messages-theme .mwai-realtime .mwai-visualizer {
  display: flex;
  justify-content: center;
  align-items: center;
}
.mwai-messages-theme .mwai-realtime .mwai-visualizer hr {
  width: 100px;
  margin-right: var(--mwai-spacing);
  margin-left: var(--mwai-spacing);
  border: 1px solid var(--mwai-backgroundPrimaryColor);
}
.mwai-messages-theme .mwai-realtime .mwai-visualizer .mwai-animation {
  background: var(--mwai-backgroundPrimaryColor);
}
.mwai-messages-theme .mwai-realtime .mwai-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: var(--mwai-spacing);
}
.mwai-messages-theme .mwai-realtime .mwai-controls > * + * {
  margin-left: 10px;
}
.mwai-messages-theme .mwai-realtime .mwai-controls button {
  border-radius: 100%;
  width: 50px;
  height: 50px;
  margin: 5px;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--mwai-fontColor);
  border: 2px solid var(--mwai-backgroundPrimaryColor);
  background: none;
  cursor: pointer;
  transition: all 0.2s ease-out;
  min-width: inherit;
  max-width: inherit;
}
.mwai-messages-theme .mwai-realtime .mwai-controls button:hover:not(:disabled) {
  background: var(--mwai-backgroundPrimaryColor);
}
.mwai-messages-theme .mwai-realtime .mwai-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: none;
}
.mwai-messages-theme .mwai-realtime .mwai-controls button.mwai-active {
  border: 2px solid var(--mwai-fontColor);
}
.mwai-messages-theme .mwai-realtime .mwai-last-transcript {
  margin: var(--mwai-spacing);
  margin-top: 0;
  border: 2px solid var(--mwai-backgroundPrimaryColor);
  padding: calc(var(--mwai-spacing) / 2);
  border-radius: var(--mwai-borderRadius);
  display: flex;
  justify-content: center;
  font-size: 80%;
}
.mwai-messages-theme .mwai-realtime .mwai-statistics {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-row-gap: 10px;
  font-size: 14px;
}
.mwai-messages-theme .mwai-realtime .mwai-statistics div {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.mwai-messages-theme .mwai-realtime .mwai-statistics label {
  font-size: 11px;
  opacity: 0.5;
  text-transform: uppercase;
}
.mwai-messages-theme .mwai-realtime .mwai-options {
  margin-top: var(--mwai-spacing);
  display: flex;
  align-items: center;
}
.mwai-messages-theme .mwai-realtime .mwai-options .mwai-option {
  cursor: pointer;
  opacity: 0.5;
  margin-right: 2px;
}
.mwai-messages-theme .mwai-realtime .mwai-options .mwai-option.mwai-active {
  opacity: 1;
}
.mwai-messages-theme.mwai-discussions {
  border-radius: var(--mwai-borderRadius);
  background: var(--mwai-backgroundHeaderColor);
  overflow: hidden;
}
.mwai-messages-theme.mwai-discussions * {
  box-sizing: border-box;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion {
  display: flex;
  position: relative;
  padding-left: calc(var(--mwai-spacing) / 2);
  padding-right: calc(var(--mwai-spacing) / 2);
  padding-bottom: calc(var(--mwai-spacing) / 2);
  color: var(--mwai-conversationsTextColor);
  opacity: 0.65;
  align-items: center;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion .mwai-discussion-content {
  flex: 1;
  padding: 5px 10px;
  overflow: hidden;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion .mwai-discussion-title {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: var(--mwai-fontSize);
  margin-bottom: 4px;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion .mwai-discussion-info {
  display: flex;
  gap: 12px;
  font-size: calc(var(--mwai-fontSize) * 0.85);
  opacity: 0.7;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion .mwai-discussion-info .mwai-info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion .mwai-discussion-info .mwai-info-item svg {
  opacity: 0.6;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion .mwai-discussion-actions {
  position: absolute;
  top: 50%;
  right: calc(var(--mwai-spacing) / 2);
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.2s ease-out;
  z-index: 100;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion .mwai-discussion-actions .mwai-menu-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--mwai-conversationsTextColor);
}
.mwai-messages-theme.mwai-discussions .mwai-discussion.mwai-active {
  cursor: pointer;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion.mwai-active .mwai-discussion-content {
  background: var(--mwai-backgroundPrimaryColor);
  border-radius: var(--mwai-borderRadius);
  opacity: 1;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion:hover {
  cursor: pointer;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion:hover .mwai-discussion-content {
  background: var(--mwai-backgroundPrimaryColor);
  border-radius: var(--mwai-borderRadius);
  opacity: 1;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion:hover .mwai-discussion-actions {
  opacity: 1;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion:has(.mwai-context-menu) .mwai-discussion-actions {
  opacity: 1;
}
.mwai-messages-theme.mwai-discussions .mwai-discussion:first-child {
  margin-top: calc(var(--mwai-spacing) / 2);
}
.mwai-messages-theme.mwai-discussions .mwai-header {
  color: var(--mwai-headerButtonsColor);
  padding: var(--mwai-spacing);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}
.mwai-messages-theme.mwai-discussions .mwai-header button {
  background: var(--mwai-backgroundPrimaryColor);
  color: var(--mwai-fontColor);
  border: none;
  padding: 8px 16px;
  border-radius: var(--mwai-borderRadius);
  cursor: pointer;
  transition: all 0.2s ease-out;
}
.mwai-messages-theme.mwai-discussions .mwai-header button:hover:not(:disabled) {
  background: var(--mwai-iconTextBackgroundColor);
}
.mwai-messages-theme.mwai-discussions .mwai-header button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.mwai-messages-theme.mwai-discussions .mwai-header .mwai-refresh-btn {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.mwai-messages-theme.mwai-discussions .mwai-body {
  background: var(--mwai-conversationsBackgroundColor);
  list-style: none;
  padding: 0;
  margin: 0;
  position: relative;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  border-radius: 0;
  z-index: 1;
}
.mwai-messages-theme.mwai-discussions .mwai-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--mwai-conversationsBackgroundColor);
  opacity: 0.9;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.mwai-messages-theme.mwai-discussions .mwai-spinner {
  animation: spin 1s linear infinite;
  color: var(--mwai-fontColor);
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.mwai-messages-theme.mwai-discussions .mwai-pagination {
  background: var(--mwai-backgroundHeaderColor);
  padding: var(--mwai-spacing);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--mwai-backgroundPrimaryColor);
}
.mwai-messages-theme.mwai-discussions .mwai-pagination button {
  background: var(--mwai-backgroundPrimaryColor);
  color: var(--mwai-fontColor);
  border: none;
  padding: 8px 12px;
  border-radius: var(--mwai-borderRadius);
  cursor: pointer;
  transition: all 0.2s ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
}
.mwai-messages-theme.mwai-discussions .mwai-pagination button:hover:not(:disabled) {
  background: var(--mwai-iconTextBackgroundColor);
}
.mwai-messages-theme.mwai-discussions .mwai-pagination button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}
.mwai-messages-theme.mwai-discussions .mwai-pagination span {
  color: var(--mwai-headerButtonsColor);
  font-size: var(--mwai-fontSize);
  font-weight: 500;
}
.mwai-messages-theme.mwai-discussions .mwai-pagination .mwai-page-indicator {
  color: var(--mwai-headerButtonsColor);
  font-size: calc(var(--mwai-fontSize) * 0.85);
  font-weight: 400;
  opacity: 0.8;
}

.mwai-messages-theme .mwai-realtime .mwai-visualizer hr {
  border: 1px solid var(--mwai-backgroundAiSecondaryColor);
}
.mwai-messages-theme .mwai-realtime .mwai-visualizer .mwai-animation {
  background: var(--mwai-backgroundAiSecondaryColor);
}
.mwai-messages-theme .mwai-realtime .mwai-controls button {
  color: var(--mwai-backgroundPrimaryColor);
  background: var(--mwai-backgroundUserColor);
}
.mwai-messages-theme .mwai-realtime .mwai-controls button:hover {
  color: var(--mwai-backgroundPrimaryColor) !important;
  background: var(--mwai-backgroundUserColor) !important;
  opacity: 0.8;
}
.mwai-messages-theme .mwai-realtime .mwai-controls button[disabled] {
  color: var(--mwai-backgroundPrimaryColor) !important;
  background: var(--mwai-backgroundUserColor) !important;
  opacity: 0.5;
}
.mwai-messages-theme .mwai-reply-actions {
  top: 5px;
}
.mwai-messages-theme .mwai-reply-actions .mwai-copy-button {
  padding-top: 4px;
}
.mwai-messages-theme .mwai-reply-actions .mwai-copy-button:hover {
  fill: var(--mwai-backgroundPrimaryColor);
  background: var(--mwai-backgroundUserColor);
}

@media (max-width: 760px) {
  .mwai-messages-theme.mwai-window {
    width: calc(100% - 40px);
    z-index: 9999999999;
  }
  .mwai-messages-theme .mwai-input {
    flex-direction: column;
  }
  .mwai-messages-theme .mwai-input .mwai-input-text {
    width: 100%;
  }
  .mwai-messages-theme .mwai-input .mwai-input-submit {
    width: 100%;
    margin: 15px 0 0 0;
  }
}
