<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit84db5b27c255bfe02c1711de17b0450f
{
    public static $prefixesPsr0 = array (
        'P' => 
        array (
            'Parsedown' => 
            array (
                0 => __DIR__ . '/..' . '/erusev/parsedown',
            ),
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixesPsr0 = ComposerStaticInit84db5b27c255bfe02c1711de17b0450f::$prefixesPsr0;
            $loader->classMap = ComposerStaticInit84db5b27c255bfe02c1711de17b0450f::$classMap;

        }, null, ClassLoader::class);
    }
}
