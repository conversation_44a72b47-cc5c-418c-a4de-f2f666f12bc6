// out:  ../chatgpt.css
@import '_common.scss';

// Chatbot
.mwai-chatgpt-theme {
	--mwai-spacing: 15px;
	--mwai-fontSize: 15px;
	--mwai-lineHeight: 1.5;
	--mwai-borderRadius: 10px;
	--mwai-width: 460px;
	--mwai-maxHeight: 40vh;
	--mwai-iconTextColor: white;
	--mwai-iconTextBackgroundColor: #343541;
	--mwai-fontColor: #FFFFFF;
	--mwai-backgroundPrimaryColor: #454654;
	--mwai-backgroundHeaderColor: #343541;
	--mwai-bubbleColor: #343541;
	--mwai-headerButtonsColor: #FFFFFF;
	--mwai-conversationsBackgroundColor: #202123;
	--mwai-conversationsTextColor: #FFFFFF;
	--mwai-backgroundSecondaryColor: #343541;
	--mwai-errorBackgroundColor: #6d2f2a;
	--mwai-errorTextColor: #FFFFFF;
	
	* {
		box-sizing: border-box;
	}

	.mwai-body {
		background: var(--mwai-backgroundSecondaryColor);
		color: var(--mwai-fontColor);
		font-size: var(--mwai-fontSize);
		overflow: hidden;
		display: flex;
		flex-direction: column;
		border-radius: var(--mwai-borderRadius);
	}

	.mwai-shortcuts {
		display: flex;
		justify-content: center;
		margin: var(--mwai-spacing);

		.mwai-shortcut {
			margin-right: calc(var(--mwai-spacing) / 2);
			display: flex;

			&.mwai-success {
				color: #4caf50;
				border: 1px solid #4caf50;
			}

			&.mwai-danger {
				color: #f44336;
				border: 1px solid #f44336;
			}

			&.mwai-warning {
				color: #ff9800;
				border: 1px solid #ff9800;
			}

			&.mwai-info {
				color: #2196f3;
				border: 1px solid #2196f3;
			}

			.mwai-icon {
				margin-right: 5px;

				img {
					max-height: 16px;
					width: auto;
				}
			}
		}
	}

	.mwai-blocks {
		display: flex;
		flex-direction: column;
		padding: var(--mwai-spacing);

		.mwai-block {
			p:first-child {
				margin-top: 0;
			}
		}

		button {
			cursor: pointer;
		}
	}

	.mwai-conversation {
		overflow: auto;
	}

	.mwai-reply {
		display: flex;
		padding: var(--mwai-spacing);
		position: relative;
		line-height: var(--mwai-lineHeight);
		transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);

		&.mwai-fade-out {
			opacity: 0;
		}

		&.mwai-user {
			background: var(--mwai-backgroundSecondaryColor);
		}

		&.mwai-ai {
			background: var(--mwai-backgroundPrimaryColor);
		}

		.mwai-name {
			color: var(--mwai-fontColor);
			margin-right: 5px;

			.mwai-name-text {
				opacity: 0.50;
				white-space: nowrap;
			}

			.mwai-avatar {
				margin-right: 10px;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 5px;
				overflow: hidden;

				img {
					width: 32px;
					height: 32px;
					min-width: 32px;
					min-height: 32px;
				}

				&.mwai-svg img {
					width: 28px;
					height: 28px;
					min-width: 28px;
					min-height: 28px;
					filter: brightness(0) invert(1);
				}
			}
		}

		.mwai-text {
			flex: auto;
			font-size: var(--mwai-fontSize);
			line-height: var(--mwai-lineHeight);
			color: var(--mwai-fontColor);

			.mwai-image {
				display: block;
				max-width: 250px;
				height: auto;
				margin: 0 0 10px 0;
				border-radius: var(--mwai-borderRadius);
			}

			.mwai-filename {
				display: flex;
				text-decoration: none;
				border: 1px solid var(--mwai-backgroundPrimaryColor);
				border-radius: var(--mwai-borderRadius);
				padding: 5px 10px;
				margin-bottom: 10px;
			}

			* {
				font-size: var(--mwai-fontSize);
			}

			> span > *:first-child {
				margin-top: 0;
			}

			> span > *:last-child {
				margin-bottom: 0;
			}

			a {
				color: #2196f3;
			}

			h1, h2, h3, h4 {
				color: var(--mwai-fontColor);
			}

			h1 {
				font-size: 200%;
			}

			h2 {
				font-size: 160%;
			}

			h3 {
				font-size: 140%;
			}

			h4 {
				font-size: 120%;
			}

			p {
				code {
					background: var(--mwai-backgroundSecondaryColor);
					padding: 2px 6px;
					border-radius: 8px;
					font-size: 90%;
					font-family: system-ui;
				}
			}

			pre {
				color: var(--mwai-fontColor);
				border-radius: var(--mwai-borderRadius);
				padding: calc(var(--mwai-spacing) * 2 / 3) var(--mwai-spacing);
				break-after: auto;
				white-space: pre-wrap;
				font-size: 95%;
				max-width: 100%;
				width: 100%;
				font-family: system-ui;
				background: #{'hsl(0 0% 0% / 30%)'};

				code {
					padding: 0 !important;
					font-family: system-ui;
				}
			}

			ul, ol {
				padding: 0;
			}

			ol {
				margin: 0 0 0 20px;
			}

			table {
				width: 100%;
				border: 2px solid var(--mwai-backgroundSecondaryColor);
				border-collapse: collapse;
			}

			thead {
				background: var(--mwai-backgroundSecondaryColor);
			}

			tr, td {
				padding: 2px 5px;
			}

			td {
				border: 2px solid var(--mwai-backgroundSecondaryColor);
			}

			.mwai-typewriter {
				display: inline-block;

				> :first-child {
					margin-top: 0;
				}
			}

			>*:first-child {
				margin-top: 0;
			}

			>*:last-child {
				margin-bottom: 0;
			}
		}

		&.mwai-system {
			background: var(--mwai-errorBackgroundColor);
			color: var(--mwai-errorFontColor);

			.mwai-name {
				display: none;
			}
		}
	}

	.mwai-input {
		display: flex;
		padding: var(--mwai-spacing);
		border-top: 1px solid var(--mwai-backgroundPrimaryColor);

		.mwai-input-text {
			flex: auto;
			position: relative;
			background: var(--mwai-backgroundPrimaryColor);
			border-radius: var(--mwai-borderRadius);
			overflow: hidden;
			display: flex;
			padding: calc(var(--mwai-spacing) / 2);

			&.mwai-blocked {
				background: var(--mwai-errorBackgroundColor);
			}

			&.mwai-dragging {
				filter: brightness(1.2);
			}

			textarea {
				background: inherit;
				color: var(--mwai-fontColor);
				padding-left: calc(var(--mwai-spacing) / 2);
				flex: auto;
				border: none;
				font-size: var(--mwai-fontSize);
				line-height: var(--mwai-lineHeight);
				resize: none;
				font-family: inherit;
				margin: 0;
				overflow: hidden;
				min-height: inherit;

				&:focus {
					outline: none;
					box-shadow: none;
				}

				&::placeholder {
					color: var(--mwai-fontColor);
					opacity: 0.5;
				}
			}

			.mwai-microphone {
				@include microphone;
			}

			.mwai-file-upload-icon {
				$icon-size: 32px;
				background: url('icons/dark-icons.svg');
				background-size: 500%; // 5 icons in the row
				background-position: -0 * $icon-size -3 * $icon-size;
				width: $icon-size;
				height: $icon-size;
				z-index: 100;
				@include file-upload-icon-sprites;
			}
		}

		button {
			margin-left: var(--mwai-spacing);
		}
	}

	.mwai-compliance {
		opacity: 0.50;
		margin-top: calc(-1 * var(--mwai-spacing));
		padding: calc(var(--mwai-spacing) / 1.5) var(--mwai-spacing);
		font-size: smaller;
		color: var(--mwai-fontColor);
		text-align: left;
	}

	.mwai-gallery {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 5px;

		img {
			width: 100%;
		}
	}

	button {
		color: var(--mwai-fontColor);
		background: var(--mwai-backgroundSecondaryColor);
		border: 1px solid var(--mwai-backgroundPrimaryColor);
		padding: calc(var(--mwai-spacing) / 2) var(--mwai-spacing);
		min-width: 70px;
		border-radius: 5px;
		cursor: pointer;
		transition: all 0.2s ease-out;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: calc(var(--mwai-fontSize) * 0.9);
		position: relative;

		.mwai-timer {
			margin-left: 5px;
			margin-right: 5px;
			font-size: 11px;
		}

		&:hover {
			background: var(--mwai-backgroundPrimaryColor);
		}
	}

	button[disabled] {
		cursor: not-allowed;

		span {
			opacity: 0.5;
		}

		&.mwai-busy {
			span {
				display: none;
			}

			&:before {
				content: '';
				width: 18px;
				height: 18px;
				margin: auto;
				border: 3px solid transparent;
				border-top-color: var(--mwai-fontColor);
				border-radius: 50%;
				animation: mwai-button-spinner 1s ease infinite;
			}
		}
	}

	&.mwai-form-container {
		padding: var(--mwai-spacing);
		font-size: var(--mwai-fontSize);
		color: var(--mwai-fontColor);
		background: var(--mwai-backgroundSecondaryColor);
		border-radius: var(--mwai-borderRadius);

		fieldset {
			border: 0;
			margin: 0;
			padding: 0;
			display: flex;
			flex-direction: column;
			margin-bottom: 10px;
	
			input[type="text"], input[type="email"], input[type="tel"], input[type="url"], input[type="password"], input[type="number"], input[type="date"], input[type="datetime"], input[type="datetime-local"], input[type="month"], input[type="search"], input[type="time"], input[type="week"], select, textarea {
				padding: calc(var(--mwai-spacing) * 2 / 3) var(--mwai-spacing);
				border: 0;
				width: 100%;
				border-radius: var(--mwai-borderRadius);
				font-size: var(--mwai-fontSize);
				background: var(--mwai-backgroundPrimaryColor);
				color: var(--mwai-fontColor);
			}
	
			select {
				padding: calc(var(--mwai-spacing) * 2 / 3) var(--mwai-spacing);
				border: 0;
				width: 100%;
				border-radius: var(--mwai-borderRadius);
				font-size: var(--mwai-fontSize);
				background: var(--mwai-backgroundPrimaryColor);
				color: var(--mwai-fontColor);
			}
	
			textarea {
				padding: calc(var(--mwai-spacing) * 2 / 3) var(--mwai-spacing);
				border: 0;
				width: 100%;
				border-radius: var(--mwai-borderRadius);
				font-family: inherit;
				font-size: var(--mwai-fontSize);
				background: var(--mwai-backgroundPrimaryColor);
				color: var(--mwai-fontColor);
			}
	
			input[disabled], select[disabled], textarea[disabled] {
				opacity: 0.25;
			}
		}
	
		.mwai-form-submit, .mwai-form-reset {
	
			button {
				height: 45px;
				background: none;
				width: 100%;
				color: var(--mwai-fontColor);
				font-size: var(--mwai-fontSize);
				background-color: var(--mwai-backgroundSecondaryColor);
				border: 1px solid var(--mwai-backgroundPrimaryColor);
				border-radius: var(--mwai-borderRadius);
				cursor: pointer;
				transition: all 0.2s ease-out;
				position: relative;
	
				&:hover {
					background: var(--mwai-backgroundPrimaryColor);
				}
			}
	
			button[disabled] {
				span {
					opacity: 0.25;
				}
	
				&:hover {
					background: none;
					cursor: not-allowed;
				}
			}
	
			&.mwai-loading {
	
				button {
					
					span {
						opacity: 0;
					}
		
					&::after {
						content: '';
						position: absolute;
						width: 18px;
						height: 18px;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						margin: auto;
						border: 3px solid transparent;
						border-top-color: var(--mwai-fontColor);
						border-radius: 50%;
						animation: mwai-button-spinner 1s ease infinite;
					}
				}
			}
		}
	
		.mwai-form-output-container {
			
			.mwai-form-output {
				font-size: var(--mwai-fontSize);
				position: relative;
				margin-top: var(--mwai-spacing);
				padding: var(--mwai-spacing);
				border: 1px solid var(--mwai-backgroundPrimaryColor);
	
				&.mwai-error {
					background: var(--mwai-errorBackgroundColor);
					color: var(--mwai-errorFontColor);
				}
	
				& > *:first-child {
					margin-top: 0;
				}
	
				& > *:last-child {
					margin-bottom: 0;
				}
		
				img {
					max-width: 33%;
				}
	
				div {
					& > *:first-child {
						margin-top: 0;
					}
		
					& > *:last-child {
						margin-bottom: 0;
					}
				}
			}
	
			&.mwai-has-content {
				display: block;
			}
		}
	
		.wp-block-columns {
			margin: 0;
		}
	}

	// ChatbotChunks - Dark theme overrides
	.mwai-chunks {
		background: rgba(255, 255, 255, 0.05);
		border-top: 1px solid rgba(255, 255, 255, 0.1);	

		.mwai-chunks-header {
			color: #9ca3af;
		}

		.mwai-chunk {
			background: rgba(255, 255, 255, 0.08);
			border: 1px solid rgba(255, 255, 255, 0.1);

			.mwai-chunk-header {
				.mwai-chunk-time {
					color: #6b7280;
				}

				.mwai-chunk-data {
					color: #e5e7eb;
				}

				.mwai-chunk-expand {
					color: #6b7280;
				}
			}

			.mwai-chunk-details {
				background: rgba(0, 0, 0, 0.2);

				pre {
					color: #d1d5db;
				}
			}
		}
	}
}

// Discussions

// Common
.mwai-chatgpt-theme {
	@include common-styles;
	@include code-dark;
	@include reply-actions;
	@include realtime;
	@include discussions-styles;

	// ChatbotChunks - Dark theme overrides
	.mwai-chunks {
		background: rgba(255, 255, 255, 0.05);

		.mwai-chunks-header {
			color: #9ca3af;

			.mwai-chunks-toggle {
				border-color: rgba(255, 255, 255, 0.1);
				color: #9ca3af;

				&:hover {
					background: rgba(255, 255, 255, 0.05);
					color: #e5e7eb;
				}
			}
		}

		.mwai-chunk {
			background: rgba(255, 255, 255, 0.08);

			.mwai-chunk-header {
				.mwai-chunk-time {
					color: #e5e7eb; // Same as chunk-data
				}

				.mwai-chunk-data {
					color: #e5e7eb;
				}

				.mwai-chunk-expand {
					color: #e5e7eb; // Same as chunk-data
				}
			}

			.mwai-chunk-details {
				background: rgba(0, 0, 0, 0.2);

				pre {
					color: #d1d5db;
				}
			}
		}
	}
}

// Mobile
.mwai-chatgpt-theme {

	@media (max-width: 760px) {
		
		&.mwai-window {
			width: calc(100% - 40px);
			z-index: 9999999999;
		}

		.mwai-input {
			flex-direction: column;

			.mwai-input-submit {
				margin: 15px 0 0 0;
				height: 40px;
				width: inherit;
			}
		}

		.mwai-name {
			margin-right: 0;
			max-width: inherit;
		}
	}
}

// Context Menu Overrides for ChatGPT Theme
.mwai-context-menu-portal.mwai-chatgpt-theme {
	.mwai-context-menu {
		background: var(--mwai-backgroundSecondaryColor);
		border: 1px solid var(--mwai-backgroundPrimaryColor);
		color: var(--mwai-fontColor);

		.mwai-menu-item {
			&:hover {
				background-color: var(--mwai-backgroundPrimaryColor);
			}

			&.mwai-danger {
				color: var(--mwai-errorTextColor);

				&:hover {
					background-color: var(--mwai-errorBackgroundColor);
				}
			}
		}
	}
}


