// main: chatgpt.scss, messages.scss

@mixin discussions-styles {
	&.mwai-discussions {
		border-radius: var(--mwai-borderRadius);
		background: var(--mwai-backgroundHeaderColor);
		overflow: hidden;

		* {
			box-sizing: border-box;
		}

		.mwai-discussion {
			display: flex;
			position: relative;
			padding-left: calc(var(--mwai-spacing) / 2);
			padding-right: calc(var(--mwai-spacing) / 2);
			padding-bottom: calc(var(--mwai-spacing) / 2);
			color: var(--mwai-conversationsTextColor);
			opacity: 0.65;
			align-items: center;

			.mwai-discussion-content {
				flex: 1;
				padding: 5px 10px;
				overflow: hidden;
			}

			.mwai-discussion-title {
				display: block;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				font-size: var(--mwai-fontSize);
				margin-bottom: 4px;
			}

			.mwai-discussion-info {
				display: flex;
				gap: 12px;
				font-size: calc(var(--mwai-fontSize) * 0.85);
				opacity: 0.7;
				
				.mwai-info-item {
					display: flex;
					align-items: center;
					gap: 4px;
					
					svg {
						opacity: 0.6;
					}
				}
			}

			.mwai-discussion-actions {
				position: absolute;
				top: 50%;
				right: calc(var(--mwai-spacing) / 2);
				transform: translateY(-50%);
				opacity: 0;
				transition: opacity 0.2s ease-out;
				z-index: 100;
	
				.mwai-menu-icon {
					width: 28px;
					height: 28px;
					display: flex;
					align-items: center;
					justify-content: center;
					cursor: pointer;
					color: var(--mwai-conversationsTextColor);
				}

			}

			&.mwai-active {
				cursor: pointer;

				.mwai-discussion-content {
					background: var(--mwai-backgroundPrimaryColor);
					border-radius: var(--mwai-borderRadius);
					opacity: 1;
				}
			}
			
			&:hover {
				cursor: pointer;

				.mwai-discussion-content {
					background: var(--mwai-backgroundPrimaryColor);
					border-radius: var(--mwai-borderRadius);
					opacity: 1;
				}

				.mwai-discussion-actions {
					opacity: 1;
				}
			}

			// Keep actions visible when menu is open
			&:has(.mwai-context-menu) {
				.mwai-discussion-actions {
					opacity: 1;
				}
			}

			&:first-child {
				margin-top: calc(var(--mwai-spacing) / 2);
			}
		}

		.mwai-header {
			color: var(--mwai-headerButtonsColor);
			padding: var(--mwai-spacing);
			display: flex;
			justify-content: space-between;
			align-items: center;
			gap: 10px;

			button {
				background: var(--mwai-backgroundPrimaryColor);
				color: var(--mwai-fontColor);
				border: none;
				padding: 8px 16px;
				border-radius: var(--mwai-borderRadius);
				cursor: pointer;
				transition: all 0.2s ease-out;

				&:hover:not(:disabled) {
					background: var(--mwai-iconTextBackgroundColor);
				}

				&:disabled {
					opacity: 0.5;
					cursor: not-allowed;
				}
			}

			.mwai-refresh-btn {
				padding: 8px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.mwai-body {
			background: var(--mwai-conversationsBackgroundColor);
			list-style: none;
			padding: 0;
			margin: 0;
			position: relative;
			min-height: 200px;
			display: flex;
			flex-direction: column;
			border-radius: 0;
			z-index: 1;
		}

		.mwai-loading-overlay {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: var(--mwai-conversationsBackgroundColor);
			opacity: 0.9;
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 10;
		}

		.mwai-spinner {
			animation: spin 1s linear infinite;
			color: var(--mwai-fontColor);
		}

		@keyframes spin {
			from { transform: rotate(0deg); }
			to { transform: rotate(360deg); }
		}

		.mwai-pagination {
			background: var(--mwai-backgroundHeaderColor);
			padding: var(--mwai-spacing);
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-top: 1px solid var(--mwai-backgroundPrimaryColor);

			button {
				background: var(--mwai-backgroundPrimaryColor);
				color: var(--mwai-fontColor);
				border: none;
				padding: 8px 12px;
				border-radius: var(--mwai-borderRadius);
				cursor: pointer;
				transition: all 0.2s ease-out;
				display: flex;
				align-items: center;
				justify-content: center;

				&:hover:not(:disabled) {
					background: var(--mwai-iconTextBackgroundColor);
				}

				&:disabled {
					opacity: 0.3;
					cursor: not-allowed;
				}
			}

			span {
				color: var(--mwai-headerButtonsColor);
				font-size: var(--mwai-fontSize);
				font-weight: 500;
			}

			.mwai-page-indicator {
				color: var(--mwai-headerButtonsColor);
				font-size: calc(var(--mwai-fontSize) * 0.85);
				font-weight: 400;
				opacity: 0.8;
			}
		}
	}
}

@mixin file-upload-icon-sprites {
	$icon-size: 32px;

	&.mwai-idle-add {
		background-position: -1 * $icon-size -3 * $icon-size;
	}

	&.mwai-image-add {
		background-position: -1 * $icon-size -0 * $icon-size;
	}

	&.mwai-image-up {
		background-position: -2 * $icon-size -0 * $icon-size;
	}

	&.mwai-image-del {
		background-position: -3 * $icon-size -0 * $icon-size;
	}

	&.mwai-image-ok {
		background-position: -4 * $icon-size -0 * $icon-size;
	}

	&.mwai-document-add {
		background-position: -1 * $icon-size -2 * $icon-size;
	}

	&.mwai-document-up {
		background-position: -2 * $icon-size -2 * $icon-size;
	}

	&.mwai-document-del {
		background-position: -3 * $icon-size -2 * $icon-size;
	}

	&.mwai-document-ok {
		background-position: -4 * $icon-size -2 * $icon-size;
	}

	.mwai-file-upload-progress {
		position: absolute;
		font-size: 8px;
		width: 21px;
		top: 24px;
		left: 23px;
		overflow: hidden;
		text-align: center;
		font-weight: bold;
		color: white;
	}
}

@mixin microphone {

	display: flex;
	justify-content: center;
	align-items: center;

	svg {
		opacity: 0.5;
		filter: grayscale(100%);
		transition: opacity 0.3s ease-out;
		cursor: pointer;
	}

	&[active=true] svg {
		opacity: 1;
	}

	&[disabled] svg {
		opacity: 0;
		cursor: not-allowed;
	}
}

@mixin fullscreen-window {
  position: fixed;
  left: 0 !important;
  right: 0 !important; 
  bottom: 0 !important;
  top: 0 !important;
  width: 100%;
  height: 100%;
  max-height: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  z-index: 999999;
  background-color: var(--mwai-backgroundSecondaryColor);

	.mwai-header {
		border-radius: 0;
	} 

  .mwai-body {
    height: 100%;
    max-height: inherit;
		border-radius: 0;

    .mwai-conversation {
      flex: auto; 
      max-height: none;
    }
  }
}

@mixin common-styles {

	// Transitions

	&.mwai-transition, .mwai-transition {
		opacity: 0;
		transition: opacity 350ms ease-in-out;
	}

	&.mwai-transition-visible, .mwai-transition-visible {
		opacity: 1;
	}

	.mwai-text {
		overflow-wrap: anywhere;
		
		img {
			max-width: 100%;
		}

		div {
			p:first-child {
				margin-top: 0;
			}
			p:last-child {
				margin-bottom: 0;
			}
		}
	}

	// The icon when the chat window is closed.

	.mwai-trigger {
		position: absolute;
		right: 0;
		bottom: 0;
		transition: all 0.2s ease-out;
		z-index: 9999;
		display: flex;
		flex-direction: column;
		align-items: end;

		.mwai-icon-text-container {
			display: flex;
			flex-direction: column;
			align-items: flex-end;

			.mwai-icon-text {
				background: var(--mwai-iconTextBackgroundColor);
				color: var(--mwai-iconTextColor);
				box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.15);
				max-width: 200px;
				font-size: 13px;
				margin-bottom: 15px;
				padding: 10px 15px;
				border-radius: 8px;
			}

			.mwai-icon-text-close {
				color: var(--mwai-iconTextColor);
				background: var(--mwai-iconTextBackgroundColor);
				padding: 0;
				width: 24px;
				height: 24px;
				border-radius: 50%;
				display: none;
				justify-content: center;
				align-items: center;
				margin-bottom: 3px;
			}

			&:hover {
				cursor: pointer;

				.mwai-icon-text-close {
					display: flex;
					font-size: 12px;

					&:hover {
						filter: brightness(1.2);
					}
				}
			}

			@media (max-width: 760px) {
				.mwai-icon-text-close {
					display: flex;
				}
			}

		}

		.mwai-icon-container {

			.mwai-icon {
				filter: drop-shadow(0px 0px 15px rgba(0, 0, 0, 0.15));
				transition: all 0.2s ease-out;

				&:hover {
					cursor: pointer;
					transform: scale(1.05);
				}
			}
		}
	}

	// Handle the chat window and fullscreen.
	// Let's keep this common for all themes.

	&.mwai-window {
		position: fixed;
		right: 30px;
		bottom: 30px;
		width: var(--mwai-width);
		z-index: 9999;

		.mwai-header {
			display: none;
			justify-content: flex-end;
			align-items: center;
			border-radius: var(--mwai-borderRadius) var(--mwai-borderRadius) 0 0;
			background: var(--mwai-backgroundHeaderColor);

			.mwai-buttons {
				display: flex;
				align-items: center;

				.mwai-resize-button {
					justify-content: center;
					height: 32px;
					width: 22px;
					cursor: pointer;
					display: flex;
					justify-content: center;
					align-items: center;

					&:before {
						transition: all 0.2s ease-out;
						content: ' ';
						cursor: pointer;
						position: absolute;
						height: 13px;
						width: 13px;
						border: 1px solid var(--mwai-headerButtonsColor);
					}

					&:hover:before {
						width: 16px;
						height: 16px;
					}
				}

				.mwai-close-button {
					justify-content: center;
					height: 32px;
					width: 33px;
					cursor: pointer;
					border-radius: var(--mwai-borderRadius);

					&:before {
						transition: all 0.2s ease-out;
						transform: translate(16px, 5px) rotate(45deg);
					}
					&:after {
						transition: all 0.2s ease-out;
						transform: translate(16px, 5px) rotate(-45deg);
					}
				}

				.mwai-close-button:before, .mwai-close-button:after {
					content: ' ';
					cursor: pointer;
					position: absolute;
					height: 22px;
					width: 1px;
					background-color: var(--mwai-headerButtonsColor);
				}

				.mwai-close-button:hover {
					&:before {
						opacity: 1;
						transform: translate(16px, 5px) rotate(135deg);
					}
					&:after {
						opacity: 1;
						transform: translate(16px, 5px) rotate(45deg);
					}
				}

			}
		}

		.mwai-body {
			display: none;
			opacity: 0;
			max-height: var(--mwai-maxHeight);
			border-radius: 0 0 var(--mwai-borderRadius) var(--mwai-borderRadius);
		}

		&.mwai-bottom-left {
			bottom: 30px;
			right: inherit;
			left: 30px;

			.mwai-trigger {
				right: inherit;
				left: 0;
			}
		}

		&.mwai-top-right {
			top: 30px;
			bottom: inherit;
			right: 30px;

			.mwai-trigger {
				top: 0;
				bottom: inherit;
			}
		}

		&.mwai-top-left {
			top: 30px;
			bottom: inherit;
			right: inherit;
			left: 30px;

			.mwai-trigger {
				top: 0;
				bottom: inherit;
				right: inherit;
				left: 0;
			}
		}

		&.mwai-top-left, &.mwai-bottom-left {
			
			.mwai-trigger {
				align-items: flex-start;
			}
		}

		&.mwai-top-right, &.mwai-top-left {

			.mwai-trigger {
				flex-direction: column-reverse;

				.mwai-icon-text {
					margin-bottom: 0;
					margin-top: 15px;
				}
			}
		}
	}

	// Popup chat is fullscreen

	&.mwai-window.mwai-fullscreen {

		.mwai-header  {

			.mwai-buttons  {
				margin-bottom: 0px;

				.mwai-resize-button {
					&:before {
						width: 16px;
						height: 16px;
					}
			
					&:hover:before {
						width: 13px;
						height: 13px;
					}
				}
			}
		}
	}

	// Standard chat is fullscreen

	&.mwai-fullscreen:not(.mwai-window), &.mwai-fullscreen.mwai-window.mwai-open {
		@include fullscreen-window;
	}

	// Popup chat is open

	&.mwai-window.mwai-open {

		.mwai-header {
			display: flex;
		}

		.mwai-body {
			display: flex;
			transition: opacity 200ms ease-in-out 0s;
			opacity: 1;
		}

		.mwai-trigger {
			display: none;
		}
	}

	.mwai-error {
		margin: var(--mwai-spacing);
		color: white;
		background: rgba(180, 55, 55, 0.55);
		padding: var(--mwai-spacing);
		border-radius: var(--mwai-borderRadius);

		&:hover {
			cursor: pointer;
			background: rgba(180, 44, 44, 0.85);
		}
	}

	&.mwai-bubble .mwai-icon-container {
		background: var(--mwai-bubbleColor);
		width: 60px;
		height: 60px;
		border-radius: 100%;
		transition: all 0.2s ease-out;
		display: flex;
		justify-content: center;
		align-items: center;

		.mwai-icon {
			max-width: 50%;
			max-height: 50%;
			filter: none;

			&:hover {
				transform: none;
			}
		}

		.mwai-emoji {
			font-size: 30px !important;
		}

		&:hover {
			cursor: pointer;
			filter: brightness(1.1);
		}
	}

	@media (max-width: 760px) {
		&.mwai-window.mwai-open {
			@include fullscreen-window;
		
			.mwai-input {
				flex-direction: column;

				button {
					font-size: 16px;
					margin-left: 0;
					width: 100%;
				}
				
				.mwai-input-text {
					//margin-bottom: var(--mwai-spacing);
					width: 100%;

					// This is to avoid iOS from zooming in when focusing on an input field.
					input, textarea {
						font-size: 16px;
					}
				}
			}
	
			.mwai-body {
				display: flex;
				transition: opacity 200ms ease-in-out 0s;
				opacity: 1;
				height: 100%;
				max-height: inherit;
	
				.mwai-conversation {
					flex: auto;
					max-height: none;
				}
			}

			.mwai-resize-button {
				display: none !important;
			}
	
			.mwai-trigger {
				display: none;
			}
		}
	}

	// Keyframes for buttons

	@keyframes mwai-button-spinner {

		from {
			transform: rotate(0turn);
		}
		to {
			transform: rotate(1turn);
		}
	}

	// Safari-specific fix for spinner cleanup
	button:not(.mwai-busy):before {
		content: none !important;
		display: none !important;
		animation: none !important;
	}

	// WordPress Admin Bar

	.admin-bar .mwai-fullscreen:not(.mwai-window),
	.admin-bar .mwai-fullscreen.mwai-window.mwai-open {
		top: 32px;
	}
}

@mixin code-dark {

  pre code.hljs {
    display: block;
    overflow-x: auto;
    padding: 1em;
  }

  code.hljs {
    padding: 3px 5px;
  }

  .hljs {
    color: #fff;

    &-subst {
      color: #fff;
    }

    &-comment {
      color: #999;
    }

    &-attr,
    &-doctag,
    &-keyword,
    &-meta .hljs-keyword,
    &-section,
    &-selector-tag {
      color: #88aece;
    }

    &-attribute {
      color: #c59bc1;
    }

    &-name,
    &-number,
    &-quote,
    &-selector-id,
    &-template-tag,
    &-type {
      color: #f08d49;
    }

    &-selector-class {
      color: #88aece;
    }

    &-link,
    &-regexp,
    &-selector-attr,
    &-string,
    &-symbol,
    &-template-variable,
    &-variable {
      color: #b5bd68;
    }

    &-meta,
    &-selector-pseudo {
      color: #88aece;
    }

    &-built_in,
    &-literal,
    &-title {
      color: #f08d49;
    }

    &-bullet,
    &-code {
      color: #ccc;
    }

    &-meta .hljs-string {
      color: #b5bd68;
    }

    &-deletion {
      color: #de7176;
    }

    &-addition {
      color: #76c490;
    }

    &-emphasis {
      font-style: italic;
    }

    &-strong {
      font-weight: 700;
    }
  }
}

@mixin code-light {
  
  pre code.hljs {
    display: block;
    overflow-x: auto;
    padding: 1em;
  }

  code.hljs {
    padding: 3px 5px;
  }

  .hljs {
    color: #333;
    background: #f0f0f0;

    &-subst {
      color: #333;
    }

    &-comment {
      color: #888;
    }

    &-attr,
    &-doctag,
    &-keyword,
    &-meta .hljs-keyword,
    &-section,
    &-selector-tag {
      color: #0077cc;
    }

    &-attribute {
      color: #aa3377;
    }

    &-name,
    &-number,
    &-quote,
    &-selector-id,
    &-template-tag,
    &-type {
      color: #c18401;
    }

    &-selector-class {
      color: #0077cc;
    }

    &-link,
    &-regexp,
    &-selector-attr,
    &-string,
    &-symbol,
    &-template-variable,
    &-variable {
      color: #689700;
    }

    &-meta,
    &-selector-pseudo {
      color: #0077cc;
    }

    &-built_in,
    &-literal,
    &-title {
      color: #c18401;
    }

    &-bullet,
    &-code {
      color: #555;
    }

    &-meta .hljs-string {
      color: #689700;
    }

    &-deletion {
      color: #b71c1c;
    }

    &-addition {
      color: #1b5e20;
    }

    &-emphasis {
      font-style: italic;
    }

    &-strong {
      font-weight: 700;
    }
  }
}

@mixin reply-actions {
	.mwai-reply-actions {
		position: absolute;
		border-radius: 5px;
		top: 10px;
		right: 10px;
		display: flex;
		align-items: center;
		padding: 2px 2px;
		z-index: 100;
		background: var(--mwai-backgroundPrimaryColor);
		box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
		z-index: 100;

		.mwai-copy-button {
			fill: var(--mwai-fontColor);
			padding: 3px 5px;
			width: 24px;
			height: 24px;
			background: var(--mwai-backgroundPrimaryColor);
			cursor: pointer;
			border-radius: 5px;

			&:hover {
				filter: brightness(1.2);
			}
		}

		&.mwai-hidden {
			opacity: 0;
		}
	}
}

@mixin realtime {
	.mwai-realtime {
		padding: var(--mwai-spacing);

		.mwai-visualizer {
			display: flex;
			justify-content: center;
			align-items: center;

			hr {
				width: 100px;
				margin-right: var(--mwai-spacing);
				margin-left: var(--mwai-spacing);
				border: 1px solid var(--mwai-backgroundPrimaryColor);
			}

			.mwai-animation {
				background: var(--mwai-backgroundPrimaryColor);
			}
		}

		.mwai-controls {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-bottom: var(--mwai-spacing);

			> * + * {
				margin-left: 10px;
			}

			button {
				border-radius: 100%;
				width: 50px;
				height: 50px;
				margin: 5px;
				padding: 5px;
				display: flex;
				align-items: center;
				justify-content: center;
				color: var(--mwai-fontColor);
				border: 2px solid var(--mwai-backgroundPrimaryColor);
				background: none;
				cursor: pointer;
				transition: all 0.2s ease-out;
				min-width: inherit;
				max-width: inherit;

				&:hover:not(:disabled) {
					background: var(--mwai-backgroundPrimaryColor);
				}

				&:disabled {
					opacity: 0.5;
					cursor: not-allowed;
					background: none;
				}

				&.mwai-active {
					border: 2px solid var(--mwai-fontColor);
				}
			}
		}

		.mwai-last-transcript {
			margin: var(--mwai-spacing);
			margin-top: 0;
			border: 2px solid var(--mwai-backgroundPrimaryColor); 
			padding: calc(var(--mwai-spacing) / 2);
			border-radius: var(--mwai-borderRadius);
			display: flex;
			justify-content: center;
			font-size: 80%;
		}

		.mwai-statistics {
			display: grid;
			grid-template-columns: 1fr 1fr 1fr;
			grid-row-gap: 10px;
			font-size: 14px;

			div {
				display: flex;
    		flex-direction: column;
    		align-items: center;
			}

			label {
				font-size: 11px;
				opacity: 0.5;
				text-transform: uppercase;
			}
		}

		.mwai-options {
			margin-top: var(--mwai-spacing);
			display: flex;
			align-items: center;

			.mwai-option {
				cursor: pointer;
				opacity: 0.5;
				margin-right: 2px;

				&.mwai-active {
					opacity: 1;
				}
			}
		}
	}
}

// Context Menu Portal (appears at document body level)
.mwai-context-menu-portal {
	.mwai-context-menu {
		background: var(--mwai-backgroundHeaderColor);
		border: 1px solid rgba(0, 0, 0, 0.1);
		border-radius: var(--mwai-borderRadius);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		font-size: 13px;
		color: var(--mwai-fontColor);

		.mwai-menu-item {
			display: flex;
			align-items: center;
			gap: 8px;
			padding: 8px 12px;
			cursor: pointer;
			transition: background-color 0.2s;

			&:hover {
				background-color: rgba(0, 0, 0, 0.05);
			}

			&.mwai-danger {
				color: #dc3545;

				&:hover {
					background-color: rgba(220, 53, 69, 0.1);
				}
			}

			svg {
				flex-shrink: 0;
			}
		}
	}
}


// ChatbotChunks - Debug Stream Events Display
.mwai-chunks {
	padding: 8px;
	background: rgba(0, 0, 0, 0.03);
	font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
	font-size: 11px;
	border-top: 1px solid rgba(0, 0, 0, 0.06);

	&.mwai-chunks-collapsed {
		.mwai-chunks-header {
			margin-bottom: 0 !important;
		}
	}

	.mwai-chunks-header {
		display: flex;
		align-items: center;
		gap: 6px;
		margin-bottom: 8px;
		color: #6b7280;
		font-size: 10px;
		text-transform: uppercase;
		letter-spacing: 0.05em;

		.mwai-chunks-title {
			flex: 1; // This pushes the buttons to the right
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.mwai-chunks-status {
			margin-left: 4px;
			font-weight: 500;
		}

		.mwai-chunks-toggle {
			background: none;
			border: 1px solid rgba(0, 0, 0, 0.1);
			border-radius: 3px;
			padding: 2px;
			width: 30px;
			height: 20px;
			cursor: pointer;
			color: #6b7280;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.2s ease;
			margin-left: 4px;

			&:hover {
				background: rgba(0, 0, 0, 0.05);
				color: #374151;
			}
		}
	}

	.mwai-chunk {
		margin-bottom: 4px;
		padding: 6px 8px;
		background: white;
		border-radius: 4px;
		border: 1px solid rgba(0, 0, 0, 0.06);
		transition: all 0.2s ease;

		.mwai-chunk-header {
			display: flex;
			align-items: center;
			gap: 8px;
			cursor: pointer;
			user-select: none;

			.mwai-chunk-time {
				color: #9ca3af;
				font-size: 10px;
				font-variant-numeric: tabular-nums;
			}

			.mwai-chunk-type {
				display: flex;
				align-items: center;
				gap: 4px;
				padding: 2px 6px;
				border-radius: 3px;
				font-size: 10px;
				font-weight: 500;
				color: white;
			}

			.mwai-chunk-data {
				flex: 1;
				color: #374151;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.mwai-chunk-expand {
				color: #9ca3af;
				transition: transform 0.2s ease;
			}
		}

		.mwai-chunk-details {
			margin-top: 8px;
			padding: 8px;
			background: rgba(0, 0, 0, 0.02);
			border-radius: 3px;
			overflow-x: auto;

			pre {
				margin: 0;
				white-space: pre-wrap;
				word-break: break-word;
				color: #4b5563;
			}
		}
	}
}