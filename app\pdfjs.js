"use strict";(self.wpJsonMwai=self.wpJsonMwai||[]).push([[854],{1100:(t,e,i)=>{i.r(e),i.d(e,{AbortException:()=>tt,AnnotationEditorLayer:()=>on,AnnotationEditorParamsType:()=>f,AnnotationEditorType:()=>m,AnnotationEditorUIManager:()=>$t,AnnotationLayer:()=>Ns,AnnotationMode:()=>g,AnnotationType:()=>E,ColorPicker:()=>Ws,DOMSVGFactory:()=>as,DrawLayer:()=>ln,FeatureTest:()=>st,GlobalWorkerOptions:()=>li,ImageKind:()=>S,InvalidPDFException:()=>Q,MathClamp:()=>ct,OPS:()=>D,OutputScale:()=>Dt,PDFDataRangeTransport:()=>Gi,PDFDateString:()=>Ct,PDFWorker:()=>qi,PasswordResponses:()=>O,PermissionFlag:()=>b,PixelsPerInch:()=>gt,RenderingCancelledException:()=>bt,ResponseException:()=>J,SignatureExtractor:()=>en,SupportedImageMimeTypes:()=>Rt,TextLayer:()=>Fi,TouchManager:()=>jt,Util:()=>at,VerbosityLevel:()=>k,XfaLayer:()=>rs,build:()=>ts,createValidAbsoluteUrl:()=>V,fetchData:()=>mt,getDocument:()=>Bi,getFilenameFromUrl:()=>wt,getPdfFilenameFromUrl:()=>yt,getUuid:()=>ht,getXfaPageViewport:()=>Tt,isDataScheme:()=>vt,isPdfFile:()=>At,isValidExplicitDest:()=>Ui,noContextMenu:()=>St,normalizeUnicode:()=>lt,setLayerDimensions:()=>kt,shadow:()=>q,stopEvent:()=>Et,updateUrlHash:()=>W,version:()=>Zi});const s=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type),n=[.001,0,0,.001,0,0],a=1.35,r=1,o=2,l=4,h=16,d=32,c=64,u=128,p=256,g={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},m={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15,SIGNATURE:101},f={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},b={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},v=0,A=1,w=2,y=3,_=3,x=4,S={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},E={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},C=1,T=2,M=3,P=4,I=5,k={ERRORS:0,WARNINGS:1,INFOS:5},D={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93,rawFillPath:94},R=0,L=1,F=2,N=3,O={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let B=k.WARNINGS;function H(t){Number.isInteger(t)&&(B=t)}function z(){return B}function U(t){B>=k.INFOS&&console.log(`Info: ${t}`)}function $(t){B>=k.WARNINGS&&console.log(`Warning: ${t}`)}function G(t){throw new Error(t)}function j(t,e){t||G(e)}function V(t,e=null,i=null){if(!t)return null;if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=decodeURIComponent(escape(t))}catch{}}const s=e?URL.parse(t,e):URL.parse(t);return function(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(s)?s:null}function W(t,e,i=!1){const s=URL.parse(t);return s?(s.hash=e,s.href):i&&V(t,"http://example.com")?t.split("#",1)[0]+""+(e?`#${e}`:""):""}function q(t,e,i,s=!1){return Object.defineProperty(t,e,{value:i,enumerable:!s,configurable:!0,writable:!1}),i}const X=function(){function t(t,e){this.message=t,this.name=e}return t.prototype=new Error,t.constructor=t,t}();class K extends X{constructor(t,e){super(t,"PasswordException"),this.code=e}}class Y extends X{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class Q extends X{constructor(t){super(t,"InvalidPDFException")}}class J extends X{constructor(t,e,i){super(t,"ResponseException"),this.status=e,this.missing=i}}class Z extends X{constructor(t){super(t,"FormatError")}}class tt extends X{constructor(t){super(t,"AbortException")}}function et(t){"object"==typeof t&&void 0!==t?.length||G("Invalid argument for bytesToString");const e=t.length,i=8192;if(e<i)return String.fromCharCode.apply(null,t);const s=[];for(let n=0;n<e;n+=i){const a=Math.min(n+i,e),r=t.subarray(n,a);s.push(String.fromCharCode.apply(null,r))}return s.join("")}function it(t){"string"!=typeof t&&G("Invalid argument for stringToBytes");const e=t.length,i=new Uint8Array(e);for(let s=0;s<e;++s)i[s]=255&t.charCodeAt(s);return i}class st{static get isLittleEndian(){return q(this,"isLittleEndian",function(){const t=new Uint8Array(4);return t[0]=1,1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return q(this,"isEvalSupported",function(){try{return new Function(""),!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return q(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get isImageDecoderSupported(){return q(this,"isImageDecoderSupported","undefined"!=typeof ImageDecoder)}static get platform(){if("undefined"!=typeof navigator&&"string"==typeof navigator?.platform&&"string"==typeof navigator?.userAgent){const{platform:t,userAgent:e}=navigator;return q(this,"platform",{isAndroid:e.includes("Android"),isLinux:t.includes("Linux"),isMac:t.includes("Mac"),isWindows:t.includes("Win"),isFirefox:e.includes("Firefox")})}return q(this,"platform",{isAndroid:!1,isLinux:!1,isMac:!1,isWindows:!1,isFirefox:!1})}static get isCSSRoundSupported(){return q(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const nt=Array.from(Array(256).keys(),(t=>t.toString(16).padStart(2,"0")));class at{static makeHexColor(t,e,i){return`#${nt[t]}${nt[e]}${nt[i]}`}static scaleMinMax(t,e){let i;t[0]?(t[0]<0&&(i=e[0],e[0]=e[2],e[2]=i),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(i=e[1],e[1]=e[3],e[3]=i),e[1]*=t[3],e[3]*=t[3]):(i=e[0],e[0]=e[1],e[1]=i,i=e[2],e[2]=e[3],e[3]=i,t[1]<0&&(i=e[1],e[1]=e[3],e[3]=i),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(i=e[0],e[0]=e[2],e[2]=i),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e,i=0){const s=t[i],n=t[i+1];t[i]=s*e[0]+n*e[2]+e[4],t[i+1]=s*e[1]+n*e[3]+e[5]}static applyTransformToBezier(t,e,i=0){const s=e[0],n=e[1],a=e[2],r=e[3],o=e[4],l=e[5];for(let e=0;e<6;e+=2){const h=t[i+e],d=t[i+e+1];t[i+e]=h*s+d*a+o,t[i+e+1]=h*n+d*r+l}}static applyInverseTransform(t,e){const i=t[0],s=t[1],n=e[0]*e[3]-e[1]*e[2];t[0]=(i*e[3]-s*e[2]+e[2]*e[5]-e[4]*e[3])/n,t[1]=(-i*e[1]+s*e[0]+e[4]*e[1]-e[5]*e[0])/n}static axialAlignedBoundingBox(t,e,i){const s=e[0],n=e[1],a=e[2],r=e[3],o=e[4],l=e[5],h=t[0],d=t[1],c=t[2],u=t[3];let p=s*h+o,g=p,m=s*c+o,f=m,b=r*d+l,v=b,A=r*u+l,w=A;if(0!==n||0!==a){const t=n*h,e=n*c,i=a*d,s=a*u;p+=i,f+=i,m+=s,g+=s,b+=t,w+=t,A+=e,v+=e}i[0]=Math.min(i[0],p,m,g,f),i[1]=Math.min(i[1],b,A,v,w),i[2]=Math.max(i[2],p,m,g,f),i[3]=Math.max(i[3],b,A,v,w)}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t,e){const i=t[0],s=t[1],n=t[2],a=t[3],r=i**2+s**2,o=i*n+s*a,l=n**2+a**2,h=(r+l)/2,d=Math.sqrt(h**2-(r*l-o**2));e[0]=Math.sqrt(h+d||1),e[1]=Math.sqrt(h-d||1)}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),s=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>s)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>a?null:[i,n,s,a]}static pointBoundingBox(t,e,i){i[0]=Math.min(i[0],t),i[1]=Math.min(i[1],e),i[2]=Math.max(i[2],t),i[3]=Math.max(i[3],e)}static rectBoundingBox(t,e,i,s,n){n[0]=Math.min(n[0],t,i),n[1]=Math.min(n[1],e,s),n[2]=Math.max(n[2],t,i),n[3]=Math.max(n[3],e,s)}static#t(t,e,i,s,n,a,r,o,l,h){if(l<=0||l>=1)return;const d=1-l,c=l*l,u=c*l,p=d*(d*(d*t+3*l*e)+3*c*i)+u*s,g=d*(d*(d*n+3*l*a)+3*c*r)+u*o;h[0]=Math.min(h[0],p),h[1]=Math.min(h[1],g),h[2]=Math.max(h[2],p),h[3]=Math.max(h[3],g)}static#e(t,e,i,s,n,a,r,o,l,h,d,c){if(Math.abs(l)<1e-12)return void(Math.abs(h)>=1e-12&&this.#t(t,e,i,s,n,a,r,o,-d/h,c));const u=h**2-4*d*l;if(u<0)return;const p=Math.sqrt(u),g=2*l;this.#t(t,e,i,s,n,a,r,o,(-h+p)/g,c),this.#t(t,e,i,s,n,a,r,o,(-h-p)/g,c)}static bezierBoundingBox(t,e,i,s,n,a,r,o,l){l[0]=Math.min(l[0],t,r),l[1]=Math.min(l[1],e,o),l[2]=Math.max(l[2],t,r),l[3]=Math.max(l[3],e,o),this.#e(t,i,n,r,e,s,a,o,3*(3*(i-n)-t+r),6*(t-2*i+n),3*(i-t),l),this.#e(t,i,n,r,e,s,a,o,3*(3*(s-a)-e+o),6*(e-2*s+a),3*(s-e),l)}}let rt=null,ot=null;function lt(t){return rt||(rt=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,ot=new Map([["ﬅ","ſt"]])),t.replaceAll(rt,((t,e,i)=>e?e.normalize("NFKC"):ot.get(i)))}function ht(){if("function"==typeof crypto.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);return crypto.getRandomValues(t),et(t)}const dt="pdfjs_internal_id_";function ct(t,e,i){return Math.min(Math.max(t,e),i)}function ut(t){return Uint8Array.prototype.toBase64?t.toBase64():btoa(et(t))}"function"!=typeof Promise.try&&(Promise.try=function(t,...e){return new Promise((i=>{i(t(...e))}))}),"function"!=typeof Math.sumPrecise&&(Math.sumPrecise=function(t){return t.reduce(((t,e)=>t+e),0)});const pt="http://www.w3.org/2000/svg";class gt{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function mt(t,e="text"){if(xt(t,document.baseURI)){const i=await fetch(t);if(!i.ok)throw new Error(i.statusText);switch(e){case"arraybuffer":return i.arrayBuffer();case"blob":return i.blob();case"json":return i.json()}return i.text()}return new Promise(((i,s)=>{const n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType=e,n.onreadystatechange=()=>{if(n.readyState===XMLHttpRequest.DONE)if(200!==n.status&&0!==n.status)s(new Error(n.statusText));else{switch(e){case"arraybuffer":case"blob":case"json":return void i(n.response)}i(n.responseText)}},n.send(null)}))}class ft{constructor({viewBox:t,userUnit:e,scale:i,rotation:s,offsetX:n=0,offsetY:a=0,dontFlip:r=!1}){this.viewBox=t,this.userUnit=e,this.scale=i,this.rotation=s,this.offsetX=n,this.offsetY=a,i*=e;const o=(t[2]+t[0])/2,l=(t[3]+t[1])/2;let h,d,c,u,p,g,m,f;switch((s%=360)<0&&(s+=360),s){case 180:h=-1,d=0,c=0,u=1;break;case 90:h=0,d=1,c=1,u=0;break;case 270:h=0,d=-1,c=-1,u=0;break;case 0:h=1,d=0,c=0,u=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}r&&(c=-c,u=-u),0===h?(p=Math.abs(l-t[1])*i+n,g=Math.abs(o-t[0])*i+a,m=(t[3]-t[1])*i,f=(t[2]-t[0])*i):(p=Math.abs(o-t[0])*i+n,g=Math.abs(l-t[1])*i+a,m=(t[2]-t[0])*i,f=(t[3]-t[1])*i),this.transform=[h*i,d*i,c*i,u*i,p-h*i*o-c*i*l,g-d*i*o-u*i*l],this.width=m,this.height=f}get rawDims(){const t=this.viewBox;return q(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:s=this.offsetY,dontFlip:n=!1}={}){return new ft({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}convertToViewportPoint(t,e){const i=[t,e];return at.applyTransform(i,this.transform),i}convertToViewportRectangle(t){const e=[t[0],t[1]];at.applyTransform(e,this.transform);const i=[t[2],t[3]];return at.applyTransform(i,this.transform),[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){const i=[t,e];return at.applyInverseTransform(i,this.transform),i}}class bt extends X{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function vt(t){const e=t.length;let i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}function At(t){return"string"==typeof t&&/\.pdf$/i.test(t)}function wt(t){return[t]=t.split(/[#?]/,1),t.substring(t.lastIndexOf("/")+1)}function yt(t,e="document.pdf"){if("string"!=typeof t)return e;if(vt(t))return $('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),e;const i=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,s=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let n=i.exec(s[1])||i.exec(s[2])||i.exec(s[3]);if(n&&(n=n[0],n.includes("%")))try{n=i.exec(decodeURIComponent(n))[0]}catch{}return n||e}class _t{started=Object.create(null);times=[];time(t){t in this.started&&$(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||$(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:t}of this.times)e=Math.max(t.length,e);for(const{name:i,start:s,end:n}of this.times)t.push(`${i.padEnd(e)} ${n-s}ms\n`);return t.join("")}}function xt(t,e){const i=e?URL.parse(t,e):URL.parse(t);return"http:"===i?.protocol||"https:"===i?.protocol}function St(t){t.preventDefault()}function Et(t){t.preventDefault(),t.stopPropagation()}class Ct{static#i;static toDateObject(t){if(!t||"string"!=typeof t)return null;this.#i||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=this.#i.exec(t);if(!e)return null;const i=parseInt(e[1],10);let s=parseInt(e[2],10);s=s>=1&&s<=12?s-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let r=parseInt(e[5],10);r=r>=0&&r<=59?r:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const l=e[7]||"Z";let h=parseInt(e[8],10);h=h>=0&&h<=23?h:0;let d=parseInt(e[9],10)||0;return d=d>=0&&d<=59?d:0,"-"===l?(a+=h,r+=d):"+"===l&&(a-=h,r-=d),new Date(Date.UTC(i,s,n,a,r,o))}}function Tt(t,{scale:e=1,rotation:i=0}){const{width:s,height:n}=t.attributes.style,a=[0,0,parseInt(s),parseInt(n)];return new ft({viewBox:a,userUnit:1,scale:e,rotation:i})}function Mt(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}return t.startsWith("rgb(")?t.slice(4,-1).split(",").map((t=>parseInt(t))):t.startsWith("rgba(")?t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3):($(`Not a valid color format: "${t}"`),[0,0,0])}function Pt(t){const{a:e,b:i,c:s,d:n,e:a,f:r}=t.getTransform();return[e,i,s,n,a,r]}function It(t){const{a:e,b:i,c:s,d:n,e:a,f:r}=t.getTransform().invertSelf();return[e,i,s,n,a,r]}function kt(t,e,i=!1,s=!0){if(e instanceof ft){const{pageWidth:s,pageHeight:n}=e.rawDims,{style:a}=t,r=st.isCSSRoundSupported,o=`var(--total-scale-factor) * ${s}px`,l=`var(--total-scale-factor) * ${n}px`,h=r?`round(down, ${o}, var(--scale-round-x))`:`calc(${o})`,d=r?`round(down, ${l}, var(--scale-round-y))`:`calc(${l})`;i&&e.rotation%180!=0?(a.width=d,a.height=h):(a.width=h,a.height=d)}s&&t.setAttribute("data-main-rotation",e.rotation)}class Dt{constructor(){const{pixelRatio:t}=Dt;this.sx=t,this.sy=t}get scaled(){return 1!==this.sx||1!==this.sy}get symmetric(){return this.sx===this.sy}limitCanvas(t,e,i,s){let n=1/0,a=1/0,r=1/0;i>0&&(n=Math.sqrt(i/(t*e))),-1!==s&&(a=s/t,r=s/e);const o=Math.min(n,a,r);return(this.sx>o||this.sy>o)&&(this.sx=o,this.sy=o,!0)}static get pixelRatio(){return globalThis.devicePixelRatio||1}}const Rt=["image/apng","image/avif","image/bmp","image/gif","image/jpeg","image/png","image/svg+xml","image/webp","image/x-icon"];class Lt{#s=null;#n=null;#a;#r=null;#o=null;#l=null;static#h=null;constructor(t){this.#a=t,Lt.#h||=Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button",signature:"pdfjs-editor-remove-signature-button"})}render(){const t=this.#s=document.createElement("div");t.classList.add("editToolbar","hidden"),t.setAttribute("role","toolbar");const e=this.#a._uiManager._signal;t.addEventListener("contextmenu",St,{signal:e}),t.addEventListener("pointerdown",Lt.#d,{signal:e});const i=this.#r=document.createElement("div");i.className="buttons",t.append(i);const s=this.#a.toolbarPosition;if(s){const{style:e}=t,i="ltr"===this.#a._uiManager.direction?1-s[0]:s[0];e.insetInlineEnd=100*i+"%",e.top=`calc(${100*s[1]}% + var(--editor-toolbar-vert-offset))`}return this.#c(),t}get div(){return this.#s}static#d(t){t.stopPropagation()}#u(t){this.#a._focusEventsAllowed=!1,Et(t)}#p(t){this.#a._focusEventsAllowed=!0,Et(t)}#g(t){const e=this.#a._uiManager._signal;t.addEventListener("focusin",this.#u.bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",this.#p.bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",St,{signal:e})}hide(){this.#s.classList.add("hidden"),this.#n?.hideDropdown()}show(){this.#s.classList.remove("hidden"),this.#o?.shown()}#c(){const{editorType:t,_uiManager:e}=this.#a,i=document.createElement("button");i.className="delete",i.tabIndex=0,i.setAttribute("data-l10n-id",Lt.#h[t]),this.#g(i),i.addEventListener("click",(t=>{e.delete()}),{signal:e._signal}),this.#r.append(i)}get#m(){const t=document.createElement("div");return t.className="divider",t}async addAltText(t){const e=await t.render();this.#g(e),this.#r.prepend(e,this.#m),this.#o=t}addColorPicker(t){this.#n=t;const e=t.renderButton();this.#g(e),this.#r.prepend(e,this.#m)}async addEditSignatureButton(t){const e=this.#l=await t.renderEditButton(this.#a);this.#g(e),this.#r.prepend(e,this.#m)}updateEditSignatureButton(t){this.#l&&(this.#l.title=t)}remove(){this.#s.remove(),this.#n?.destroy(),this.#n=null}}class Ft{#r=null;#s=null;#f;constructor(t){this.#f=t}#b(){const t=this.#s=document.createElement("div");t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",St,{signal:this.#f._signal});const e=this.#r=document.createElement("div");return e.className="buttons",t.append(e),this.#v(),t}#A(t,e){let i=0,s=0;for(const n of t){const t=n.y+n.height;if(t<i)continue;const a=n.x+(e?n.width:0);t>i?(s=a,i=t):e?a>s&&(s=a):a<s&&(s=a)}return[e?1-s:s,i]}show(t,e,i){const[s,n]=this.#A(e,i),{style:a}=this.#s||=this.#b();t.append(this.#s),a.insetInlineEnd=100*s+"%",a.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){this.#s.remove()}#v(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const i=this.#f._signal;t.addEventListener("contextmenu",St,{signal:i}),t.addEventListener("click",(()=>{this.#f.highlightSelection("floating_button")}),{signal:i}),this.#r.append(t)}}function Nt(t,e,i){for(const s of i)e.addEventListener(s,t[s].bind(t))}class Ot{#w=0;get id(){return"pdfjs_internal_editor_"+this.#w++}}class Bt{#y=ht();#w=0;#_=null;static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),e=new Image;e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';return q(this,"_isSVGFittingCanvas",e.decode().then((()=>(t.drawImage(e,0,0,1,1,0,0,1,3),0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0]))))}async#x(t,e){this.#_||=new Map;let i=this.#_.get(t);if(null===i)return null;if(i?.bitmap)return i.refCounter+=1,i;try{let t;if(i||={bitmap:null,id:`image_${this.#y}_${this.#w++}`,refCounter:0,isSvg:!1},"string"==typeof e?(i.url=e,t=await mt(e,"blob")):e instanceof File?t=i.file=e:e instanceof Blob&&(t=e),"image/svg+xml"===t.type){const e=Bt._isSVGFittingCanvas,s=new FileReader,n=new Image,a=new Promise(((t,a)=>{n.onload=()=>{i.bitmap=n,i.isSvg=!0,t()},s.onload=async()=>{const t=i.svgUrl=s.result;n.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t},n.onerror=s.onerror=a}));s.readAsDataURL(t),await a}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(t){$(t),i=null}return this.#_.set(t,i),i&&this.#_.set(i.id,i),i}async getFromFile(t){const{lastModified:e,name:i,size:s,type:n}=t;return this.#x(`${e}_${i}_${s}_${n}`,t)}async getFromUrl(t){return this.#x(t,t)}async getFromBlob(t,e){const i=await e;return this.#x(t,i)}async getFromId(t){this.#_||=new Map;const e=this.#_.get(t);if(!e)return null;if(e.bitmap)return e.refCounter+=1,e;if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:t}=e;return delete e.blobPromise,this.getFromBlob(e.id,t)}return this.getFromUrl(e.url)}getFromCanvas(t,e){this.#_||=new Map;let i=this.#_.get(t);if(i?.bitmap)return i.refCounter+=1,i;const s=new OffscreenCanvas(e.width,e.height);return s.getContext("2d").drawImage(e,0,0),i={bitmap:s.transferToImageBitmap(),id:`image_${this.#y}_${this.#w++}`,refCounter:1,isSvg:!1},this.#_.set(t,i),this.#_.set(i.id,i),i}getSvgUrl(t){const e=this.#_.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#_||=new Map;const e=this.#_.get(t);if(!e)return;if(e.refCounter-=1,0!==e.refCounter)return;const{bitmap:i}=e;if(!e.url&&!e.file){const t=new OffscreenCanvas(i.width,i.height);t.getContext("bitmaprenderer").transferFromImageBitmap(i),e.blobPromise=t.convertToBlob()}i.close?.(),e.bitmap=null}isValidId(t){return t.startsWith(`image_${this.#y}_`)}}class Ht{#S=[];#E=!1;#C;#T=-1;constructor(t=128){this.#C=t}add({cmd:t,undo:e,post:i,mustExec:s,type:n=NaN,overwriteIfSameType:a=!1,keepUndo:r=!1}){if(s&&t(),this.#E)return;const o={cmd:t,undo:e,post:i,type:n};if(-1===this.#T)return this.#S.length>0&&(this.#S.length=0),this.#T=0,void this.#S.push(o);if(a&&this.#S[this.#T].type===n)return r&&(o.undo=this.#S[this.#T].undo),void(this.#S[this.#T]=o);const l=this.#T+1;l===this.#C?this.#S.splice(0,1):(this.#T=l,l<this.#S.length&&this.#S.splice(l)),this.#S.push(o)}undo(){if(-1===this.#T)return;this.#E=!0;const{undo:t,post:e}=this.#S[this.#T];t(),e?.(),this.#E=!1,this.#T-=1}redo(){if(this.#T<this.#S.length-1){this.#T+=1,this.#E=!0;const{cmd:t,post:e}=this.#S[this.#T];t(),e?.(),this.#E=!1}}hasSomethingToUndo(){return-1!==this.#T}hasSomethingToRedo(){return this.#T<this.#S.length-1}cleanType(t){if(-1!==this.#T){for(let e=this.#T;e>=0;e--)if(this.#S[e].type!==t)return this.#S.splice(e+1,this.#T-e),void(this.#T=e);this.#S.length=0,this.#T=-1}}destroy(){this.#S=null}}class zt{constructor(t){this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=st.platform;for(const[i,s,n={}]of t)for(const t of i){const i=t.startsWith("mac+");e&&i?(this.callbacks.set(t.slice(4),{callback:s,options:n}),this.allKeys.add(t.split("+").at(-1))):e||i||(this.callbacks.set(t,{callback:s,options:n}),this.allKeys.add(t.split("+").at(-1)))}}#M(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e}exec(t,e){if(!this.allKeys.has(e.key))return;const i=this.callbacks.get(this.#M(e));if(!i)return;const{callback:s,options:{bubbles:n=!1,args:a=[],checker:r=null}}=i;r&&!r(t,e)||(s.bind(t,...a,e)(),n||Et(e))}}class Ut{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return function(t){const e=document.createElement("span");e.style.visibility="hidden",e.style.colorScheme="only light",document.body.append(e);for(const i of t.keys()){e.style.color=i;const s=window.getComputedStyle(e).color;t.set(i,Mt(s))}e.remove()}(t),q(this,"_colors",t)}convert(t){const e=Mt(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[t,i]of this._colors)if(i.every(((t,i)=>t===e[i])))return Ut._colorsMapping.get(t);return e}getHexCode(t){const e=this._colors.get(t);return e?at.makeHexColor(...e):t}}class $t{#P=new AbortController;#I=null;#k=new Map;#D=new Map;#R=null;#L=null;#F=null;#N=new Ht;#O=null;#B=null;#H=0;#z=new Set;#U=null;#$=null;#G=new Set;_editorUndoBar=null;#j=!1;#V=!1;#W=!1;#q=null;#X=null;#K=null;#Y=null;#Q=!1;#J=null;#Z=new Ot;#tt=!1;#et=!1;#it=null;#st=null;#nt=null;#at=null;#rt=null;#ot=m.NONE;#lt=new Set;#ht=null;#dt=null;#ct=null;#ut=null;#pt={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#gt=[0,0];#mt=null;#ft=null;#bt=null;#vt=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=$t.prototype,e=t=>t.#ft.contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),i=(t,{target:e})=>{if(e instanceof HTMLInputElement){const{type:t}=e;return"text"!==t&&"number"!==t}return!0},s=this.TRANSLATE_SMALL,n=this.TRANSLATE_BIG;return q(this,"_keyboardManager",new zt([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:i}],[["ctrl+z","mac+meta+z"],t.undo,{checker:i}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:i}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:i}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#ft.contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#ft.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-s,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-n,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[s,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[n,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-s],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-n],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,s],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,n],checker:e}]]))}constructor(t,e,i,s,n,a,r,o,l,h,d,c,u,p){const g=this._signal=this.#P.signal;this.#ft=t,this.#bt=e,this.#R=i,this.#dt=s,this._eventBus=n,n._on("editingaction",this.onEditingAction.bind(this),{signal:g}),n._on("pagechanging",this.onPageChanging.bind(this),{signal:g}),n._on("scalechanging",this.onScaleChanging.bind(this),{signal:g}),n._on("rotationchanging",this.onRotationChanging.bind(this),{signal:g}),n._on("setpreference",this.onSetPreference.bind(this),{signal:g}),n._on("switchannotationeditorparams",(t=>this.updateParams(t.type,t.value)),{signal:g}),this.#At(),this.#wt(),this.#yt(),this.#L=a.annotationStorage,this.#q=a.filterFactory,this.#ct=r,this.#Y=o||null,this.#j=l,this.#V=h,this.#W=d,this.#rt=c||null,this.viewParameters={realScale:gt.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1,this._editorUndoBar=u||null,this._supportsPinchToZoom=!1!==p}destroy(){this.#vt?.resolve(),this.#vt=null,this.#P?.abort(),this.#P=null,this._signal=null;for(const t of this.#D.values())t.destroy();this.#D.clear(),this.#k.clear(),this.#G.clear(),this.#at?.clear(),this.#I=null,this.#lt.clear(),this.#N.destroy(),this.#R?.destroy(),this.#dt?.destroy(),this.#J?.hide(),this.#J=null,this.#nt?.destroy(),this.#nt=null,this.#X&&(clearTimeout(this.#X),this.#X=null),this.#mt&&(clearTimeout(this.#mt),this.#mt=null),this._editorUndoBar?.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return this.#rt}get useNewAltTextFlow(){return this.#V}get useNewAltTextWhenAddingImage(){return this.#W}get hcmFilter(){return q(this,"hcmFilter",this.#ct?this.#q.addHCMFilter(this.#ct.foreground,this.#ct.background):"none")}get direction(){return q(this,"direction",getComputedStyle(this.#ft).direction)}get highlightColors(){return q(this,"highlightColors",this.#Y?new Map(this.#Y.split(",").map((t=>t.split("=").map((t=>t.trim()))))):null)}get highlightColorNames(){return q(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,(t=>t.reverse()))):null)}setCurrentDrawingSession(t){t?(this.unselectAll(),this.disableUserSelect(!0)):this.disableUserSelect(!1),this.#B=t}setMainHighlightColorPicker(t){this.#nt=t}editAltText(t,e=!1){this.#R?.editAltText(this,t,e)}getSignature(t){this.#dt?.getSignature({uiManager:this,editor:t})}get signatureManager(){return this.#dt}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal}),this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){if("enableNewAltTextWhenAddingImage"===t)this.#W=e}onPageChanging({pageNumber:t}){this.#H=t-1}focusMainContainer(){this.#ft.focus()}findParent(t,e){for(const i of this.#D.values()){const{x:s,y:n,width:a,height:r}=i.div.getBoundingClientRect();if(t>=s&&t<=s+a&&e>=n&&e<=n+r)return i}return null}disableUserSelect(t=!1){this.#bt.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#G.add(t)}removeShouldRescale(t){this.#G.delete(t)}onScaleChanging({scale:t}){this.commitOrRemove(),this.viewParameters.realScale=t*gt.PDF_TO_CSS_UNITS;for(const t of this.#G)t.onScaleChanging();this.#B?.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}#_t({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}#xt(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const e of this.#D.values())if(e.hasTextLayer(t))return e;return null}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:a}=e,r=e.toString(),o=this.#_t(e).closest(".textLayer"),l=this.getSelectionBoxes(o);if(!l)return;e.empty();const h=this.#xt(o),d=this.#ot===m.NONE,c=()=>{h?.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:a,text:r}),d&&this.showAllEditors("highlight",!0,!0)};d?this.switchToMode(m.HIGHLIGHT,c):c()}#St(){const t=document.getSelection();if(!t||t.isCollapsed)return;const e=this.#_t(t).closest(".textLayer"),i=this.getSelectionBoxes(e);i&&(this.#J||=new Ft(this),this.#J.show(e,i,"ltr"===this.direction))}addToAnnotationStorage(t){t.isEmpty()||!this.#L||this.#L.has(t.id)||this.#L.setValue(t.id,t)}#Et(){const t=document.getSelection();if(!t||t.isCollapsed)return void(this.#ht&&(this.#J?.hide(),this.#ht=null,this.#Ct({hasSelectedText:!1})));const{anchorNode:e}=t;if(e===this.#ht)return;const i=this.#_t(t).closest(".textLayer");if(i){if(this.#J?.hide(),this.#ht=e,this.#Ct({hasSelectedText:!0}),(this.#ot===m.HIGHLIGHT||this.#ot===m.NONE)&&(this.#ot===m.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),this.#Q=this.isShiftKeyDown,!this.isShiftKeyDown)){const t=this.#ot===m.HIGHLIGHT?this.#xt(i):null;t?.toggleDrawing();const e=new AbortController,s=this.combinedSignal(e),n=i=>{"pointerup"===i.type&&0!==i.button||(e.abort(),t?.toggleDrawing(!0),"pointerup"===i.type&&this.#Tt("main_toolbar"))};window.addEventListener("pointerup",n,{signal:s}),window.addEventListener("blur",n,{signal:s})}}else this.#ht&&(this.#J?.hide(),this.#ht=null,this.#Ct({hasSelectedText:!1}))}#Tt(t=""){this.#ot===m.HIGHLIGHT?this.highlightSelection(t):this.#j&&this.#St()}#At(){document.addEventListener("selectionchange",this.#Et.bind(this),{signal:this._signal})}#Mt(){if(this.#K)return;this.#K=new AbortController;const t=this.combinedSignal(this.#K);window.addEventListener("focus",this.focus.bind(this),{signal:t}),window.addEventListener("blur",this.blur.bind(this),{signal:t})}#Pt(){this.#K?.abort(),this.#K=null}blur(){if(this.isShiftKeyDown=!1,this.#Q&&(this.#Q=!1,this.#Tt("main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#lt)if(e.div.contains(t)){this.#st=[e,t],e._focusEventsAllowed=!1;break}}focus(){if(!this.#st)return;const[t,e]=this.#st;this.#st=null,e.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this._signal}),e.focus()}#yt(){if(this.#it)return;this.#it=new AbortController;const t=this.combinedSignal(this.#it);window.addEventListener("keydown",this.keydown.bind(this),{signal:t}),window.addEventListener("keyup",this.keyup.bind(this),{signal:t})}#It(){this.#it?.abort(),this.#it=null}#kt(){if(this.#O)return;this.#O=new AbortController;const t=this.combinedSignal(this.#O);document.addEventListener("copy",this.copy.bind(this),{signal:t}),document.addEventListener("cut",this.cut.bind(this),{signal:t}),document.addEventListener("paste",this.paste.bind(this),{signal:t})}#Dt(){this.#O?.abort(),this.#O=null}#wt(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})}addEditListeners(){this.#yt(),this.#kt()}removeEditListeners(){this.#It(),this.#Dt()}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const i of this.#$)if(i.isHandlingMimeForPasting(e))return t.dataTransfer.dropEffect="copy",void t.preventDefault()}drop(t){for(const e of t.dataTransfer.items)for(const i of this.#$)if(i.isHandlingMimeForPasting(e.type))return i.paste(e,this.currentLayer),void t.preventDefault()}copy(t){if(t.preventDefault(),this.#I?.commitOrRemove(),!this.hasSelection)return;const e=[];for(const t of this.#lt){const i=t.serialize(!0);i&&e.push(i)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const t of e.items)for(const e of this.#$)if(e.isHandlingMimeForPasting(t.type))return void e.paste(t,this.currentLayer);let i=e.getData("application/pdfjs");if(!i)return;try{i=JSON.parse(i)}catch(t){return void $(`paste: "${t.message}".`)}if(!Array.isArray(i))return;this.unselectAll();const s=this.currentLayer;try{const t=[];for(const e of i){const i=await s.deserialize(e);if(!i)return;t.push(i)}const e=()=>{for(const e of t)this.#Rt(e);this.#Lt(t)},n=()=>{for(const e of t)e.remove()};this.addCommands({cmd:e,undo:n,mustExec:!0})}catch(t){$(`paste: "${t.message}".`)}}keydown(t){this.isShiftKeyDown||"Shift"!==t.key||(this.isShiftKeyDown=!0),this.#ot===m.NONE||this.isEditorHandlingKeyboard||$t._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&"Shift"===t.key&&(this.isShiftKeyDown=!1,this.#Q&&(this.#Q=!1,this.#Tt("main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}#Ct(t){Object.entries(t).some((([t,e])=>this.#pt[t]!==e))&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#pt,t)}),this.#ot===m.HIGHLIGHT&&!1===t.hasSelectedEditor&&this.#Ft([[f.HIGHLIGHT_FREE,!0]]))}#Ft(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){t?(this.#Mt(),this.#kt(),this.#Ct({isEditing:this.#ot!==m.NONE,isEmpty:this.#Nt(),hasSomethingToUndo:this.#N.hasSomethingToUndo(),hasSomethingToRedo:this.#N.hasSomethingToRedo(),hasSelectedEditor:!1})):(this.#Pt(),this.#Dt(),this.#Ct({isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!this.#$){this.#$=t;for(const t of this.#$)this.#Ft(t.defaultPropertiesToUpdate)}}getId(){return this.#Z.id}get currentLayer(){return this.#D.get(this.#H)}getLayer(t){return this.#D.get(t)}get currentPageIndex(){return this.#H}addLayer(t){this.#D.set(t.pageIndex,t),this.#tt?t.enable():t.disable()}removeLayer(t){this.#D.delete(t.pageIndex)}async updateMode(t,e=null,i=!1){if(this.#ot!==t&&(!this.#vt||(await this.#vt.promise,this.#vt))){if(this.#vt=Promise.withResolvers(),this.#B?.commitOrRemove(),this.#ot=t,t===m.NONE)return this.setEditingState(!1),this.#Ot(),this._editorUndoBar?.hide(),void this.#vt.resolve();t===m.SIGNATURE&&await(this.#dt?.loadSignatures()),this.setEditingState(!0),await this.#Bt(),this.unselectAll();for(const e of this.#D.values())e.updateMode(t);if(!e)return i&&this.addNewEditorFromKeyboard(),void this.#vt.resolve();for(const t of this.#k.values())t.annotationElementId===e?(this.setSelected(t),t.enterInEditMode()):t.unselect();this.#vt.resolve()}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==this.#ot&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(this.#$){switch(t){case f.CREATE:return void this.currentLayer.addNewEditor(e);case f.HIGHLIGHT_DEFAULT_COLOR:this.#nt?.updateColor(e);break;case f.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(this.#ut||=new Map).set(t,e),this.showAllEditors("highlight",e)}for(const i of this.#lt)i.updateParams(t,e);for(const i of this.#$)i.updateDefaultParams(t,e)}}showAllEditors(t,e,i=!1){for(const i of this.#k.values())i.editorType===t&&i.show(e);(this.#ut?.get(f.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#Ft([[f.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#et!==t){this.#et=t;for(const e of this.#D.values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}async#Bt(){if(!this.#tt){this.#tt=!0;const t=[];for(const e of this.#D.values())t.push(e.enable());await Promise.all(t);for(const t of this.#k.values())t.enable()}}#Ot(){if(this.unselectAll(),this.#tt){this.#tt=!1;for(const t of this.#D.values())t.disable();for(const t of this.#k.values())t.disable()}}getEditors(t){const e=[];for(const i of this.#k.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#k.get(t)}addEditor(t){this.#k.set(t.id,t)}removeEditor(t){t.div.contains(document.activeElement)&&(this.#X&&clearTimeout(this.#X),this.#X=setTimeout((()=>{this.focusMainContainer(),this.#X=null}),0)),this.#k.delete(t.id),t.annotationElementId&&this.#at?.delete(t.annotationElementId),this.unselect(t),t.annotationElementId&&this.#z.has(t.annotationElementId)||this.#L?.remove(t.id)}addDeletedAnnotationElement(t){this.#z.add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return this.#z.has(t)}removeDeletedAnnotationElement(t){this.#z.delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}#Rt(t){const e=this.#D.get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))}setActiveEditor(t){this.#I!==t&&(this.#I=t,t&&this.#Ft(t.propertiesToUpdate))}get#Ht(){let t=null;for(t of this.#lt);return t}updateUI(t){this.#Ht===t&&this.#Ft(t.propertiesToUpdate)}updateUIForDefaultProperties(t){this.#Ft(t.defaultPropertiesToUpdate)}toggleSelected(t){if(this.#lt.has(t))return this.#lt.delete(t),t.unselect(),void this.#Ct({hasSelectedEditor:this.hasSelection});this.#lt.add(t),t.select(),this.#Ft(t.propertiesToUpdate),this.#Ct({hasSelectedEditor:!0})}setSelected(t){this.#B?.commitOrRemove();for(const e of this.#lt)e!==t&&e.unselect();this.#lt.clear(),this.#lt.add(t),t.select(),this.#Ft(t.propertiesToUpdate),this.#Ct({hasSelectedEditor:!0})}isSelected(t){return this.#lt.has(t)}get firstSelectedEditor(){return this.#lt.values().next().value}unselect(t){t.unselect(),this.#lt.delete(t),this.#Ct({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#lt.size}get isEnterHandled(){return 1===this.#lt.size&&this.firstSelectedEditor.isEnterHandled}undo(){this.#N.undo(),this.#Ct({hasSomethingToUndo:this.#N.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#Nt()}),this._editorUndoBar?.hide()}redo(){this.#N.redo(),this.#Ct({hasSomethingToUndo:!0,hasSomethingToRedo:this.#N.hasSomethingToRedo(),isEmpty:this.#Nt()})}addCommands(t){this.#N.add(t),this.#Ct({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#Nt()})}cleanUndoStack(t){this.#N.cleanType(t)}#Nt(){if(0===this.#k.size)return!0;if(1===this.#k.size)for(const t of this.#k.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();const t=this.currentLayer?.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...this.#lt],i=()=>{for(const t of e)this.#Rt(t)};this.addCommands({cmd:()=>{this._editorUndoBar?.show(i,1===e.length?e[0].editorType:e.length);for(const t of e)t.remove()},undo:i,mustExec:!0})}commitOrRemove(){this.#I?.commitOrRemove()}hasSomethingToControl(){return this.#I||this.hasSelection}#Lt(t){for(const t of this.#lt)t.unselect();this.#lt.clear();for(const e of t)e.isEmpty()||(this.#lt.add(e),e.select());this.#Ct({hasSelectedEditor:this.hasSelection})}selectAll(){for(const t of this.#lt)t.commit();this.#Lt(this.#k.values())}unselectAll(){if((!this.#I||(this.#I.commitOrRemove(),this.#ot===m.NONE))&&!this.#B?.commitOrRemove()&&this.hasSelection){for(const t of this.#lt)t.unselect();this.#lt.clear(),this.#Ct({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,i=!1){if(i||this.commitOrRemove(),!this.hasSelection)return;this.#gt[0]+=t,this.#gt[1]+=e;const[s,n]=this.#gt,a=[...this.#lt];this.#mt&&clearTimeout(this.#mt),this.#mt=setTimeout((()=>{this.#mt=null,this.#gt[0]=this.#gt[1]=0,this.addCommands({cmd:()=>{for(const t of a)this.#k.has(t.id)&&(t.translateInPage(s,n),t.translationDone())},undo:()=>{for(const t of a)this.#k.has(t.id)&&(t.translateInPage(-s,-n),t.translationDone())},mustExec:!1})}),1e3);for(const i of a)i.translateInPage(t,e),i.translationDone()}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),this.#U=new Map;for(const t of this.#lt)this.#U.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#U)return!1;this.disableUserSelect(!1);const t=this.#U;this.#U=null;let e=!1;for(const[{x:i,y:s,pageIndex:n},a]of t)a.newX=i,a.newY=s,a.newPageIndex=n,e||=i!==a.savedX||s!==a.savedY||n!==a.savedPageIndex;if(!e)return!1;const i=(t,e,i,s)=>{if(this.#k.has(t.id)){const n=this.#D.get(s);n?t._setParentAndPosition(n,e,i):(t.pageIndex=s,t.x=e,t.y=i)}};return this.addCommands({cmd:()=>{for(const[e,{newX:s,newY:n,newPageIndex:a}]of t)i(e,s,n,a)},undo:()=>{for(const[e,{savedX:s,savedY:n,savedPageIndex:a}]of t)i(e,s,n,a)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(this.#U)for(const i of this.#U.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||1===this.#lt.size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#I===t}getActive(){return this.#I}getMode(){return this.#ot}get imageManager(){return q(this,"imageManager",new Bt)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let i=0,s=e.rangeCount;i<s;i++)if(!t.contains(e.getRangeAt(i).commonAncestorContainer))return null;const{x:i,y:s,width:n,height:a}=t.getBoundingClientRect();let r;switch(t.getAttribute("data-main-rotation")){case"90":r=(t,e,r,o)=>({x:(e-s)/a,y:1-(t+r-i)/n,width:o/a,height:r/n});break;case"180":r=(t,e,r,o)=>({x:1-(t+r-i)/n,y:1-(e+o-s)/a,width:r/n,height:o/a});break;case"270":r=(t,e,r,o)=>({x:1-(e+o-s)/a,y:(t-i)/n,width:o/a,height:r/n});break;default:r=(t,e,r,o)=>({x:(t-i)/n,y:(e-s)/a,width:r/n,height:o/a})}const o=[];for(let t=0,i=e.rangeCount;t<i;t++){const i=e.getRangeAt(t);if(!i.collapsed)for(const{x:t,y:e,width:s,height:n}of i.getClientRects())0!==s&&0!==n&&o.push(r(t,e,s,n))}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#F||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#F?.delete(t)}renderAnnotationElement(t){const e=this.#F?.get(t.data.id);if(!e)return;const i=this.#L.getRawValue(e);i&&(this.#ot!==m.NONE||i.hasBeenModified)&&i.renderAnnotationElement(t)}setMissingCanvas(t,e,i){const s=this.#at?.get(t);s&&(s.setCanvas(e,i),this.#at.delete(t))}addMissingCanvas(t,e){(this.#at||=new Map).set(t,e)}}class Gt{#o=null;#zt=!1;#Ut=null;#$t=null;#Gt=null;#jt=null;#Vt=!1;#Wt=null;#a=null;#qt=null;#Xt=null;#Kt=!1;static#Yt=null;static _l10n=null;constructor(t){this.#a=t,this.#Kt=t._uiManager.useNewAltTextFlow,Gt.#Yt||=Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"})}static initialize(t){Gt._l10n??=t}async render(){const t=this.#Ut=document.createElement("button");t.className="altText",t.tabIndex="0";const e=this.#$t=document.createElement("span");t.append(e),this.#Kt?(t.classList.add("new"),t.setAttribute("data-l10n-id",Gt.#Yt.missing),e.setAttribute("data-l10n-id",Gt.#Yt["missing-label"])):(t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button"),e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label"));const i=this.#a._uiManager._signal;t.addEventListener("contextmenu",St,{signal:i}),t.addEventListener("pointerdown",(t=>t.stopPropagation()),{signal:i});const s=t=>{t.preventDefault(),this.#a._uiManager.editAltText(this.#a),this.#Kt&&this.#a._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:this.#Qt}})};return t.addEventListener("click",s,{capture:!0,signal:i}),t.addEventListener("keydown",(e=>{e.target===t&&"Enter"===e.key&&(this.#Vt=!0,s(e))}),{signal:i}),await this.#Jt(),t}get#Qt(){return(this.#o?"added":null===this.#o&&this.guessedText&&"review")||"missing"}finish(){this.#Ut&&(this.#Ut.focus({focusVisible:this.#Vt}),this.#Vt=!1)}isEmpty(){return this.#Kt?null===this.#o:!this.#o&&!this.#zt}hasData(){return this.#Kt?null!==this.#o||!!this.#qt:this.isEmpty()}get guessedText(){return this.#qt}async setGuessedText(t){null===this.#o&&(this.#qt=t,this.#Xt=await Gt._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t}),this.#Jt())}toggleAltTextBadge(t=!1){if(!this.#Kt||this.#o)return this.#Wt?.remove(),void(this.#Wt=null);if(!this.#Wt){const t=this.#Wt=document.createElement("div");t.className="noAltTextBadge",this.#a.div.append(t)}this.#Wt.classList.toggle("hidden",!t)}serialize(t){let e=this.#o;return t||this.#qt!==e||(e=this.#Xt),{altText:e,decorative:this.#zt,guessedText:this.#qt,textWithDisclaimer:this.#Xt}}get data(){return{altText:this.#o,decorative:this.#zt}}set data({altText:t,decorative:e,guessedText:i,textWithDisclaimer:s,cancel:n=!1}){i&&(this.#qt=i,this.#Xt=s),this.#o===t&&this.#zt===e||(n||(this.#o=t,this.#zt=e),this.#Jt())}toggle(t=!1){this.#Ut&&(!t&&this.#jt&&(clearTimeout(this.#jt),this.#jt=null),this.#Ut.disabled=!t)}shown(){this.#a._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:this.#Qt}})}destroy(){this.#Ut?.remove(),this.#Ut=null,this.#$t=null,this.#Gt=null,this.#Wt?.remove(),this.#Wt=null}async#Jt(){const t=this.#Ut;if(!t)return;if(this.#Kt){if(t.classList.toggle("done",!!this.#o),t.setAttribute("data-l10n-id",Gt.#Yt[this.#Qt]),this.#$t?.setAttribute("data-l10n-id",Gt.#Yt[`${this.#Qt}-label`]),!this.#o)return void this.#Gt?.remove()}else{if(!this.#o&&!this.#zt)return t.classList.remove("done"),void this.#Gt?.remove();t.classList.add("done"),t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=this.#Gt;if(!e){this.#Gt=e=document.createElement("span"),e.className="tooltip",e.setAttribute("role","tooltip"),e.id=`alt-text-tooltip-${this.#a.id}`;const i=100,s=this.#a._uiManager._signal;s.addEventListener("abort",(()=>{clearTimeout(this.#jt),this.#jt=null}),{once:!0}),t.addEventListener("mouseenter",(()=>{this.#jt=setTimeout((()=>{this.#jt=null,this.#Gt.classList.add("show"),this.#a._reportTelemetry({action:"alt_text_tooltip"})}),i)}),{signal:s}),t.addEventListener("mouseleave",(()=>{this.#jt&&(clearTimeout(this.#jt),this.#jt=null),this.#Gt?.classList.remove("show")}),{signal:s})}this.#zt?e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip"):(e.removeAttribute("data-l10n-id"),e.textContent=this.#o),e.parentNode||t.append(e);const i=this.#a.getElementForAltText();i?.setAttribute("aria-describedby",e.id)}}class jt{#ft;#Zt=!1;#te=null;#ee;#ie;#se;#ne;#ae=null;#re;#oe=null;#le;#he=null;constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:i=null,onPinchStart:s=null,onPinching:n=null,onPinchEnd:a=null,signal:r}){this.#ft=t,this.#te=i,this.#ee=e,this.#ie=s,this.#se=n,this.#ne=a,this.#le=new AbortController,this.#re=AbortSignal.any([r,this.#le.signal]),t.addEventListener("touchstart",this.#de.bind(this),{passive:!1,signal:this.#re})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return 35/Dt.pixelRatio}#de(t){if(this.#ee?.())return;if(1===t.touches.length){if(this.#ae)return;const t=this.#ae=new AbortController,e=AbortSignal.any([this.#re,t.signal]),i=this.#ft,s={capture:!0,signal:e,passive:!1},n=t=>{"touch"===t.pointerType&&(this.#ae?.abort(),this.#ae=null)};return i.addEventListener("pointerdown",(t=>{"touch"===t.pointerType&&(Et(t),n(t))}),s),i.addEventListener("pointerup",n,s),void i.addEventListener("pointercancel",n,s)}if(!this.#he){this.#he=new AbortController;const t=AbortSignal.any([this.#re,this.#he.signal]),e=this.#ft,i={signal:t,capture:!1,passive:!1};e.addEventListener("touchmove",this.#ce.bind(this),i);const s=this.#ue.bind(this);e.addEventListener("touchend",s,i),e.addEventListener("touchcancel",s,i),i.capture=!0,e.addEventListener("pointerdown",Et,i),e.addEventListener("pointermove",Et,i),e.addEventListener("pointercancel",Et,i),e.addEventListener("pointerup",Et,i),this.#ie?.()}if(Et(t),2!==t.touches.length||this.#te?.())return void(this.#oe=null);let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]),this.#oe={touch0X:e.screenX,touch0Y:e.screenY,touch1X:i.screenX,touch1Y:i.screenY}}#ce(t){if(!this.#oe||2!==t.touches.length)return;Et(t);let[e,i]=t.touches;e.identifier>i.identifier&&([e,i]=[i,e]);const{screenX:s,screenY:n}=e,{screenX:a,screenY:r}=i,o=this.#oe,{touch0X:l,touch0Y:h,touch1X:d,touch1Y:c}=o,u=d-l,p=c-h,g=a-s,m=r-n,f=Math.hypot(g,m)||1,b=Math.hypot(u,p)||1;if(!this.#Zt&&Math.abs(b-f)<=jt.MIN_TOUCH_DISTANCE_TO_PINCH)return;if(o.touch0X=s,o.touch0Y=n,o.touch1X=a,o.touch1Y=r,!this.#Zt)return void(this.#Zt=!0);const v=[(s+a)/2,(n+r)/2];this.#se?.(v,b,f)}#ue(t){t.touches.length>=2||(this.#he&&(this.#he.abort(),this.#he=null,this.#ne?.()),this.#oe&&(Et(t),this.#oe=null,this.#Zt=!1))}destroy(){this.#le?.abort(),this.#le=null,this.#ae?.abort(),this.#ae=null}}class Vt{#pe=null;#ge=null;#o=null;#me=!1;#fe=null;#be="";#ve=!1;#Ae=null;#we=null;#ye=null;#_e=null;#xe="";#Se=!1;#Ee=null;#Ce=!1;#Te=!1;#Me=!1;#Pe=null;#Ie=0;#ke=0;#De=null;#Re=null;_isCopy=!1;_editToolbar=null;_initialOptions=Object.create(null);_initialData=null;_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;static _l10n=null;static _l10nResizer=null;#Le=!1;#Fe=Vt._zIndex++;static _borderLineWidth=-1;static _colorManager=new Ut;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){const t=Vt.prototype._resizeWithKeyboard,e=$t.TRANSLATE_SMALL,i=$t.TRANSLATE_BIG;return q(this,"_resizerKeyboardManager",new zt([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-i,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[i,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-i]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,i]}],[["Escape","mac+Escape"],Vt.prototype._stopResizingWithKeyboard]]))}constructor(t){this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:i,pageHeight:s,pageX:n,pageY:a}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[i,s],this.pageTranslation=[n,a];const[r,o]=this.parentDimensions;this.x=t.x/r,this.y=t.y/o,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get isDrawer(){return!1}static get _defaultLineColor(){return q(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new Wt({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){if(Vt._l10n??=t,Vt._l10nResizer||=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"}),-1!==Vt._borderLineWidth)return;const i=getComputedStyle(document.documentElement);Vt._borderLineWidth=parseFloat(i.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){G("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#Le}set _isDraggable(t){this.#Le=t,this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t),this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t),this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2,this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#Fe}setParent(t){null!==t?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):this.#Ne(),this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#Se?this.#Se=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;const e=t.relatedTarget;e?.closest(`#${this.id}`)||(t.preventDefault(),this.parent?.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,s){const[n,a]=this.parentDimensions;[i,s]=this.screenToPageTranslation(i,s),this.x=(t+i)/n,this.y=(e+s)/a,this.fixAndSetPosition()}_moveAfterPaste(t,e){const[i,s]=this.parentDimensions;this.setAt(t*i,e*s,this.width*i,this.height*s),this._onTranslated()}#Oe([t,e],i,s){[i,s]=this.screenToPageTranslation(i,s),this.x+=i/t,this.y+=s/e,this._onTranslating(this.x,this.y),this.fixAndSetPosition()}translate(t,e){this.#Oe(this.parentDimensions,t,e)}translateInPage(t,e){this.#Ee||=[this.x,this.y,this.width,this.height],this.#Oe(this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}translationDone(){this._onTranslated(this.x,this.y)}drag(t,e){this.#Ee||=[this.x,this.y,this.width,this.height];const{div:i,parentDimensions:[s,n]}=this;if(this.x+=t/s,this.y+=e/n,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();this.parent.findNewParent(this,t,e)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:a,y:r}=this;const[o,l]=this.getBaseTranslation();a+=o,r+=l;const{style:h}=i;h.left=`${(100*a).toFixed(2)}%`,h.top=`${(100*r).toFixed(2)}%`,this._onTranslating(a,r),i.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!this.#Ee&&(this.#Ee[0]!==this.x||this.#Ee[1]!==this.y)}get _hasBeenResized(){return!!this.#Ee&&(this.#Ee[2]!==this.width||this.#Ee[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:i}=Vt,s=i/t,n=i/e;switch(this.rotation){case 90:return[-s,n];case 180:return[s,n];case 270:return[s,-n];default:return[-s,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[i,s]}=this;let{x:n,y:a,width:r,height:o}=this;if(r*=i,o*=s,n*=i,a*=s,this._mustFixPosition)switch(t){case 0:n=ct(n,0,i-r),a=ct(a,0,s-o);break;case 90:n=ct(n,0,i-o),a=ct(a,r,s);break;case 180:n=ct(n,r,i),a=ct(a,o,s);break;case 270:n=ct(n,o,i),a=ct(a,0,s-r)}this.x=n/=i,this.y=a/=s;const[l,h]=this.getBaseTranslation();n+=l,a+=h,e.left=`${(100*n).toFixed(2)}%`,e.top=`${(100*a).toFixed(2)}%`,this.moveInDOM()}static#Be(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return Vt.#Be(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return Vt.#Be(t,e,360-this.parentRotation)}#He(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,i]}=this;return[e*t,i*t]}setDims(t,e){const[i,s]=this.parentDimensions,{style:n}=this.div;n.width=`${(100*t/i).toFixed(2)}%`,this.#ve||(n.height=`${(100*e/s).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:i}=t,s=i.endsWith("%"),n=!this.#ve&&e.endsWith("%");if(s&&n)return;const[a,r]=this.parentDimensions;s||(t.width=`${(100*parseFloat(i)/a).toFixed(2)}%`),this.#ve||n||(t.height=`${(100*parseFloat(e)/r).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#ze(){if(this.#Ae)return;this.#Ae=document.createElement("div"),this.#Ae.classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const i of t){const t=document.createElement("div");this.#Ae.append(t),t.classList.add("resizer",i),t.setAttribute("data-resizer-name",i),t.addEventListener("pointerdown",this.#Ue.bind(this,i),{signal:e}),t.addEventListener("contextmenu",St,{signal:e}),t.tabIndex=-1}this.div.prepend(this.#Ae)}#Ue(t,e){e.preventDefault();const{isMac:i}=st.platform;if(0!==e.button||e.ctrlKey&&i)return;this.#o?.toggle(!1);const s=this._isDraggable;this._isDraggable=!1,this.#we=[e.screenX,e.screenY];const n=new AbortController,a=this._uiManager.combinedSignal(n);this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",this.#$e.bind(this,t),{passive:!0,capture:!0,signal:a}),window.addEventListener("touchmove",Et,{passive:!1,signal:a}),window.addEventListener("contextmenu",St,{signal:a}),this.#ye={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const r=this.parent.div.style.cursor,o=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const l=()=>{n.abort(),this.parent.togglePointerEvents(!0),this.#o?.toggle(!0),this._isDraggable=s,this.parent.div.style.cursor=r,this.div.style.cursor=o,this.#Ge()};window.addEventListener("pointerup",l,{signal:a}),window.addEventListener("blur",l,{signal:a})}#je(t,e,i,s){this.width=i,this.height=s,this.x=t,this.y=e;const[n,a]=this.parentDimensions;this.setDims(n*i,a*s),this.fixAndSetPosition(),this._onResized()}_onResized(){}#Ge(){if(!this.#ye)return;const{savedX:t,savedY:e,savedWidth:i,savedHeight:s}=this.#ye;this.#ye=null;const n=this.x,a=this.y,r=this.width,o=this.height;n===t&&a===e&&r===i&&o===s||this.addCommands({cmd:this.#je.bind(this,n,a,r,o),undo:this.#je.bind(this,t,e,i,s),mustExec:!0})}static _round(t){return Math.round(1e4*t)/1e4}#$e(t,e){const[i,s]=this.parentDimensions,n=this.x,a=this.y,r=this.width,o=this.height,l=Vt.MIN_SIZE/i,h=Vt.MIN_SIZE/s,d=this.#He(this.rotation),c=(t,e)=>[d[0]*t+d[2]*e,d[1]*t+d[3]*e],u=this.#He(360-this.rotation);let p,g,m=!1,f=!1;switch(t){case"topLeft":m=!0,p=(t,e)=>[0,0],g=(t,e)=>[t,e];break;case"topMiddle":p=(t,e)=>[t/2,0],g=(t,e)=>[t/2,e];break;case"topRight":m=!0,p=(t,e)=>[t,0],g=(t,e)=>[0,e];break;case"middleRight":f=!0,p=(t,e)=>[t,e/2],g=(t,e)=>[0,e/2];break;case"bottomRight":m=!0,p=(t,e)=>[t,e],g=(t,e)=>[0,0];break;case"bottomMiddle":p=(t,e)=>[t/2,e],g=(t,e)=>[t/2,0];break;case"bottomLeft":m=!0,p=(t,e)=>[0,e],g=(t,e)=>[t,0];break;case"middleLeft":f=!0,p=(t,e)=>[0,e/2],g=(t,e)=>[t,e/2]}const b=p(r,o),v=g(r,o);let A=c(...v);const w=Vt._round(n+A[0]),y=Vt._round(a+A[1]);let _,x,S=1,E=1;if(e.fromKeyboard)({deltaX:_,deltaY:x}=e);else{const{screenX:t,screenY:i}=e,[s,n]=this.#we;[_,x]=this.screenToPageTranslation(t-s,i-n),this.#we[0]=t,this.#we[1]=i}var C,T;if([_,x]=(C=_/i,T=x/s,[u[0]*C+u[2]*T,u[1]*C+u[3]*T]),m){const t=Math.hypot(r,o);S=E=Math.max(Math.min(Math.hypot(v[0]-b[0]-_,v[1]-b[1]-x)/t,1/r,1/o),l/r,h/o)}else f?S=ct(Math.abs(v[0]-b[0]-_),l,1)/r:E=ct(Math.abs(v[1]-b[1]-x),h,1)/o;const M=Vt._round(r*S),P=Vt._round(o*E);A=c(...g(M,P));const I=w-A[0],k=y-A[1];this.#Ee||=[this.x,this.y,this.width,this.height],this.width=M,this.height=P,this.x=I,this.y=k,this.setDims(i*M,s*P),this.fixAndSetPosition(),this._onResizing()}_onResizing(){}altTextFinish(){this.#o?.finish()}async addEditToolbar(){return this._editToolbar||this.#Te||(this._editToolbar=new Lt(this),this.div.append(this._editToolbar.render()),this.#o&&await this._editToolbar.addAltText(this.#o)),this._editToolbar}removeEditToolbar(){this._editToolbar&&(this._editToolbar.remove(),this._editToolbar=null,this.#o?.destroy())}addContainer(t){const e=this._editToolbar?.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){this.#o||(Gt.initialize(Vt._l10n),this.#o=new Gt(this),this.#pe&&(this.#o.data=this.#pe,this.#pe=null),await this.addEditToolbar())}get altTextData(){return this.#o?.data}set altTextData(t){this.#o&&(this.#o.data=t)}get guessedAltText(){return this.#o?.guessedText}async setGuessedAltText(t){await(this.#o?.setGuessedText(t))}serializeAltText(t){return this.#o?.serialize(t)}hasAltText(){return!!this.#o&&!this.#o.isEmpty()}hasAltTextData(){return this.#o?.hasData()??!1}render(){const t=this.div=document.createElement("div");t.setAttribute("data-editor-rotation",(360-this.rotation)%360),t.className=this.name,t.setAttribute("id",this.id),t.tabIndex=this.#me?-1:0,t.setAttribute("role","application"),this.defaultL10nId&&t.setAttribute("data-l10n-id",this.defaultL10nId),this._isVisible||t.classList.add("hidden"),this.setInForeground(),this.#Ve();const[e,i]=this.parentDimensions;this.parentRotation%180!=0&&(t.style.maxWidth=`${(100*i/e).toFixed(2)}%`,t.style.maxHeight=`${(100*e/i).toFixed(2)}%`);const[s,n]=this.getInitialTranslation();return this.translate(s,n),Nt(this,t,["keydown","pointerdown"]),this.isResizable&&this._uiManager._supportsPinchToZoom&&(this.#Re||=new jt({container:t,isPinchingDisabled:()=>!this.isSelected,onPinchStart:this.#We.bind(this),onPinching:this.#qe.bind(this),onPinchEnd:this.#Xe.bind(this),signal:this._uiManager._signal})),this._uiManager._editorUndoBar?.hide(),t}#We(){this.#ye={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height},this.#o?.toggle(!1),this.parent.togglePointerEvents(!1)}#qe(t,e,i){let s=i/e*.7+1-.7;if(1===s)return;const n=this.#He(this.rotation),a=(t,e)=>[n[0]*t+n[2]*e,n[1]*t+n[3]*e],[r,o]=this.parentDimensions,l=this.x,h=this.y,d=this.width,c=this.height,u=Vt.MIN_SIZE/r,p=Vt.MIN_SIZE/o;s=Math.max(Math.min(s,1/d,1/c),u/d,p/c);const g=Vt._round(d*s),m=Vt._round(c*s);if(g===d&&m===c)return;this.#Ee||=[l,h,d,c];const f=a(d/2,c/2),b=Vt._round(l+f[0]),v=Vt._round(h+f[1]),A=a(g/2,m/2);this.x=b-A[0],this.y=v-A[1],this.width=g,this.height=m,this.setDims(r*g,o*m),this.fixAndSetPosition(),this._onResizing()}#Xe(){this.#o?.toggle(!0),this.parent.togglePointerEvents(!0),this.#Ge()}pointerdown(t){const{isMac:e}=st.platform;0!==t.button||t.ctrlKey&&e?t.preventDefault():(this.#Se=!0,this._isDraggable?this.#Ke(t):this.#Ye(t))}get isSelected(){return this._uiManager.isSelected(this)}#Ye(t){const{isMac:e}=st.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#Ke(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let i=!1;const s=new AbortController,n=this._uiManager.combinedSignal(s),a={capture:!0,passive:!1,signal:n},r=t=>{s.abort(),this.#fe=null,this.#Se=!1,this._uiManager.endDragSession()||this.#Ye(t),i&&this._onStopDragging()};e&&(this.#Ie=t.clientX,this.#ke=t.clientY,this.#fe=t.pointerId,this.#be=t.pointerType,window.addEventListener("pointermove",(t=>{i||(i=!0,this._onStartDragging());const{clientX:e,clientY:s,pointerId:n}=t;if(n!==this.#fe)return void Et(t);const[a,r]=this.screenToPageTranslation(e-this.#Ie,s-this.#ke);this.#Ie=e,this.#ke=s,this._uiManager.dragSelectedEditors(a,r)}),a),window.addEventListener("touchmove",Et,a),window.addEventListener("pointerdown",(t=>{t.pointerType===this.#be&&(this.#Re||t.isPrimary)&&r(t),Et(t)}),a));const o=t=>{this.#fe&&this.#fe!==t.pointerId?Et(t):r(t)};window.addEventListener("pointerup",o,{signal:n}),window.addEventListener("blur",o,{signal:n})}_onStartDragging(){}_onStopDragging(){}moveInDOM(){this.#Pe&&clearTimeout(this.#Pe),this.#Pe=setTimeout((()=>{this.#Pe=null,this.parent?.moveEditorInDOM(this)}),0)}_setParentAndPosition(t,e,i){t.changeParent(this),this.x=e,this.y=i,this.fixAndSetPosition(),this._onTranslated()}getRect(t,e,i=this.rotation){const s=this.parentScale,[n,a]=this.pageDimensions,[r,o]=this.pageTranslation,l=t/s,h=e/s,d=this.x*n,c=this.y*a,u=this.width*n,p=this.height*a;switch(i){case 0:return[d+l+r,a-c-h-p+o,d+l+u+r,a-c-h+o];case 90:return[d+h+r,a-c+l+o,d+h+p+r,a-c+l+u+o];case 180:return[d-l-u+r,a-c+h+o,d-l+r,a-c+h+p+o];case 270:return[d-h-p+r,a-c-l-u+o,d-h+r,a-c-l+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[i,s,n,a]=t,r=n-i,o=a-s;switch(this.rotation){case 0:return[i,e-a,r,o];case 90:return[i,e-s,o,r];case 180:return[n,e-s,r,o];case 270:return[n,e-a,o,r];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){this.#Te=!0}disableEditMode(){this.#Te=!1}isInEditMode(){return this.#Te}shouldGetKeyboardEvents(){return this.#Me}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:i,right:s}=this.getClientDimensions(),{innerHeight:n,innerWidth:a}=window;return e<a&&s>0&&t<n&&i>0}#Ve(){if(this.#_e||!this.div)return;this.#_e=new AbortController;const t=this._uiManager.combinedSignal(this.#_e);this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t}),this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})}rebuild(){this.#Ve()}rotate(t){}resize(){}serializeDeleted(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:this._initialData?.popupRef||""}}serialize(t=!1,e=null){G("An editor must be serializable")}static async deserialize(t,e,i){const s=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});s.rotation=t.rotation,s.#pe=t.accessibilityData,s._isCopy=t.isCopy||!1;const[n,a]=s.pageDimensions,[r,o,l,h]=s.getRectInCurrentCoords(t.rect,a);return s.x=r/n,s.y=o/a,s.width=l/n,s.height=h/a,s}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){if(this.#_e?.abort(),this.#_e=null,this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),this.#Pe&&(clearTimeout(this.#Pe),this.#Pe=null),this.#Ne(),this.removeEditToolbar(),this.#De){for(const t of this.#De.values())clearTimeout(t);this.#De=null}this.parent=null,this.#Re?.destroy(),this.#Re=null}get isResizable(){return!1}makeResizable(){this.isResizable&&(this.#ze(),this.#Ae.classList.remove("hidden"))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||"Enter"!==t.key)return;this._uiManager.setSelected(this),this.#ye={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const e=this.#Ae.children;if(!this.#ge){this.#ge=Array.from(e);const t=this.#Qe.bind(this),i=this.#Je.bind(this),s=this._uiManager._signal;for(const e of this.#ge){const n=e.getAttribute("data-resizer-name");e.setAttribute("role","spinbutton"),e.addEventListener("keydown",t,{signal:s}),e.addEventListener("blur",i,{signal:s}),e.addEventListener("focus",this.#Ze.bind(this,n),{signal:s}),e.setAttribute("data-l10n-id",Vt._l10nResizer[n])}}const i=this.#ge[0];let s=0;for(const t of e){if(t===i)break;s++}const n=(360-this.rotation+this.parentRotation)%360/90*(this.#ge.length/4);if(n!==s){if(n<s)for(let t=0;t<s-n;t++)this.#Ae.append(this.#Ae.firstChild);else if(n>s)for(let t=0;t<n-s;t++)this.#Ae.firstChild.before(this.#Ae.lastChild);let t=0;for(const i of e){const e=this.#ge[t++].getAttribute("data-resizer-name");i.setAttribute("data-l10n-id",Vt._l10nResizer[e])}}this.#ti(0),this.#Me=!0,this.#Ae.firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}#Qe(t){Vt._resizerKeyboardManager.exec(this,t)}#Je(t){this.#Me&&t.relatedTarget?.parentNode!==this.#Ae&&this.#Ne()}#Ze(t){this.#xe=this.#Me?t:""}#ti(t){if(this.#ge)for(const e of this.#ge)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#Me&&this.#$e(this.#xe,{deltaX:t,deltaY:e,fromKeyboard:!0})}#Ne(){this.#Me=!1,this.#ti(-1),this.#Ge()}_stopResizingWithKeyboard(){this.#Ne(),this.div.focus()}select(){this.makeResizable(),this.div?.classList.add("selectedEditor"),this._editToolbar?(this._editToolbar?.show(),this.#o?.toggleAltTextBadge(!1)):this.addEditToolbar().then((()=>{this.div?.classList.contains("selectedEditor")&&this._editToolbar?.show()}))}unselect(){this.#Ae?.classList.add("hidden"),this.div?.classList.remove("selectedEditor"),this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),this._editToolbar?.hide(),this.#o?.toggleAltTextBadge(!0)}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getElementForAltText(){return this.div}get contentDiv(){return this.div}get isEditing(){return this.#Ce}set isEditing(t){this.#Ce=t,this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){this.#ve=!0;const i=t/e,{style:s}=this.div;s.aspectRatio=i,s.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#De||=new Map;const{action:e}=t;let i=this.#De.get(e);return i&&clearTimeout(i),i=setTimeout((()=>{this._reportTelemetry(t),this.#De.delete(e),0===this.#De.size&&(this.#De=null)}),Vt._telemetryTimeout),void this.#De.set(e,i)}t.type||=this.editorType,this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),this.#me=!1}disable(){this.div&&(this.div.tabIndex=-1),this.#me=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){const t=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.before(e)}}else e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);return e}resetAnnotationElement(t){const{firstChild:e}=t.container;"DIV"===e?.nodeName&&e.classList.contains("annotationContent")&&e.remove()}}class Wt extends Vt{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return this.serializeDeleted()}}const qt=3285377520,Xt=4294901760,Kt=65535;class Yt{constructor(t){this.h1=t?4294967295&t:qt,this.h2=t?4294967295&t:qt}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length),i=0;for(let s=0,n=t.length;s<n;s++){const n=t.charCodeAt(s);n<=255?e[i++]=n:(e[i++]=n>>>8,e[i++]=255&n)}}else{if(!ArrayBuffer.isView(t))throw new Error("Invalid data format, must be a string or TypedArray.");e=t.slice(),i=e.byteLength}const s=i>>2,n=i-4*s,a=new Uint32Array(e.buffer,0,s);let r=0,o=0,l=this.h1,h=this.h2;const d=3432918353,c=461845907,u=11601,p=13715;for(let t=0;t<s;t++)1&t?(r=a[t],r=r*d&Xt|r*u&Kt,r=r<<15|r>>>17,r=r*c&Xt|r*p&Kt,l^=r,l=l<<13|l>>>19,l=5*l+3864292196):(o=a[t],o=o*d&Xt|o*u&Kt,o=o<<15|o>>>17,o=o*c&Xt|o*p&Kt,h^=o,h=h<<13|h>>>19,h=5*h+3864292196);switch(r=0,n){case 3:r^=e[4*s+2]<<16;case 2:r^=e[4*s+1]<<8;case 1:r^=e[4*s],r=r*d&Xt|r*u&Kt,r=r<<15|r>>>17,r=r*c&Xt|r*p&Kt,1&s?l^=r:h^=r}this.h1=l,this.h2=h}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=3981806797*t&Xt|36045*t&Kt,e=4283543511*e&Xt|(2950163797*(e<<16|t>>>16)&Xt)>>>16,t^=e>>>1,t=444984403*t&Xt|60499*t&Kt,e=3301882366*e&Xt|(3120437893*(e<<16|t>>>16)&Xt)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const Qt=Object.freeze({map:null,hash:"",transfer:void 0});class Jt{#ei=!1;#ii=null;#si=new Map;constructor(){this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const i=this.#si.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#si.get(t)}remove(t){if(this.#si.delete(t),0===this.#si.size&&this.resetModified(),"function"==typeof this.onAnnotationEditor){for(const t of this.#si.values())if(t instanceof Vt)return;this.onAnnotationEditor(null)}}setValue(t,e){const i=this.#si.get(t);let s=!1;if(void 0!==i)for(const[t,n]of Object.entries(e))i[t]!==n&&(s=!0,i[t]=n);else s=!0,this.#si.set(t,e);s&&this.#ni(),e instanceof Vt&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#si.has(t)}get size(){return this.#si.size}#ni(){this.#ei||(this.#ei=!0,"function"==typeof this.onSetModified&&this.onSetModified())}resetModified(){this.#ei&&(this.#ei=!1,"function"==typeof this.onResetModified&&this.onResetModified())}get print(){return new Zt(this)}get serializable(){if(0===this.#si.size)return Qt;const t=new Map,e=new Yt,i=[],s=Object.create(null);let n=!1;for(const[i,a]of this.#si){const r=a instanceof Vt?a.serialize(!1,s):a;r&&(t.set(i,r),e.update(`${i}:${JSON.stringify(r)}`),n||=!!r.bitmap)}if(n)for(const e of t.values())e.bitmap&&i.push(e.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:i}:Qt}get editorStats(){let t=null;const e=new Map;for(const i of this.#si.values()){if(!(i instanceof Vt))continue;const s=i.telemetryFinalData;if(!s)continue;const{type:n}=s;e.has(n)||e.set(n,Object.getPrototypeOf(i).constructor),t||=Object.create(null);const a=t[n]||=new Map;for(const[t,e]of Object.entries(s)){if("type"===t)continue;let i=a.get(t);i||(i=new Map,a.set(t,i));const s=i.get(e)??0;i.set(e,s+1)}}for(const[i,s]of e)t[i]=s.computeTelemetryFinalData(t[i]);return t}resetModifiedIds(){this.#ii=null}get modifiedIds(){if(this.#ii)return this.#ii;const t=[];for(const e of this.#si.values())e instanceof Vt&&e.annotationElementId&&e.serialize()&&t.push(e.annotationElementId);return this.#ii={ids:new Set(t),hash:t.join(",")}}[Symbol.iterator](){return this.#si.entries()}}class Zt extends Jt{#ai;constructor(t){super();const{map:e,hash:i,transfer:s}=t.serializable,n=structuredClone(e,s?{transfer:s}:null);this.#ai={map:n,hash:i,transfer:s}}get print(){G("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#ai}get modifiedIds(){return q(this,"modifiedIds",{ids:new Set,hash:""})}}class te{#ri=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),this.#ri.clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,disableFontFace:e,_inspectFont:i}){if(t&&!this.#ri.has(t.loadedName))if(j(!e,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:e,src:s,style:n}=t,a=new FontFace(e,s,n);this.addNativeFontFace(a);try{await a.load(),this.#ri.add(e),i?.(t)}catch{$(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(a)}}else G("Not implemented: loadSystemFont without the Font Loading API.")}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo)return void await this.loadSystemFont(t);if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){throw $(`Failed to load font '${e.family}': '${i}'.`),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise((e=>{const i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)}))}}get isFontLoadingAPISupported(){return q(this,"isFontLoadingAPISupported",!!this._document?.fonts)}get isSyncFontLoadingSupported(){return q(this,"isSyncFontLoadingSupported",s||st.platform.isFirefox)}_queueLoadingCallback(t){const{loadingRequests:e}=this,i={done:!1,complete:function(){for(j(!i.done,"completeRequest() cannot be called twice."),i.done=!0;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};return e.push(i),i}get _loadTestFont(){return q(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){function i(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function s(t,e,i,s){return t.substring(0,e)+s+t.substring(e+i)}let n,a;const r=this._document.createElement("canvas");r.width=1,r.height=1;const o=r.getContext("2d");let l=0;const h=`lt${Date.now()}${this.loadTestFontId++}`;let d=this._loadTestFont;d=s(d,976,h.length,h);const c=1482184792;let u=i(d,16);for(n=0,a=h.length-3;n<a;n+=4)u=u-c+i(h,n)|0;var p;n<h.length&&(u=u-c+i(h+"XXX",n)|0),d=s(d,16,4,(p=u,String.fromCharCode(p>>24&255,p>>16&255,p>>8&255,255&p)));const g=`@font-face {font-family:"${h}";src:${`url(data:font/opentype;base64,${btoa(d)});`}}`;this.insertRule(g);const m=this._document.createElement("div");m.style.visibility="hidden",m.style.width=m.style.height="10px",m.style.position="absolute",m.style.top=m.style.left="0px";for(const e of[t.loadedName,h]){const t=this._document.createElement("span");t.textContent="Hi",t.style.fontFamily=e,m.append(t)}this._document.body.append(m),function t(e,i){if(++l>30)return $("Load test font never loaded."),void i();o.font="30px "+e,o.fillText(".",0,20),o.getImageData(0,0,1,1).data[3]>0?i():setTimeout(t.bind(null,e,i))}(h,(()=>{m.remove(),e.complete()}))}}class ee{constructor(t,e=null){this.compiledGlyphs=Object.create(null);for(const e in t)this[e]=t[e];this._inspectFont=e}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});return this._inspectFont?.(this),t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${ut(this.data)});`;let e;if(this.cssFontInfo){let i=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(i+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${i}src:${t}}`}else e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;return this._inspectFont?.(this,t),e}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];const i=this.loadedName+"_path_"+e;let s;try{s=t.get(i)}catch(t){$(`getPathGenerator - ignoring character: "${t}".`)}const n=new Path2D(s||"");return this.fontExtraProperties||t.delete(i),this.compiledGlyphs[e]=n}}const ie=1,se=2,ne=1,ae=2,re=3,oe=4,le=5,he=6,de=7,ce=8;function ue(){}function pe(t){if(t instanceof tt||t instanceof Q||t instanceof K||t instanceof J||t instanceof Y)return t;switch(t instanceof Error||"object"==typeof t&&null!==t||G('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),t.name){case"AbortException":return new tt(t.message);case"InvalidPDFException":return new Q(t.message);case"PasswordException":return new K(t.message,t.code);case"ResponseException":return new J(t.message,t.status,t.missing);case"UnknownErrorException":return new Y(t.message,t.details)}return new Y(t.message,t.toString())}class ge{#oi=new AbortController;constructor(t,e,i){this.sourceName=t,this.targetName=e,this.comObj=i,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),i.addEventListener("message",this.#li.bind(this),{signal:this.#oi.signal})}#li({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream)return void this.#hi(t);if(t.callback){const e=t.callbackId,i=this.callbackCapabilities[e];if(!i)throw new Error(`Cannot resolve callback ${e}`);if(delete this.callbackCapabilities[e],t.callback===ie)i.resolve(t.data);else{if(t.callback!==se)throw new Error("Unexpected callback case");i.reject(pe(t.reason))}return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const i=this.sourceName,s=t.sourceName,n=this.comObj;Promise.try(e,t.data).then((function(e){n.postMessage({sourceName:i,targetName:s,callback:ie,callbackId:t.callbackId,data:e})}),(function(e){n.postMessage({sourceName:i,targetName:s,callback:se,callbackId:t.callbackId,reason:pe(e)})}))}else t.streamId?this.#di(t):e(t.data)}on(t,e){const i=this.actionHandler;if(i[t])throw new Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){const s=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[s]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(t){n.reject(t)}return n.promise}sendWithStream(t,e,i,s){const n=this.streamId++,a=this.sourceName,r=this.targetName,o=this.comObj;return new ReadableStream({start:i=>{const l=Promise.withResolvers();return this.streamControllers[n]={controller:i,startCall:l,pullCall:null,cancelCall:null,isClosed:!1},o.postMessage({sourceName:a,targetName:r,action:t,streamId:n,data:e,desiredSize:i.desiredSize},s),l.promise},pull:t=>{const e=Promise.withResolvers();return this.streamControllers[n].pullCall=e,o.postMessage({sourceName:a,targetName:r,stream:he,streamId:n,desiredSize:t.desiredSize}),e.promise},cancel:t=>{j(t instanceof Error,"cancel must have a valid reason");const e=Promise.withResolvers();return this.streamControllers[n].cancelCall=e,this.streamControllers[n].isClosed=!0,o.postMessage({sourceName:a,targetName:r,stream:ne,streamId:n,reason:pe(t)}),e.promise}},i)}#di(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,a=this,r=this.actionHandler[t.action],o={enqueue(t,a=1,r){if(this.isCancelled)return;const o=this.desiredSize;this.desiredSize-=a,o>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),n.postMessage({sourceName:i,targetName:s,stream:oe,streamId:e,chunk:t},r)},close(){this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:i,targetName:s,stream:re,streamId:e}),delete a.streamSinks[e])},error(t){j(t instanceof Error,"error must have a valid reason"),this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:i,targetName:s,stream:le,streamId:e,reason:pe(t)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve(),o.ready=o.sinkCapability.promise,this.streamSinks[e]=o,Promise.try(r,t.data,o).then((function(){n.postMessage({sourceName:i,targetName:s,stream:ce,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:ce,streamId:e,reason:pe(t)})}))}#hi(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,a=this.streamControllers[e],r=this.streamSinks[e];switch(t.stream){case ce:t.success?a.startCall.resolve():a.startCall.reject(pe(t.reason));break;case de:t.success?a.pullCall.resolve():a.pullCall.reject(pe(t.reason));break;case he:if(!r){n.postMessage({sourceName:i,targetName:s,stream:de,streamId:e,success:!0});break}r.desiredSize<=0&&t.desiredSize>0&&r.sinkCapability.resolve(),r.desiredSize=t.desiredSize,Promise.try(r.onPull||ue).then((function(){n.postMessage({sourceName:i,targetName:s,stream:de,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:de,streamId:e,reason:pe(t)})}));break;case oe:if(j(a,"enqueue should have stream controller"),a.isClosed)break;a.controller.enqueue(t.chunk);break;case re:if(j(a,"close should have stream controller"),a.isClosed)break;a.isClosed=!0,a.controller.close(),this.#ci(a,e);break;case le:j(a,"error should have stream controller"),a.controller.error(pe(t.reason)),this.#ci(a,e);break;case ae:t.success?a.cancelCall.resolve():a.cancelCall.reject(pe(t.reason)),this.#ci(a,e);break;case ne:if(!r)break;const o=pe(t.reason);Promise.try(r.onCancel||ue,o).then((function(){n.postMessage({sourceName:i,targetName:s,stream:ae,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:ae,streamId:e,reason:pe(t)})})),r.sinkCapability.reject(o),r.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#ci(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]),delete this.streamControllers[e]}destroy(){this.#oi?.abort(),this.#oi=null}}class me{#ui=!1;constructor({enableHWA:t=!1}){this.#ui=t}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d",{willReadFrequently:!this.#ui})}}reset(t,e,i){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||i<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=i}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){G("Abstract method `_createCanvas` called.")}}class fe extends me{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){const i=this._document.createElement("canvas");return i.width=t,i.height=e,i}}class be{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then((t=>({cMapData:t,isCompressed:this.isCompressed}))).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)}))}async _fetch(t){G("Abstract method `_fetch` called.")}}class ve extends be{async _fetch(t){const e=await mt(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):it(e)}}class Ae{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,i,s,n){return"none"}destroy(t=!1){}}class we extends Ae{#pi;#gi;#mi;#fi;#bi;#vi;#w=0;constructor({docId:t,ownerDocument:e=globalThis.document}){super(),this.#fi=t,this.#bi=e}get#_(){return this.#gi||=new Map}get#Ai(){return this.#vi||=new Map}get#wi(){if(!this.#mi){const t=this.#bi.createElement("div"),{style:e}=t;e.visibility="hidden",e.contain="strict",e.width=e.height=0,e.position="absolute",e.top=e.left=0,e.zIndex=-1;const i=this.#bi.createElementNS(pt,"svg");i.setAttribute("width",0),i.setAttribute("height",0),this.#mi=this.#bi.createElementNS(pt,"defs"),t.append(i),i.append(this.#mi),this.#bi.body.append(t)}return this.#mi}#yi(t){if(1===t.length){const e=t[0],i=new Array(256);for(let t=0;t<256;t++)i[t]=e[t]/255;const s=i.join(",");return[s,s,s]}const[e,i,s]=t,n=new Array(256),a=new Array(256),r=new Array(256);for(let t=0;t<256;t++)n[t]=e[t]/255,a[t]=i[t]/255,r[t]=s[t]/255;return[n.join(","),a.join(","),r.join(",")]}#_i(t){if(void 0===this.#pi){this.#pi="";const t=this.#bi.URL;t!==this.#bi.baseURI&&(vt(t)?$('#createUrl: ignore "data:"-URL for performance reasons.'):this.#pi=W(t,""))}return`url(${this.#pi}#${t})`}addFilter(t){if(!t)return"none";let e=this.#_.get(t);if(e)return e;const[i,s,n]=this.#yi(t),a=1===t.length?i:`${i}${s}${n}`;if(e=this.#_.get(a),e)return this.#_.set(t,e),e;const r=`g_${this.#fi}_transfer_map_${this.#w++}`,o=this.#_i(r);this.#_.set(t,o),this.#_.set(a,o);const l=this.#xi(r);return this.#Si(i,s,n,l),o}addHCMFilter(t,e){const i=`${t}-${e}`,s="base";let n=this.#Ai.get(s);if(n?.key===i)return n.url;if(n?(n.filter?.remove(),n.key=i,n.url="none",n.filter=null):(n={key:i,url:"none",filter:null},this.#Ai.set(s,n)),!t||!e)return n.url;const a=this.#Ei(t);t=at.makeHexColor(...a);const r=this.#Ei(e);if(e=at.makeHexColor(...r),this.#wi.style.color="","#000000"===t&&"#ffffff"===e||t===e)return n.url;const o=new Array(256);for(let t=0;t<=255;t++){const e=t/255;o[t]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}const l=o.join(","),h=`g_${this.#fi}_hcm_filter`,d=n.filter=this.#xi(h);this.#Si(l,l,l,d),this.#Ci(d);const c=(t,e)=>{const i=a[t]/255,s=r[t]/255,n=new Array(e+1);for(let t=0;t<=e;t++)n[t]=i+t/e*(s-i);return n.join(",")};return this.#Si(c(0,5),c(1,5),c(2,5),d),n.url=this.#_i(h),n.url}addAlphaFilter(t){let e=this.#_.get(t);if(e)return e;const[i]=this.#yi([t]),s=`alpha_${i}`;if(e=this.#_.get(s),e)return this.#_.set(t,e),e;const n=`g_${this.#fi}_alpha_map_${this.#w++}`,a=this.#_i(n);this.#_.set(t,a),this.#_.set(s,a);const r=this.#xi(n);return this.#Ti(i,r),a}addLuminosityFilter(t){let e,i,s=this.#_.get(t||"luminosity");if(s)return s;if(t?([e]=this.#yi([t]),i=`luminosity_${e}`):i="luminosity",s=this.#_.get(i),s)return this.#_.set(t,s),s;const n=`g_${this.#fi}_luminosity_map_${this.#w++}`,a=this.#_i(n);this.#_.set(t,a),this.#_.set(i,a);const r=this.#xi(n);return this.#Mi(r),t&&this.#Ti(e,r),a}addHighlightHCMFilter(t,e,i,s,n){const a=`${e}-${i}-${s}-${n}`;let r=this.#Ai.get(t);if(r?.key===a)return r.url;if(r?(r.filter?.remove(),r.key=a,r.url="none",r.filter=null):(r={key:a,url:"none",filter:null},this.#Ai.set(t,r)),!e||!i)return r.url;const[o,l]=[e,i].map(this.#Ei.bind(this));let h=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),d=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),[c,u]=[s,n].map(this.#Ei.bind(this));d<h&&([h,d,c,u]=[d,h,u,c]),this.#wi.style.color="";const p=(t,e,i)=>{const s=new Array(256),n=(d-h)/i,a=t/255,r=(e-t)/(255*i);let o=0;for(let t=0;t<=i;t++){const e=Math.round(h+t*n),i=a+t*r;for(let t=o;t<=e;t++)s[t]=i;o=e+1}for(let t=o;t<256;t++)s[t]=s[o-1];return s.join(",")},g=`g_${this.#fi}_hcm_${t}_filter`,m=r.filter=this.#xi(g);return this.#Ci(m),this.#Si(p(c[0],u[0],5),p(c[1],u[1],5),p(c[2],u[2],5),m),r.url=this.#_i(g),r.url}destroy(t=!1){t&&this.#vi?.size||(this.#mi?.parentNode.parentNode.remove(),this.#mi=null,this.#gi?.clear(),this.#gi=null,this.#vi?.clear(),this.#vi=null,this.#w=0)}#Mi(t){const e=this.#bi.createElementNS(pt,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),t.append(e)}#Ci(t){const e=this.#bi.createElementNS(pt,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),t.append(e)}#xi(t){const e=this.#bi.createElementNS(pt,"filter");return e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("id",t),this.#wi.append(e),e}#Pi(t,e,i){const s=this.#bi.createElementNS(pt,e);s.setAttribute("type","discrete"),s.setAttribute("tableValues",i),t.append(s)}#Si(t,e,i,s){const n=this.#bi.createElementNS(pt,"feComponentTransfer");s.append(n),this.#Pi(n,"feFuncR",t),this.#Pi(n,"feFuncG",e),this.#Pi(n,"feFuncB",i)}#Ti(t,e){const i=this.#bi.createElementNS(pt,"feComponentTransfer");e.append(i),this.#Pi(i,"feFuncA",t)}#Ei(t){return this.#wi.style.color=t,Mt(getComputedStyle(this.#wi).getPropertyValue("color"))}}class ye{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch((t=>{throw new Error(`Unable to load font data at: ${e}`)}))}async _fetch(t){G("Abstract method `_fetch` called.")}}class _e extends ye{async _fetch(t){const e=await mt(t,"arraybuffer");return new Uint8Array(e)}}class xe{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `wasmUrl` API parameter is provided.");if(!t)throw new Error("Wasm filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch((t=>{throw new Error(`Unable to load wasm data at: ${e}`)}))}async _fetch(t){G("Abstract method `_fetch` called.")}}class Se extends xe{async _fetch(t){const e=await mt(t,"arraybuffer");return new Uint8Array(e)}}async function Ee(t){const e=process.getBuiltinModule("fs"),i=await e.promises.readFile(t);return new Uint8Array(i)}s&&$("Please use the `legacy` build in Node.js environments.");class Ce extends Ae{}class Te extends me{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire("file:///Users/<USER>/Documents/Coding/plugins/ai-engine-pro/node_modules/pdfjs-dist/build/pdf.mjs")("@napi-rs/canvas").createCanvas(t,e)}}class Me extends be{async _fetch(t){return Ee(t)}}class Pe extends ye{async _fetch(t){return Ee(t)}}class Ie extends xe{async _fetch(t){return Ee(t)}}const ke="Fill",De="Stroke",Re="Shading";function Le(t,e){if(!e)return;const i=e[2]-e[0],s=e[3]-e[1],n=new Path2D;n.rect(e[0],e[1],i,s),t.clip(n)}class Fe{isModifyingCurrentTransform(){return!1}getPattern(){G("Abstract method `getPattern` called.")}}class Ne extends Fe{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const t of this._colorStops)e.addColorStop(t[0],t[1]);return e}getPattern(t,e,i,s){let n;if(s===De||s===ke){const a=e.current.getClippedPathBoundingBox(s,Pt(t))||[0,0,0,0],r=Math.ceil(a[2]-a[0])||1,o=Math.ceil(a[3]-a[1])||1,l=e.cachedCanvases.getCanvas("pattern",r,o),h=l.context;h.clearRect(0,0,h.canvas.width,h.canvas.height),h.beginPath(),h.rect(0,0,h.canvas.width,h.canvas.height),h.translate(-a[0],-a[1]),i=at.transform(i,[1,0,0,1,a[0],a[1]]),h.transform(...e.baseTransform),this.matrix&&h.transform(...this.matrix),Le(h,this._bbox),h.fillStyle=this._createGradient(h),h.fill(),n=t.createPattern(l.canvas,"no-repeat");const d=new DOMMatrix(i);n.setTransform(d)}else Le(t,this._bbox),n=this._createGradient(t);return n}}function Oe(t,e,i,s,n,a,r,o){const l=e.coords,h=e.colors,d=t.data,c=4*t.width;let u;l[i+1]>l[s+1]&&(u=i,i=s,s=u,u=a,a=r,r=u),l[s+1]>l[n+1]&&(u=s,s=n,n=u,u=r,r=o,o=u),l[i+1]>l[s+1]&&(u=i,i=s,s=u,u=a,a=r,r=u);const p=(l[i]+e.offsetX)*e.scaleX,g=(l[i+1]+e.offsetY)*e.scaleY,m=(l[s]+e.offsetX)*e.scaleX,f=(l[s+1]+e.offsetY)*e.scaleY,b=(l[n]+e.offsetX)*e.scaleX,v=(l[n+1]+e.offsetY)*e.scaleY;if(g>=v)return;const A=h[a],w=h[a+1],y=h[a+2],_=h[r],x=h[r+1],S=h[r+2],E=h[o],C=h[o+1],T=h[o+2],M=Math.round(g),P=Math.round(v);let I,k,D,R,L,F,N,O;for(let t=M;t<=P;t++){if(t<f){const e=t<g?0:(g-t)/(g-f);I=p-(p-m)*e,k=A-(A-_)*e,D=w-(w-x)*e,R=y-(y-S)*e}else{let e;e=t>v?1:f===v?0:(f-t)/(f-v),I=m-(m-b)*e,k=_-(_-E)*e,D=x-(x-C)*e,R=S-(S-T)*e}let e;e=t<g?0:t>v?1:(g-t)/(g-v),L=p-(p-b)*e,F=A-(A-E)*e,N=w-(w-C)*e,O=y-(y-T)*e;const i=Math.round(Math.min(I,L)),s=Math.round(Math.max(I,L));let n=c*t+4*i;for(let t=i;t<=s;t++)e=(I-t)/(I-L),e<0?e=0:e>1&&(e=1),d[n++]=k-(k-F)*e|0,d[n++]=D-(D-N)*e|0,d[n++]=R-(R-O)*e|0,d[n++]=255}}function Be(t,e,i){const s=e.coords,n=e.colors;let a,r;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(s.length/o)-1,h=o-1;for(a=0;a<l;a++){let e=a*o;for(let a=0;a<h;a++,e++)Oe(t,i,s[e],s[e+1],s[e+o],n[e],n[e+1],n[e+o]),Oe(t,i,s[e+o+1],s[e+1],s[e+o],n[e+o+1],n[e+1],n[e+o])}break;case"triangles":for(a=0,r=s.length;a<r;a+=3)Oe(t,i,s[a],s[a+1],s[a+2],n[a],n[a+1],n[a+2]);break;default:throw new Error("illegal figure")}}class He extends Fe{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[6],this._background=t[7],this.matrix=null}_createMeshCanvas(t,e,i){const s=Math.floor(this._bounds[0]),n=Math.floor(this._bounds[1]),a=Math.ceil(this._bounds[2])-s,r=Math.ceil(this._bounds[3])-n,o=Math.min(Math.ceil(Math.abs(a*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(r*t[1]*1.1)),3e3),h=a/o,d=r/l,c={coords:this._coords,colors:this._colors,offsetX:-s,offsetY:-n,scaleX:1/h,scaleY:1/d},u=o+4,p=l+4,g=i.getCanvas("mesh",u,p),m=g.context,f=m.createImageData(o,l);if(e){const t=f.data;for(let i=0,s=t.length;i<s;i+=4)t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=255}for(const t of this._figures)Be(f,t,c);m.putImageData(f,2,2);return{canvas:g.canvas,offsetX:s-2*h,offsetY:n-2*d,scaleX:h,scaleY:d}}isModifyingCurrentTransform(){return!0}getPattern(t,e,i,s){Le(t,this._bbox);const n=new Float32Array(2);if(s===Re)at.singularValueDecompose2dScale(Pt(t),n);else if(this.matrix){at.singularValueDecompose2dScale(this.matrix,n);const[t,i]=n;at.singularValueDecompose2dScale(e.baseTransform,n),n[0]*=t,n[1]*=i}else at.singularValueDecompose2dScale(e.baseTransform,n);const a=this._createMeshCanvas(n,s===Re?null:this._background,e.cachedCanvases);return s!==Re&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(a.offsetX,a.offsetY),t.scale(a.scaleX,a.scaleY),t.createPattern(a.canvas,"no-repeat")}}class ze extends Fe{getPattern(){return"hotpink"}}const Ue=1,$e=2;class Ge{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,s){this.color=t[1],this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.ctx=e,this.canvasGraphicsFactory=i,this.baseTransform=s}createPatternCanvas(t){const{bbox:e,operatorList:i,paintType:s,tilingType:n,color:a,canvasGraphicsFactory:r}=this;let{xstep:o,ystep:l}=this;o=Math.abs(o),l=Math.abs(l),U("TilingType: "+n);const h=e[0],d=e[1],c=e[2],u=e[3],p=c-h,g=u-d,m=new Float32Array(2);at.singularValueDecompose2dScale(this.matrix,m);const[f,b]=m;at.singularValueDecompose2dScale(this.baseTransform,m);const v=f*m[0],A=b*m[1];let w=p,y=g,_=!1,x=!1;const S=Math.ceil(o*v),E=Math.ceil(l*A);S>=Math.ceil(p*v)?w=o:_=!0,E>=Math.ceil(g*A)?y=l:x=!0;const C=this.getSizeAndScale(w,this.ctx.canvas.width,v),T=this.getSizeAndScale(y,this.ctx.canvas.height,A),M=t.cachedCanvases.getCanvas("pattern",C.size,T.size),P=M.context,I=r.createCanvasGraphics(P);if(I.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(I,s,a),P.translate(-C.scale*h,-T.scale*d),I.transform(C.scale,0,0,T.scale,0,0),P.save(),this.clipBbox(I,h,d,c,u),I.baseTransform=Pt(I.ctx),I.executeOperatorList(i),I.endDrawing(),P.restore(),_||x){const e=M.canvas;_&&(w=o),x&&(y=l);const i=this.getSizeAndScale(w,this.ctx.canvas.width,v),s=this.getSizeAndScale(y,this.ctx.canvas.height,A),n=i.size,a=s.size,r=t.cachedCanvases.getCanvas("pattern-workaround",n,a),c=r.context,u=_?Math.floor(p/o):0,m=x?Math.floor(g/l):0;for(let t=0;t<=u;t++)for(let i=0;i<=m;i++)c.drawImage(e,n*t,a*i,n,a,0,0,n,a);return{canvas:r.canvas,scaleX:i.scale,scaleY:s.scale,offsetX:h,offsetY:d}}return{canvas:M.canvas,scaleX:C.scale,scaleY:T.scale,offsetX:h,offsetY:d}}getSizeAndScale(t,e,i){const s=Math.max(Ge.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*i);return n>=s?n=s:i=n/t,{scale:i,size:n}}clipBbox(t,e,i,s,n){const a=s-e,r=n-i;t.ctx.rect(e,i,a,r),at.axialAlignedBoundingBox([e,i,s,n],Pt(t.ctx),t.current.minMax),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,i){const s=t.ctx,n=t.current;switch(e){case Ue:const t=this.ctx;s.fillStyle=t.fillStyle,s.strokeStyle=t.strokeStyle,n.fillColor=t.fillStyle,n.strokeColor=t.strokeStyle;break;case $e:const a=at.makeHexColor(i[0],i[1],i[2]);s.fillStyle=a,s.strokeStyle=a,n.fillColor=a,n.strokeColor=a;break;default:throw new Z(`Unsupported paint type: ${e}`)}}isModifyingCurrentTransform(){return!1}getPattern(t,e,i,s){let n=i;s!==Re&&(n=at.transform(n,e.baseTransform),this.matrix&&(n=at.transform(n,this.matrix)));const a=this.createPatternCanvas(e);let r=new DOMMatrix(n);r=r.translate(a.offsetX,a.offsetY),r=r.scale(1/a.scaleX,1/a.scaleY);const o=t.createPattern(a.canvas,"repeat");return o.setTransform(r),o}}function je({src:t,srcPos:e=0,dest:i,width:s,height:n,nonBlackColor:a=4294967295,inverseDecode:r=!1}){const o=st.isLittleEndian?4278190080:255,[l,h]=r?[a,o]:[o,a],d=s>>3,c=7&s,u=t.length;i=new Uint32Array(i.buffer);let p=0;for(let s=0;s<n;s++){for(const s=e+d;e<s;e++){const s=e<u?t[e]:255;i[p++]=128&s?h:l,i[p++]=64&s?h:l,i[p++]=32&s?h:l,i[p++]=16&s?h:l,i[p++]=8&s?h:l,i[p++]=4&s?h:l,i[p++]=2&s?h:l,i[p++]=1&s?h:l}if(0===c)continue;const s=e<u?t[e++]:255;for(let t=0;t<c;t++)i[p++]=s&1<<7-t?h:l}return{srcPos:e,destPos:p}}const Ve=16,We=new DOMMatrix,qe=new Float32Array(2),Xe=new Float32Array([1/0,1/0,-1/0,-1/0]);class Ke{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,i){let s;return void 0!==this.cache[t]?(s=this.cache[t],this.canvasFactory.reset(s,e,i)):(s=this.canvasFactory.create(e,i),this.cache[t]=s),s}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function Ye(t,e,i,s,n,a,r,o,l,h){const[d,c,u,p,g,m]=Pt(t);if(0===c&&0===u){const f=r*d+g,b=Math.round(f),v=o*p+m,A=Math.round(v),w=(r+l)*d+g,y=Math.abs(Math.round(w)-b)||1,_=(o+h)*p+m,x=Math.abs(Math.round(_)-A)||1;return t.setTransform(Math.sign(d),0,0,Math.sign(p),b,A),t.drawImage(e,i,s,n,a,0,0,y,x),t.setTransform(d,c,u,p,g,m),[y,x]}if(0===d&&0===p){const f=o*u+g,b=Math.round(f),v=r*c+m,A=Math.round(v),w=(o+h)*u+g,y=Math.abs(Math.round(w)-b)||1,_=(r+l)*c+m,x=Math.abs(Math.round(_)-A)||1;return t.setTransform(0,Math.sign(c),Math.sign(u),0,b,A),t.drawImage(e,i,s,n,a,0,0,x,y),t.setTransform(d,c,u,p,g,m),[x,y]}t.drawImage(e,i,s,n,a,r,o,l,h);return[Math.hypot(d,c)*l,Math.hypot(u,p)*h]}class Qe{alphaIsShape=!1;fontSize=0;fontSizeScale=1;textMatrix=null;textMatrixScale=1;fontMatrix=n;leading=0;x=0;y=0;lineX=0;lineY=0;charSpacing=0;wordSpacing=0;textHScale=1;textRenderingMode=v;textRise=0;fillColor="#000000";strokeColor="#000000";patternFill=!1;patternStroke=!1;fillAlpha=1;strokeAlpha=1;lineWidth=1;activeSMask=null;transferMaps="none";constructor(t,e){this.clipBox=new Float32Array([0,0,t,e]),this.minMax=Xe.slice()}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t.minMax=this.minMax.slice(),t}getPathBoundingBox(t=ke,e=null){const i=this.minMax.slice();if(t===De){e||G("Stroke bounding box must include transform."),at.singularValueDecompose2dScale(e,qe);const t=qe[0]*this.lineWidth/2,s=qe[1]*this.lineWidth/2;i[0]-=t,i[1]-=s,i[2]+=t,i[3]+=s}return i}updateClipFromPath(){const t=at.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minMax[0]===1/0}startNewPathAndClipBox(t){this.clipBox.set(t,0),this.minMax.set(Xe,0)}getClippedPathBoundingBox(t=ke,e=null){return at.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function Je(t,e){if(e instanceof ImageData)return void t.putImageData(e,0,0);const i=e.height,s=e.width,n=i%Ve,a=(i-n)/Ve,r=0===n?a:a+1,o=t.createImageData(s,Ve);let l,h=0;const d=e.data,c=o.data;let u,p,g,m;if(e.kind===S.GRAYSCALE_1BPP){const e=d.byteLength,i=new Uint32Array(c.buffer,0,c.byteLength>>2),m=i.length,f=s+7>>3,b=4294967295,v=st.isLittleEndian?4278190080:255;for(u=0;u<r;u++){for(g=u<a?Ve:n,l=0,p=0;p<g;p++){const t=e-h;let n=0;const a=t>f?s:8*t-7,r=-8&a;let o=0,c=0;for(;n<r;n+=8)c=d[h++],i[l++]=128&c?b:v,i[l++]=64&c?b:v,i[l++]=32&c?b:v,i[l++]=16&c?b:v,i[l++]=8&c?b:v,i[l++]=4&c?b:v,i[l++]=2&c?b:v,i[l++]=1&c?b:v;for(;n<a;n++)0===o&&(c=d[h++],o=128),i[l++]=c&o?b:v,o>>=1}for(;l<m;)i[l++]=0;t.putImageData(o,0,u*Ve)}}else if(e.kind===S.RGBA_32BPP){for(p=0,m=s*Ve*4,u=0;u<a;u++)c.set(d.subarray(h,h+m)),h+=m,t.putImageData(o,0,p),p+=Ve;u<r&&(m=s*n*4,c.set(d.subarray(h,h+m)),t.putImageData(o,0,p))}else{if(e.kind!==S.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);for(g=Ve,m=s*g,u=0;u<r;u++){for(u>=a&&(g=n,m=s*g),l=0,p=m;p--;)c[l++]=d[h++],c[l++]=d[h++],c[l++]=d[h++],c[l++]=255;t.putImageData(o,0,u*Ve)}}}function Ze(t,e){if(e.bitmap)return void t.drawImage(e.bitmap,0,0);const i=e.height,s=e.width,n=i%Ve,a=(i-n)/Ve,r=0===n?a:a+1,o=t.createImageData(s,Ve);let l=0;const h=e.data,d=o.data;for(let e=0;e<r;e++){const i=e<a?Ve:n;({srcPos:l}=je({src:h,srcPos:l,dest:d,width:s,height:i,nonBlackColor:0})),t.putImageData(o,0,e*Ve)}}function ti(t,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of i)void 0!==t[s]&&(e[s]=t[s]);void 0!==t.setLineDash&&(e.setLineDash(t.getLineDash()),e.lineDashOffset=t.lineDashOffset)}function ei(t){t.strokeStyle=t.fillStyle="#000000",t.fillRule="nonzero",t.globalAlpha=1,t.lineWidth=1,t.lineCap="butt",t.lineJoin="miter",t.miterLimit=10,t.globalCompositeOperation="source-over",t.font="10px sans-serif",void 0!==t.setLineDash&&(t.setLineDash([]),t.lineDashOffset=0);const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}function ii(t,e){if(e)return!0;at.singularValueDecompose2dScale(t,qe);const i=Math.fround(Dt.pixelRatio*gt.PDF_TO_CSS_UNITS);return qe[0]<=i&&qe[1]<=i}const si=["butt","round","square"],ni=["miter","round","bevel"],ai={},ri={};class oi{constructor(t,e,i,s,n,{optionalContentConfig:a,markedContentStack:r=null},o,l){this.ctx=t,this.current=new Qe(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=i,this.canvasFactory=s,this.filterFactory=n,this.groupStack=[],this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=r||[],this.optionalContentConfig=a,this.cachedCanvases=new Ke(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=o,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=l,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:i=!1,background:s=null}){const n=this.ctx.canvas.width,a=this.ctx.canvas.height,r=this.ctx.fillStyle;if(this.ctx.fillStyle=s||"#ffffff",this.ctx.fillRect(0,0,n,a),this.ctx.fillStyle=r,i){const t=this.cachedCanvases.getCanvas("transparent",n,a);this.compositeCtx=this.ctx,this.transparentCanvas=t.canvas,this.ctx=t.context,this.ctx.save(),this.ctx.transform(...Pt(this.compositeCtx))}this.ctx.save(),ei(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=Pt(this.ctx)}executeOperatorList(t,e,i,s){const n=t.argsArray,a=t.fnArray;let r=e||0;const o=n.length;if(o===r)return r;const l=o-r>10&&"function"==typeof i,h=l?Date.now()+15:0;let d=0;const c=this.commonObjs,u=this.objs;let p;for(;;){if(void 0!==s&&r===s.nextBreakPoint)return s.breakIt(r,i),r;if(p=a[r],p!==D.dependency)this[p].apply(this,n[r]);else for(const t of n[r]){const e=t.startsWith("g_")?c:u;if(!e.has(t))return e.get(t,i),r}if(r++,r===o)return r;if(l&&++d>10){if(Date.now()>h)return i(),r;d=0}}}#Ii(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null,this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)}endDrawing(){this.#Ii(),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),this.#ki()}#ki(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}}_scaleImage(t,e){const i=t.width??t.displayWidth,s=t.height??t.displayHeight;let n,a,r=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=i,h=s,d="prescale1";for(;r>2&&l>1||o>2&&h>1;){let e=l,i=h;r>2&&l>1&&(e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2),r/=l/e),o>2&&h>1&&(i=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h)/2,o/=h/i),n=this.cachedCanvases.getCanvas(d,e,i),a=n.context,a.clearRect(0,0,e,i),a.drawImage(t,0,0,l,h,0,0,e,i),t=n.canvas,l=e,h=i,d="prescale1"===d?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:h}}_createMaskCanvas(t){const e=this.ctx,{width:i,height:s}=t,n=this.current.fillColor,a=this.current.patternFill,r=Pt(e);let o,l,h,d;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;l=JSON.stringify(a?r:[r.slice(0,4),n]),o=this._cachedBitmapsMap.get(e),o||(o=new Map,this._cachedBitmapsMap.set(e,o));const i=o.get(l);if(i&&!a){return{canvas:i,offsetX:Math.round(Math.min(r[0],r[2])+r[4]),offsetY:Math.round(Math.min(r[1],r[3])+r[5])}}h=i}h||(d=this.cachedCanvases.getCanvas("maskCanvas",i,s),Ze(d.context,t));let c=at.transform(r,[1/i,0,0,-1/s,0,0]);c=at.transform(c,[1,0,0,1,0,-s]);const u=Xe.slice();at.axialAlignedBoundingBox([0,0,i,s],c,u);const[p,g,m,f]=u,b=Math.round(m-p)||1,v=Math.round(f-g)||1,A=this.cachedCanvases.getCanvas("fillCanvas",b,v),w=A.context,y=p,_=g;w.translate(-y,-_),w.transform(...c),h||(h=this._scaleImage(d.canvas,It(w)),h=h.img,o&&a&&o.set(l,h)),w.imageSmoothingEnabled=ii(Pt(w),t.interpolate),Ye(w,h,0,0,h.width,h.height,0,0,i,s),w.globalCompositeOperation="source-in";const x=at.transform(It(w),[1,0,0,1,-y,-_]);return w.fillStyle=a?n.getPattern(e,this,x,ke):n,w.fillRect(0,0,i,s),o&&!a&&(this.cachedCanvases.delete("fillCanvas"),o.set(l,A.canvas)),{canvas:A.canvas,offsetX:Math.round(y),offsetY:Math.round(_)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=si[t]}setLineJoin(t){this.ctx.lineJoin=ni[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const i=this.ctx;void 0!==i.setLineDash&&(i.setLineDash(t),i.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.ctx.globalAlpha=this.current.fillAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,s=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx;const n=this.ctx=s.context;n.setTransform(this.suspendedCtx.getTransform()),ti(this.suspendedCtx,n),function(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save,t.__originalRestore=t.restore,t.__originalRotate=t.rotate,t.__originalScale=t.scale,t.__originalTranslate=t.translate,t.__originalTransform=t.transform,t.__originalSetTransform=t.setTransform,t.__originalResetTransform=t.resetTransform,t.__originalClip=t.clip,t.__originalMoveTo=t.moveTo,t.__originalLineTo=t.lineTo,t.__originalBezierCurveTo=t.bezierCurveTo,t.__originalRect=t.rect,t.__originalClosePath=t.closePath,t.__originalBeginPath=t.beginPath,t._removeMirroring=()=>{t.save=t.__originalSave,t.restore=t.__originalRestore,t.rotate=t.__originalRotate,t.scale=t.__originalScale,t.translate=t.__originalTranslate,t.transform=t.__originalTransform,t.setTransform=t.__originalSetTransform,t.resetTransform=t.__originalResetTransform,t.clip=t.__originalClip,t.moveTo=t.__originalMoveTo,t.lineTo=t.__originalLineTo,t.bezierCurveTo=t.__originalBezierCurveTo,t.rect=t.__originalRect,t.closePath=t.__originalClosePath,t.beginPath=t.__originalBeginPath,delete t._removeMirroring},t.save=function(){e.save(),this.__originalSave()},t.restore=function(){e.restore(),this.__originalRestore()},t.translate=function(t,i){e.translate(t,i),this.__originalTranslate(t,i)},t.scale=function(t,i){e.scale(t,i),this.__originalScale(t,i)},t.transform=function(t,i,s,n,a,r){e.transform(t,i,s,n,a,r),this.__originalTransform(t,i,s,n,a,r)},t.setTransform=function(t,i,s,n,a,r){e.setTransform(t,i,s,n,a,r),this.__originalSetTransform(t,i,s,n,a,r)},t.resetTransform=function(){e.resetTransform(),this.__originalResetTransform()},t.rotate=function(t){e.rotate(t),this.__originalRotate(t)},t.clip=function(t){e.clip(t),this.__originalClip(t)},t.moveTo=function(t,i){e.moveTo(t,i),this.__originalMoveTo(t,i)},t.lineTo=function(t,i){e.lineTo(t,i),this.__originalLineTo(t,i)},t.bezierCurveTo=function(t,i,s,n,a,r){e.bezierCurveTo(t,i,s,n,a,r),this.__originalBezierCurveTo(t,i,s,n,a,r)},t.rect=function(t,i,s,n){e.rect(t,i,s,n),this.__originalRect(t,i,s,n)},t.closePath=function(){e.closePath(),this.__originalClosePath()},t.beginPath=function(){e.beginPath(),this.__originalBeginPath()}}(n,this.suspendedCtx),this.setGState([["BM","source-over"]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),ti(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,i=this.suspendedCtx;this.composeSMask(i,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,i,s){const n=s[0],a=s[1],r=s[2]-n,o=s[3]-a;0!==r&&0!==o&&(this.genericComposeSMask(e.context,i,r,o,e.subtype,e.backdrop,e.transferMap,n,a,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(i.canvas,0,0),t.restore())}genericComposeSMask(t,e,i,s,n,a,r,o,l,h,d){let c=t.canvas,u=o-h,p=l-d;if(a){const e=at.makeHexColor(...a);if(u<0||p<0||u+i>c.width||p+s>c.height){const t=this.cachedCanvases.getCanvas("maskExtension",i,s),n=t.context;n.drawImage(c,-u,-p),n.globalCompositeOperation="destination-atop",n.fillStyle=e,n.fillRect(0,0,i,s),n.globalCompositeOperation="source-over",c=t.canvas,u=p=0}else{t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const n=new Path2D;n.rect(u,p,i,s),t.clip(n),t.globalCompositeOperation="destination-atop",t.fillStyle=e,t.fillRect(u,p,i,s),t.restore()}}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),"Alpha"===n&&r?e.filter=this.filterFactory.addAlphaFilter(r):"Luminosity"===n&&(e.filter=this.filterFactory.addLuminosityFilter(r));const g=new Path2D;g.rect(o,l,i,s),e.clip(g),e.globalCompositeOperation="destination-in",e.drawImage(c,u,p,i,s,o,l,i,s),e.restore()}save(){this.inSMaskMode&&ti(this.ctx,this.suspendedCtx),this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){0!==this.stateStack.length?(this.current=this.stateStack.pop(),this.ctx.restore(),this.inSMaskMode&&ti(this.suspendedCtx,this.ctx),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null):this.inSMaskMode&&this.endSMaskMode()}transform(t,e,i,s,n,a){this.ctx.transform(t,e,i,s,n,a),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){let[s]=e;if(!i)return s||=e[0]=new Path2D,void this[t](s);if(!(s instanceof Path2D)){const t=e[0]=new Path2D;for(let e=0,i=s.length;e<i;)switch(s[e++]){case R:t.moveTo(s[e++],s[e++]);break;case L:t.lineTo(s[e++],s[e++]);break;case F:t.bezierCurveTo(s[e++],s[e++],s[e++],s[e++],s[e++],s[e++]);break;case N:t.closePath();break;default:$(`Unrecognized drawing path operator: ${s[e-1]}`)}s=t}at.axialAlignedBoundingBox(i,Pt(this.ctx),this.current.minMax),this[t](s)}closePath(){this.ctx.closePath()}stroke(t,e=!0){const i=this.ctx,s=this.current.strokeColor;if(i.globalAlpha=this.current.strokeAlpha,this.contentVisible)if("object"==typeof s&&s?.getPattern){const e=s.isModifyingCurrentTransform()?i.getTransform():null;if(i.save(),i.strokeStyle=s.getPattern(i,this,It(i),De),e){const s=new Path2D;s.addPath(t,i.getTransform().invertSelf().multiplySelf(e)),t=s}this.rescaleAndStroke(t,!1),i.restore()}else this.rescaleAndStroke(t,!0);e&&this.consumePath(t,this.current.getClippedPathBoundingBox(De,Pt(this.ctx))),i.globalAlpha=this.current.fillAlpha}closeStroke(t){this.stroke(t)}fill(t,e=!0){const i=this.ctx,s=this.current.fillColor;let n=!1;if(this.current.patternFill){const e=s.isModifyingCurrentTransform()?i.getTransform():null;if(i.save(),i.fillStyle=s.getPattern(i,this,It(i),ke),e){const s=new Path2D;s.addPath(t,i.getTransform().invertSelf().multiplySelf(e)),t=s}n=!0}const a=this.current.getClippedPathBoundingBox();this.contentVisible&&null!==a&&(this.pendingEOFill?(i.fill(t,"evenodd"),this.pendingEOFill=!1):i.fill(t)),n&&i.restore(),e&&this.consumePath(t,a)}eoFill(t){this.pendingEOFill=!0,this.fill(t)}fillStroke(t){this.fill(t,!1),this.stroke(t,!1),this.consumePath(t)}eoFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}closeFillStroke(t){this.fillStroke(t)}closeEOFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}endPath(t){this.consumePath(t)}rawFillPath(t){this.ctx.fill(t)}clip(){this.pendingClip=ai}eoClip(){this.pendingClip=ri}beginText(){this.current.textMatrix=null,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0===t)return;const i=new Path2D,s=e.getTransform().invertSelf();for(const{transform:e,x:n,y:a,fontSize:r,path:o}of t)i.addPath(o,new DOMMatrix(e).preMultiplySelf(s).translate(n,a).scale(r,-r));e.clip(i),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const i=this.commonObjs.get(t),s=this.current;if(!i)throw new Error(`Can't find font for ${t}`);if(s.fontMatrix=i.fontMatrix||n,0!==s.fontMatrix[0]&&0!==s.fontMatrix[3]||$("Invalid font matrix for font "+t),e<0?(e=-e,s.fontDirection=-1):s.fontDirection=1,this.current.font=i,this.current.fontSize=e,i.isType3Font)return;const a=i.loadedName||"sans-serif",r=i.systemFontInfo?.css||`"${a}", ${i.fallbackName}`;let o="normal";i.black?o="900":i.bold&&(o="bold");const l=i.italic?"italic":"normal";let h=e;e<16?h=16:e>100&&(h=100),this.current.fontSizeScale=e/h,this.ctx.font=`${l} ${o} ${h}px ${r}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t){const{current:e}=this;e.textMatrix=t,e.textMatrixScale=Math.hypot(t[0],t[1]),e.x=e.lineX=0,e.y=e.lineY=0}nextLine(){this.moveText(0,this.current.leading)}#Di(t,e,i){const s=new Path2D;return s.addPath(t,new DOMMatrix(i).invertSelf().multiplySelf(e)),s}paintChar(t,e,i,s,n){const a=this.ctx,r=this.current,o=r.font,l=r.textRenderingMode,h=r.fontSize/r.fontSizeScale,d=l&_,c=!!(l&x),u=r.patternFill&&!o.missingFile,p=r.patternStroke&&!o.missingFile;let g;if((o.disableFontFace||c||u||p)&&(g=o.getPathGenerator(this.commonObjs,t)),o.disableFontFace||u||p){let t;if(a.save(),a.translate(e,i),a.scale(h,-h),d!==v&&d!==w||(s?(t=a.getTransform(),a.setTransform(...s),a.fill(this.#Di(g,t,s))):a.fill(g)),d===A||d===w)if(n){t||=a.getTransform(),a.setTransform(...n);const{a:e,b:i,c:s,d:r}=t,o=at.inverseTransform(n),l=at.transform([e,i,s,r,0,0],o);at.singularValueDecompose2dScale(l,qe),a.lineWidth*=Math.max(qe[0],qe[1])/h,a.stroke(this.#Di(g,t,n))}else a.lineWidth/=h,a.stroke(g);a.restore()}else d!==v&&d!==w||a.fillText(t,e,i),d!==A&&d!==w||a.strokeText(t,e,i);if(c){(this.pendingTextPaths||=[]).push({transform:Pt(a),x:e,y:i,fontSize:h,path:g})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let i=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){i=!0;break}return q(this,"isFontSubpixelAAEnabled",i)}showText(t){const e=this.current,i=e.font;if(i.isType3Font)return this.showType3Text(t);const s=e.fontSize;if(0===s)return;const n=this.ctx,a=e.fontSizeScale,r=e.charSpacing,o=e.wordSpacing,l=e.fontDirection,h=e.textHScale*l,d=t.length,c=i.vertical,u=c?1:-1,p=i.defaultVMetrics,g=s*e.fontMatrix[0],m=e.textRenderingMode===v&&!i.disableFontFace&&!e.patternFill;let f,b;if(n.save(),e.textMatrix&&n.transform(...e.textMatrix),n.translate(e.x,e.y+e.textRise),l>0?n.scale(h,-1):n.scale(h,1),e.patternFill){n.save();const t=e.fillColor.getPattern(n,this,It(n),ke);f=Pt(n),n.restore(),n.fillStyle=t}if(e.patternStroke){n.save();const t=e.strokeColor.getPattern(n,this,It(n),De);b=Pt(n),n.restore(),n.strokeStyle=t}let y=e.lineWidth;const x=e.textMatrixScale;if(0===x||0===y){const t=e.textRenderingMode&_;t!==A&&t!==w||(y=this.getSinglePixelWidth())}else y/=x;if(1!==a&&(n.scale(a,a),y/=a),n.lineWidth=y,i.isInvalidPDFjsFont){const i=[];let s=0;for(const e of t)i.push(e.unicode),s+=e.width;return n.fillText(i.join(""),0,0),e.x+=s*g*h,n.restore(),void this.compose()}let S,E=0;for(S=0;S<d;++S){const e=t[S];if("number"==typeof e){E+=u*e*s/1e3;continue}let h=!1;const d=(e.isSpace?o:0)+r,v=e.fontChar,A=e.accent;let w,y,_=e.width;if(c){const t=e.vmetric||p,i=-(e.vmetric?t[1]:.5*_)*g,s=t[2]*g;_=t?-t[0]:_,w=i/a,y=(E+s)/a}else w=E/a,y=0;if(i.remeasure&&_>0){const t=1e3*n.measureText(v).width/s*a;if(_<t&&this.isFontSubpixelAAEnabled){const e=_/t;h=!0,n.save(),n.scale(e,1),w/=e}else _!==t&&(w+=(_-t)/2e3*s/a)}if(this.contentVisible&&(e.isInFont||i.missingFile))if(m&&!A)n.fillText(v,w,y);else if(this.paintChar(v,w,y,f,b),A){const t=w+s*A.offset.x/a,e=y-s*A.offset.y/a;this.paintChar(A.fontChar,t,e,f,b)}E+=c?_*g-d*l:_*g+d*l,h&&n.restore()}c?e.y-=E:e.x+=E*h,n.restore(),this.compose()}showType3Text(t){const e=this.ctx,i=this.current,s=i.font,a=i.fontSize,r=i.fontDirection,o=s.vertical?1:-1,l=i.charSpacing,h=i.wordSpacing,d=i.textHScale*r,c=i.fontMatrix||n,u=t.length;let p,g,m,f;if(!(i.textRenderingMode===y)&&0!==a){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),i.textMatrix&&e.transform(...i.textMatrix),e.translate(i.x,i.y+i.textRise),e.scale(d,r),p=0;p<u;++p){if(g=t[p],"number"==typeof g){f=o*g*a/1e3,this.ctx.translate(f,0),i.x+=f*d;continue}const n=(g.isSpace?h:0)+l,r=s.charProcOperatorList[g.operatorListId];r?this.contentVisible&&(this.save(),e.scale(a,a),e.transform(...c),this.executeOperatorList(r),this.restore()):$(`Type3 character "${g.operatorListId}" is not available.`);const u=[g.width,0];at.applyTransform(u,c),m=u[0]*a+n,e.translate(m,0),i.x+=m*d}e.restore()}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,s,n,a){const r=new Path2D;r.rect(i,s,n-i,a-s),this.ctx.clip(r),this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const i=this.baseTransform||Pt(this.ctx),s={createCanvasGraphics:t=>new oi(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new Ge(t,this.ctx,s,i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments),this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t,e,i){this.ctx.strokeStyle=this.current.strokeColor=at.makeHexColor(t,e,i),this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent",this.current.patternStroke=!1}setFillRGBColor(t,e,i){this.ctx.fillStyle=this.current.fillColor=at.makeHexColor(t,e,i),this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent",this.current.patternFill=!1}_getPattern(t,e=null){let i;return this.cachedPatterns.has(t)?i=this.cachedPatterns.get(t):(i=function(t){switch(t[0]){case"RadialAxial":return new Ne(t);case"Mesh":return new He(t);case"Dummy":return new ze}throw new Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t)),this.cachedPatterns.set(t,i)),e&&(i.matrix=e),i}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const i=this._getPattern(t);e.fillStyle=i.getPattern(e,this,It(e),Re);const s=It(e);if(s){const{width:t,height:i}=e.canvas,n=Xe.slice();at.axialAlignedBoundingBox([0,0,t,i],s,n);const[a,r,o,l]=n;this.ctx.fillRect(a,r,o-a,l-r)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){G("Should not call beginInlineImage")}beginImageData(){G("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=Pt(this.ctx),e)){at.axialAlignedBoundingBox(e,this.baseTransform,this.current.minMax);const[t,i,s,n]=e,a=new Path2D;a.rect(t,i,s-t,n-i),this.ctx.clip(a),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||U("TODO: Support non-isolated groups."),t.knockout&&$("Knockout groups not supported.");const i=Pt(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let s=Xe.slice();at.axialAlignedBoundingBox(t.bbox,Pt(e),s);const n=[0,0,e.canvas.width,e.canvas.height];s=at.intersect(s,n)||[0,0,0,0];const a=Math.floor(s[0]),r=Math.floor(s[1]),o=Math.max(Math.ceil(s[2])-a,1),l=Math.max(Math.ceil(s[3])-r,1);this.current.startNewPathAndClipBox([0,0,o,l]);let h="groupAt"+this.groupLevel;t.smask&&(h+="_smask_"+this.smaskCounter++%2);const d=this.cachedCanvases.getCanvas(h,o,l),c=d.context;c.translate(-a,-r),c.transform(...i);let u=new Path2D;const[p,g,m,f]=t.bbox;if(u.rect(p,g,m-p,f-g),t.matrix){const e=new Path2D;e.addPath(u,new DOMMatrix(t.matrix)),u=e}c.clip(u),t.smask?this.smaskStack.push({canvas:d.canvas,context:c,offsetX:a,offsetY:r,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(a,r),e.save()),ti(e,c),this.ctx=c,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,i=this.groupStack.pop();if(this.ctx=i,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const t=Pt(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...t);const i=Xe.slice();at.axialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t,i),this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(i)}}beginAnnotation(t,e,i,s,n){if(this.#Ii(),ei(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const s=e[2]-e[0],a=e[3]-e[1];if(n&&this.annotationCanvasMap){(i=i.slice())[4]-=e[0],i[5]-=e[1],(e=e.slice())[0]=e[1]=0,e[2]=s,e[3]=a,at.singularValueDecompose2dScale(Pt(this.ctx),qe);const{viewportScale:n}=this,r=Math.ceil(s*this.outputScaleX*n),o=Math.ceil(a*this.outputScaleY*n);this.annotationCanvas=this.canvasFactory.create(r,o);const{canvas:l,context:h}=this.annotationCanvas;this.annotationCanvasMap.set(t,l),this.annotationCanvas.savedCtx=this.ctx,this.ctx=h,this.ctx.save(),this.ctx.setTransform(qe[0],0,0,-qe[1],0,a*qe[1]),ei(this.ctx)}else{ei(this.ctx),this.endPath();const t=new Path2D;t.rect(e[0],e[1],s,a),this.ctx.clip(t)}}this.current=new Qe(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...i),this.transform(...s)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),this.#ki(),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const i=this.ctx,s=this._createMaskCanvas(t),n=s.canvas;i.save(),i.setTransform(1,0,0,1,0,0),i.drawImage(n,s.offsetX,s.offsetY),i.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,i=0,s=0,n,a){if(!this.contentVisible)return;t=this.getObject(t.data,t);const r=this.ctx;r.save();const o=Pt(r);r.transform(e,i,s,n,0,0);const l=this._createMaskCanvas(t);r.setTransform(1,0,0,1,l.offsetX-o[4],l.offsetY-o[5]);for(let t=0,h=a.length;t<h;t+=2){const h=at.transform(o,[e,i,s,n,a[t],a[t+1]]);r.drawImage(l.canvas,h[4],h[5])}r.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,i=this.current.fillColor,s=this.current.patternFill;for(const n of t){const{data:t,width:a,height:r,transform:o}=n,l=this.cachedCanvases.getCanvas("maskCanvas",a,r),h=l.context;h.save();Ze(h,this.getObject(t,n)),h.globalCompositeOperation="source-in",h.fillStyle=s?i.getPattern(h,this,It(e),ke):i,h.fillRect(0,0,a,r),h.restore(),e.save(),e.transform(...o),e.scale(1,-1),Ye(e,l.canvas,0,0,a,r,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):$("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,i,s){if(!this.contentVisible)return;const n=this.getObject(t);if(!n)return void $("Dependent image isn't ready yet");const a=n.width,r=n.height,o=[];for(let t=0,n=s.length;t<n;t+=2)o.push({transform:[e,0,0,i,s[t],s[t+1]],x:0,y:0,w:a,h:r});this.paintInlineImageXObjectGroup(n,o)}applyTransferMapsToCanvas(t){return"none"!==this.current.transferMaps&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:i,height:s}=t,n=this.cachedCanvases.getCanvas("inlineImage",i,s),a=n.context;return a.filter=this.current.transferMaps,a.drawImage(e,0,0),a.filter="none",n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,i=t.height,s=this.ctx;this.save();const{filter:n}=s;let a;if("none"!==n&&""!==n&&(s.filter="none"),s.scale(1/e,-1/i),t.bitmap)a=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)a=t;else{const s=this.cachedCanvases.getCanvas("inlineImage",e,i).context;Je(s,t),a=this.applyTransferMapsToCanvas(s)}const r=this._scaleImage(a,It(s));s.imageSmoothingEnabled=ii(Pt(s),t.interpolate),Ye(s,r.img,0,0,r.paintWidth,r.paintHeight,0,-i,e,i),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const i=this.ctx;let s;if(t.bitmap)s=t.bitmap;else{const e=t.width,i=t.height,n=this.cachedCanvases.getCanvas("inlineImage",e,i).context;Je(n,t),s=this.applyTransferMapsToCanvas(n)}for(const t of e)i.save(),i.transform(...t.transform),i.scale(1,-1),Ye(i,s,t.x,t.y,t.w,t.h,0,-1,1,1),i.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t,e){const i=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(e);const s=this.ctx;this.pendingClip&&(i||(this.pendingClip===ri?s.clip(t,"evenodd"):s.clip(t)),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox)}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=Pt(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),s=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,s)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:i,c:s,d:n}=this.ctx.getTransform();let a,r;if(0===i&&0===s){const i=Math.abs(e),s=Math.abs(n);if(i===s)if(0===t)a=r=1/i;else{const e=i*t;a=r=e<1?1/e:1}else if(0===t)a=1/i,r=1/s;else{const e=i*t,n=s*t;a=e<1?1/e:1,r=n<1?1/n:1}}else{const o=Math.abs(e*n-i*s),l=Math.hypot(e,i),h=Math.hypot(s,n);if(0===t)a=h/o,r=l/o;else{const e=t*o;a=h>e?h/e:1,r=l>e?l/e:1}}this._cachedScaleForStroking[0]=a,this._cachedScaleForStroking[1]=r}return this._cachedScaleForStroking}rescaleAndStroke(t,e){const{ctx:i,current:{lineWidth:s}}=this,[n,a]=this.getScaleForStroking();if(n===a)return i.lineWidth=(s||1)*n,void i.stroke(t);const r=i.getLineDash();e&&i.save(),i.scale(n,a),We.a=1/n,We.d=1/a;const o=new Path2D;if(o.addPath(t,We),r.length>0){const t=Math.max(n,a);i.setLineDash(r.map((e=>e/t))),i.lineDashOffset/=t}i.lineWidth=s||1,i.stroke(o),e&&i.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(const t in D)void 0!==oi.prototype[t]&&(oi.prototype[D[t]]=oi.prototype[t]);class li{static#Ri=null;static#Li="";static get workerPort(){return this.#Ri}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw new Error("Invalid `workerPort` type.");this.#Ri=t}static get workerSrc(){return this.#Li}static set workerSrc(t){if("string"!=typeof t)throw new Error("Invalid `workerSrc` type.");this.#Li=t}}class hi{#Fi;#Ni;constructor({parsedData:t,rawData:e}){this.#Fi=t,this.#Ni=e}getRaw(){return this.#Ni}get(t){return this.#Fi.get(t)??null}[Symbol.iterator](){return this.#Fi.entries()}}const di=Symbol("INTERNAL");class ci{#Oi=!1;#Bi=!1;#Hi=!1;#zi=!0;constructor(t,{name:e,intent:i,usage:s,rbGroups:n}){this.#Oi=!!(t&o),this.#Bi=!!(t&l),this.name=e,this.intent=i,this.usage=s,this.rbGroups=n}get visible(){if(this.#Hi)return this.#zi;if(!this.#zi)return!1;const{print:t,view:e}=this.usage;return this.#Oi?"OFF"!==e?.viewState:!this.#Bi||"OFF"!==t?.printState}_setVisible(t,e,i=!1){t!==di&&G("Internal method `_setVisible` called."),this.#Hi=i,this.#zi=e}}class ui{#Ui=null;#$i=new Map;#Gi=null;#ji=null;constructor(t,e=o){if(this.renderingIntent=e,this.name=null,this.creator=null,null!==t){this.name=t.name,this.creator=t.creator,this.#ji=t.order;for(const i of t.groups)this.#$i.set(i.id,new ci(e,i));if("OFF"===t.baseState)for(const t of this.#$i.values())t._setVisible(di,!1);for(const e of t.on)this.#$i.get(e)._setVisible(di,!0);for(const e of t.off)this.#$i.get(e)._setVisible(di,!1);this.#Gi=this.getHash()}}#Vi(t){const e=t.length;if(e<2)return!0;const i=t[0];for(let s=1;s<e;s++){const e=t[s];let n;if(Array.isArray(e))n=this.#Vi(e);else{if(!this.#$i.has(e))return $(`Optional content group not found: ${e}`),!0;n=this.#$i.get(e).visible}switch(i){case"And":if(!n)return!1;break;case"Or":if(n)return!0;break;case"Not":return!n;default:return!0}}return"And"===i}isVisible(t){if(0===this.#$i.size)return!0;if(!t)return U("Optional content group not defined."),!0;if("OCG"===t.type)return this.#$i.has(t.id)?this.#$i.get(t.id).visible:($(`Optional content group not found: ${t.id}`),!0);if("OCMD"===t.type){if(t.expression)return this.#Vi(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#$i.has(e))return $(`Optional content group not found: ${e}`),!0;if(this.#$i.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#$i.has(e))return $(`Optional content group not found: ${e}`),!0;if(!this.#$i.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#$i.has(e))return $(`Optional content group not found: ${e}`),!0;if(!this.#$i.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#$i.has(e))return $(`Optional content group not found: ${e}`),!0;if(this.#$i.get(e).visible)return!1}return!0}return $(`Unknown optional content policy ${t.policy}.`),!0}return $(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0,i=!0){const s=this.#$i.get(t);if(s){if(i&&e&&s.rbGroups.length)for(const e of s.rbGroups)for(const i of e)i!==t&&this.#$i.get(i)?._setVisible(di,!1,!0);s._setVisible(di,!!e,!0),this.#Ui=null}else $(`Optional content group not found: ${t}`)}setOCGState({state:t,preserveRB:e}){let i;for(const s of t){switch(s){case"ON":case"OFF":case"Toggle":i=s;continue}const t=this.#$i.get(s);if(t)switch(i){case"ON":this.setVisibility(s,!0,e);break;case"OFF":this.setVisibility(s,!1,e);break;case"Toggle":this.setVisibility(s,!t.visible,e)}}this.#Ui=null}get hasInitialVisibility(){return null===this.#Gi||this.getHash()===this.#Gi}getOrder(){return this.#$i.size?this.#ji?this.#ji.slice():[...this.#$i.keys()]:null}getGroup(t){return this.#$i.get(t)||null}getHash(){if(null!==this.#Ui)return this.#Ui;const t=new Yt;for(const[e,i]of this.#$i)t.update(`${e}:${i.visible}`);return this.#Ui=t.hexdigest()}[Symbol.iterator](){return this.#$i.entries()}}class pi{constructor(t,{disableRange:e=!1,disableStream:i=!1}){j(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:s,initialData:n,progressiveDone:a,contentDispositionFilename:r}=t;if(this._queuedChunks=[],this._progressiveDone=a,this._contentDispositionFilename=r,n?.length>0){const t=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!i,this._isRangeSupported=!e,this._contentLength=s,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})})),t.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})})),t.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})})),t.addProgressiveDoneListener((()=>{this._onProgressiveDone()})),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const i=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(void 0===t)this._fullRequestReader?this._fullRequestReader._enqueue(i):this._queuedChunks.push(i);else{j(this._rangeReaders.some((function(e){return e._begin===t&&(e._enqueue(i),!0)})),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){j(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new gi(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new mi(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class gi{constructor(t,e,i=!1,s=null){this._stream=t,this._done=i||!1,this._filename=At(s)?s:null,this._queuedChunks=e||[],this._loaded=0;for(const t of this._queuedChunks)this._loaded+=t.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class mi{constructor(t,e,i){this._stream=t,this._begin=e,this._end=i,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function fi(t,e){const i=new Headers;if(!t||!e||"object"!=typeof e)return i;for(const t in e){const s=e[t];void 0!==s&&i.append(t,s)}return i}function bi(t){return URL.parse(t)?.origin??null}function vi({responseHeaders:t,isHttp:e,rangeChunkSize:i,disableRange:s}){const n={allowRangeRequests:!1,suggestedLength:void 0},a=parseInt(t.get("Content-Length"),10);if(!Number.isInteger(a))return n;if(n.suggestedLength=a,a<=2*i)return n;if(s||!e)return n;if("bytes"!==t.get("Accept-Ranges"))return n;return"identity"!==(t.get("Content-Encoding")||"identity")||(n.allowRangeRequests=!0),n}function Ai(t){const e=t.get("Content-Disposition");if(e){let t=function(t){let e=!0,i=s("filename\\*","i").exec(t);if(i){i=i[1];let t=r(i);return t=unescape(t),t=o(t),t=l(t),a(t)}if(i=function(t){const e=[];let i;const n=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(i=n.exec(t));){let[,t,s,n]=i;if(t=parseInt(t,10),t in e){if(0===t)break}else e[t]=[s,n]}const a=[];for(let t=0;t<e.length&&t in e;++t){let[i,s]=e[t];s=r(s),i&&(s=unescape(s),0===t&&(s=o(s))),a.push(s)}return a.join("")}(t),i)return a(l(i));if(i=s("filename","i").exec(t),i){i=i[1];let t=r(i);return t=l(t),a(t)}function s(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function n(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{const s=new TextDecoder(t,{fatal:!0}),n=it(i);i=s.decode(n),e=!1}catch{}}return i}function a(t){return e&&/[\x80-\xff]/.test(t)&&(t=n("utf-8",t),e&&(t=n("iso-8859-1",t))),t}function r(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const i=e[t].indexOf('"');-1!==i&&(e[t]=e[t].slice(0,i),e.length=t+1),e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function o(t){const e=t.indexOf("'");return-1===e?t:n(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function l(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,i,s){if("q"===i||"Q"===i)return n(e,s=(s=s.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{s=atob(s)}catch{}return n(e,s)}))}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if(At(t))return t}return null}function wi(t,e){return new J(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t,404===t||0===t&&e.startsWith("file:"))}function yi(t){return 200===t||206===t}function _i(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function xi(t){return t instanceof Uint8Array?t.buffer:t instanceof ArrayBuffer?t:($(`getArrayBuffer - unexpected data format: ${t}`),new Uint8Array(t).buffer)}class Si{_responseOrigin=null;constructor(t){this.source=t,this.isHttp=/^https?:/i.test(t.url),this.headers=fi(this.isHttp,t.httpHeaders),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return j(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new Ei(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new Ci(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Ei{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange;const i=new Headers(t.headers),s=e.url;fetch(s,_i(i,this._withCredentials,this._abortController)).then((e=>{if(t._responseOrigin=bi(e.url),!yi(e.status))throw wi(e.status,s);this._reader=e.body.getReader(),this._headersCapability.resolve();const i=e.headers,{allowRangeRequests:n,suggestedLength:a}=vi({responseHeaders:i,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=n,this._contentLength=a||this._contentLength,this._filename=Ai(i),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new tt("Streaming is disabled."))})).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:xi(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class Ci{constructor(t,e,i){this._stream=t,this._reader=null,this._loaded=0;const s=t.source;this._withCredentials=s.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!s.disableStream,this._abortController=new AbortController;const n=new Headers(t.headers);n.append("Range",`bytes=${e}-${i-1}`);const a=s.url;fetch(a,_i(n,this._withCredentials,this._abortController)).then((e=>{const i=bi(e.url);if(i!==t._responseOrigin)throw new Error(`Expected range response-origin "${i}" to match "${t._responseOrigin}".`);if(!yi(e.status))throw wi(e.status,a);this._readCapability.resolve(),this._reader=e.body.getReader()})).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded}),{value:xi(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class Ti{_responseOrigin=null;constructor({url:t,httpHeaders:e,withCredentials:i}){this.url=t,this.isHttp=/^https?:/i.test(t),this.headers=fi(this.isHttp,e),this.withCredentials=i||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,i=this.currXhrId++,s=this.pendingRequests[i]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const[t,i]of this.headers)e.setRequestHeader(t,i);return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),s.expectedStatus=206):s.expectedStatus=200,e.responseType="arraybuffer",j(t.onError,"Expected `onError` callback to be provided."),e.onerror=()=>{t.onError(e.status)},e.onreadystatechange=this.onStateChange.bind(this,i),e.onprogress=this.onProgress.bind(this,i),s.onHeadersReceived=t.onHeadersReceived,s.onDone=t.onDone,s.onError=t.onError,s.onProgress=t.onProgress,e.send(null),i}onProgress(t,e){const i=this.pendingRequests[t];i&&i.onProgress?.(e)}onStateChange(t,e){const i=this.pendingRequests[t];if(!i)return;const s=i.xhr;if(s.readyState>=2&&i.onHeadersReceived&&(i.onHeadersReceived(),delete i.onHeadersReceived),4!==s.readyState)return;if(!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],0===s.status&&this.isHttp)return void i.onError(s.status);const n=s.status||200;if(!(200===n&&206===i.expectedStatus)&&n!==i.expectedStatus)return void i.onError(s.status);const a=function(t){const e=t.response;return"string"!=typeof e?e:it(e).buffer}(s);if(206===n){const t=s.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);e?i.onDone({begin:parseInt(e[1],10),chunk:a}):($('Missing or invalid "Content-Range" header.'),i.onError(0))}else a?i.onDone({begin:0,chunk:a}):i.onError(s.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class Mi{constructor(t){this._source=t,this._manager=new Ti(t),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return j(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new Pi(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const i=new Ii(this._manager,t,e);return i.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Pi{constructor(t,e){this._manager=t,this._url=e.url,this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=bi(e.responseURL);const i=e.getAllResponseHeaders(),s=new Headers(i?i.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map((t=>{const[e,...i]=t.split(": ");return[e,i.join(": ")]})):[]),{allowRangeRequests:n,suggestedLength:a}=vi({responseHeaders:s,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});n&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=Ai(s),this._isRangeSupported&&this._manager.abortRequest(t),this._headersCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);if(this._done=!0,!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=wi(t,this._url),this._headersCapability.reject(this._storedError);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){if(await this._headersCapability.promise,this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersCapability.reject(t);for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class Ii{constructor(t,e,i){this._manager=t,this._url=t.url,this._requestId=t.request({begin:e,end:i,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_onHeadersReceived(){const t=bi(this._manager.getRequestXhr(this._requestId)?.responseURL);t!==this._manager._responseOrigin&&(this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`),this._onError(0))}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError??=wi(t,this._url);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const ki=/^[a-z][a-z0-9\-+.]+:/i;class Di{constructor(t){this.source=t,this.url=function(t){if(ki.test(t))return new URL(t);const e=process.getBuiltinModule("url");return new URL(e.pathToFileURL(t))}(t.url),j("file:"===this.url.protocol,"PDFNodeStream only supports file:// URLs."),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return j(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=new Ri(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new Li(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Ri{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers();const i=process.getBuiltinModule("fs");i.promises.lstat(this._url).then((t=>{this._contentLength=t.size,this._setReadableStream(i.createReadStream(this._url)),this._headersCapability.resolve()}),(t=>{"ENOENT"===t.code&&(t=wi(0,this._url.href)),this._storedError=t,this._headersCapability.reject(t)}))}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t)return this._readCapability=Promise.withResolvers(),this.read();this._loaded+=t.length,this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",(()=>{this._readCapability.resolve()})),t.on("end",(()=>{t.destroy(),this._done=!0,this._readCapability.resolve()})),t.on("error",(t=>{this._error(t)})),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new tt("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class Li{constructor(t,e,i){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const s=t.source;this._isStreamingSupported=!s.disableStream;const n=process.getBuiltinModule("fs");this._setReadableStream(n.createReadStream(this._url,{start:e,end:i-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t)return this._readCapability=Promise.withResolvers(),this.read();this._loaded+=t.length,this.onProgress?.({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",(()=>{this._readCapability.resolve()})),t.on("end",(()=>{t.destroy(),this._done=!0,this._readCapability.resolve()})),t.on("error",(t=>{this._error(t)})),this._storedError&&this._readableStream.destroy(this._storedError)}}class Fi{#Wi=Promise.withResolvers();#ft=null;#qi=!1;#Xi=!!globalThis.FontInspector?.enabled;#Ki=null;#Yi=null;#Qi=0;#Ji=0;#Zi=null;#ts=null;#es=0;#is=0;#ss=Object.create(null);#ns=[];#as=null;#rs=[];#os=new WeakMap;#ls=null;static#hs=new Map;static#ds=new Map;static#cs=new WeakMap;static#us=null;static#ps=new Set;constructor({textContentSource:t,container:e,viewport:i}){if(t instanceof ReadableStream)this.#as=t;else{if("object"!=typeof t)throw new Error('No "textContentSource" parameter specified.');this.#as=new ReadableStream({start(e){e.enqueue(t),e.close()}})}this.#ft=this.#ts=e,this.#is=i.scale*Dt.pixelRatio,this.#es=i.rotation,this.#Yi={div:null,properties:null,ctx:null};const{pageWidth:s,pageHeight:n,pageX:a,pageY:r}=i.rawDims;this.#ls=[1,0,0,-1,-a,r+n],this.#Ji=s,this.#Qi=n,Fi.#gs(),kt(e,i),this.#Wi.promise.finally((()=>{Fi.#ps.delete(this),this.#Yi=null,this.#ss=null})).catch((()=>{}))}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=st.platform;return q(this,"fontFamilyMap",new Map([["sans-serif",(t&&e?"Calibri, ":"")+"sans-serif"],["monospace",(t&&e?"Lucida Console, ":"")+"monospace"]]))}render(){const t=()=>{this.#Zi.read().then((({value:e,done:i})=>{i?this.#Wi.resolve():(this.#Ki??=e.lang,Object.assign(this.#ss,e.styles),this.#ms(e.items),t())}),this.#Wi.reject)};return this.#Zi=this.#as.getReader(),Fi.#ps.add(this),t(),this.#Wi.promise}update({viewport:t,onBefore:e=null}){const i=t.scale*Dt.pixelRatio,s=t.rotation;if(s!==this.#es&&(e?.(),this.#es=s,kt(this.#ts,{rotation:s})),i!==this.#is){e?.(),this.#is=i;const t={div:null,properties:null,ctx:Fi.#fs(this.#Ki)};for(const e of this.#rs)t.properties=this.#os.get(e),t.div=e,this.#bs(t)}}cancel(){const t=new tt("TextLayer task cancelled.");this.#Zi?.cancel(t).catch((()=>{})),this.#Zi=null,this.#Wi.reject(t)}get textDivs(){return this.#rs}get textContentItemsStr(){return this.#ns}#ms(t){if(this.#qi)return;this.#Yi.ctx??=Fi.#fs(this.#Ki);const e=this.#rs,i=this.#ns;for(const s of t){if(e.length>1e5)return $("Ignoring additional textDivs for performance reasons."),void(this.#qi=!0);if(void 0!==s.str)i.push(s.str),this.#vs(s);else if("beginMarkedContentProps"===s.type||"beginMarkedContent"===s.type){const t=this.#ft;this.#ft=document.createElement("span"),this.#ft.classList.add("markedContent"),null!==s.id&&this.#ft.setAttribute("id",`${s.id}`),t.append(this.#ft)}else"endMarkedContent"===s.type&&(this.#ft=this.#ft.parentNode)}}#vs(t){const e=document.createElement("span"),i={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};this.#rs.push(e);const s=at.transform(this.#ls,t.transform);let n=Math.atan2(s[1],s[0]);const a=this.#ss[t.fontName];a.vertical&&(n+=Math.PI/2);let r=this.#Xi&&a.fontSubstitution||a.fontFamily;r=Fi.fontFamilyMap.get(r)||r;const o=Math.hypot(s[2],s[3]),l=o*Fi.#As(r,a,this.#Ki);let h,d;0===n?(h=s[4],d=s[5]-l):(h=s[4]+l*Math.sin(n),d=s[5]-l*Math.cos(n));const c="calc(var(--total-scale-factor) *",u=e.style;this.#ft===this.#ts?(u.left=`${(100*h/this.#Ji).toFixed(2)}%`,u.top=`${(100*d/this.#Qi).toFixed(2)}%`):(u.left=`${c}${h.toFixed(2)}px)`,u.top=`${c}${d.toFixed(2)}px)`),u.fontSize=`${c}${(Fi.#us*o).toFixed(2)}px)`,u.fontFamily=r,i.fontSize=o,e.setAttribute("role","presentation"),e.textContent=t.str,e.dir=t.dir,this.#Xi&&(e.dataset.fontName=a.fontSubstitutionLoadedName||t.fontName),0!==n&&(i.angle=n*(180/Math.PI));let p=!1;if(t.str.length>1)p=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){const e=Math.abs(t.transform[0]),i=Math.abs(t.transform[3]);e!==i&&Math.max(e,i)/Math.min(e,i)>1.5&&(p=!0)}if(p&&(i.canvasWidth=a.vertical?t.height:t.width),this.#os.set(e,i),this.#Yi.div=e,this.#Yi.properties=i,this.#bs(this.#Yi),i.hasText&&this.#ft.append(e),i.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation"),this.#ft.append(t)}}#bs(t){const{div:e,properties:i,ctx:s}=t,{style:n}=e;let a="";if(Fi.#us>1&&(a=`scale(${1/Fi.#us})`),0!==i.canvasWidth&&i.hasText){const{fontFamily:t}=n,{canvasWidth:r,fontSize:o}=i;Fi.#ws(s,o*this.#is,t);const{width:l}=s.measureText(e.textContent);l>0&&(a=`scaleX(${r*this.#is/l}) ${a}`)}0!==i.angle&&(a=`rotate(${i.angle}deg) ${a}`),a.length>0&&(n.transform=a)}static cleanup(){if(!(this.#ps.size>0)){this.#hs.clear();for(const{canvas:t}of this.#ds.values())t.remove();this.#ds.clear()}}static#fs(t=null){let e=this.#ds.get(t||="");if(!e){const i=document.createElement("canvas");i.className="hiddenCanvasElement",i.lang=t,document.body.append(i),e=i.getContext("2d",{alpha:!1,willReadFrequently:!0}),this.#ds.set(t,e),this.#cs.set(e,{size:0,family:""})}return e}static#ws(t,e,i){const s=this.#cs.get(t);e===s.size&&i===s.family||(t.font=`${e}px ${i}`,s.size=e,s.family=i)}static#gs(){if(null!==this.#us)return;const t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.style.position="absolute",t.textContent="X",document.body.append(t),this.#us=t.getBoundingClientRect().height,t.remove()}static#As(t,e,i){const s=this.#hs.get(t);if(s)return s;const n=this.#fs(i);n.canvas.width=n.canvas.height=30,this.#ws(n,30,t);const a=n.measureText(""),r=a.fontBoundingBoxAscent,o=Math.abs(a.fontBoundingBoxDescent);n.canvas.width=n.canvas.height=0;let l=.8;return r?l=r/(r+o):(st.platform.isFirefox&&$("Enable the `dom.textMetrics.fontBoundingBox.enabled` preference in `about:config` to improve TextLayer rendering."),e.ascent?l=e.ascent:e.descent&&(l=1+e.descent)),this.#hs.set(t,l),l}}class Ni{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};return function t(i){if(!i)return;let s=null;const n=i.name;if("#text"===n)s=i.value;else{if(!Ni.shouldBuildText(n))return;i?.attributes?.textContent?s=i.attributes.textContent:i.value&&(s=i.value)}if(null!==s&&e.push({str:s}),i.children)for(const e of i.children)t(e)}(t),i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}const Oi=65536;function Bi(t={}){"string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t});const e=new $i,{docId:i}=e,n=t.url?function(t){if(t instanceof URL)return t.href;if("string"==typeof t){if(s)return t;const e=URL.parse(t,window.location);if(e)return e.href}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(t.url):null,a=t.data?function(t){if(s&&"undefined"!=typeof Buffer&&t instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return it(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(t?.length))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(t.data):null,r=t.httpHeaders||null,o=!0===t.withCredentials,l=t.password??null,h=t.range instanceof Gi?t.range:null,d=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:Oi;let c=t.worker instanceof qi?t.worker:null;const u=t.verbosity,p="string"!=typeof t.docBaseUrl||vt(t.docBaseUrl)?null:t.docBaseUrl,g=Hi(t.cMapUrl),m=!1!==t.cMapPacked,f=t.CMapReaderFactory||(s?Me:ve),b=Hi(t.iccUrl),v=Hi(t.standardFontDataUrl),A=t.StandardFontDataFactory||(s?Pe:_e),w=Hi(t.wasmUrl),y=t.WasmFactory||(s?Ie:Se),_=!0!==t.stopAtErrors,x=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,S=!1!==t.isEvalSupported,E="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!s,C="boolean"==typeof t.isImageDecoderSupported?t.isImageDecoderSupported:!s&&(st.platform.isFirefox||!globalThis.chrome),T=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,M="boolean"==typeof t.disableFontFace?t.disableFontFace:s,P=!0===t.fontExtraProperties,I=!0===t.enableXfa,k=t.ownerDocument||globalThis.document,D=!0===t.disableRange,R=!0===t.disableStream,L=!0===t.disableAutoFetch,F=!0===t.pdfBug,N=t.CanvasFactory||(s?Te:fe),O=t.FilterFactory||(s?Ce:we),B=!0===t.enableHWA,z=!1!==t.useWasm,U=h?h.length:t.length??NaN,$="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!s&&!M,G="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:!!(f===ve&&A===_e&&y===Se&&g&&v&&w&&xt(g,document.baseURI)&&xt(v,document.baseURI)&&xt(w,document.baseURI));H(u);const j={canvasFactory:new N({ownerDocument:k,enableHWA:B}),filterFactory:new O({docId:i,ownerDocument:k}),cMapReaderFactory:G?null:new f({baseUrl:g,isCompressed:m}),standardFontDataFactory:G?null:new A({baseUrl:v}),wasmFactory:G?null:new y({baseUrl:w})};if(!c){const t={verbosity:u,port:li.workerPort};c=t.port?qi.fromPort(t):new qi(t),e._worker=c}const V={docId:i,apiVersion:"5.2.133",data:a,password:l,disableAutoFetch:L,rangeChunkSize:d,length:U,docBaseUrl:p,enableXfa:I,evaluatorOptions:{maxImageSize:x,disableFontFace:M,ignoreErrors:_,isEvalSupported:S,isOffscreenCanvasSupported:E,isImageDecoderSupported:C,canvasMaxAreaInBytes:T,fontExtraProperties:P,useSystemFonts:$,useWasm:z,useWorkerFetch:G,cMapUrl:g,iccUrl:b,standardFontDataUrl:v,wasmUrl:w}},W={ownerDocument:k,pdfBug:F,styleElement:null,loadingParams:{disableAutoFetch:L,enableXfa:I}};return c.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const t=c.messageHandler.sendWithPromise("GetDocRequest",V,a?[a.buffer]:null);let l;if(h)l=new pi(h,{disableRange:D,disableStream:R});else if(!a){if(!n)throw new Error("getDocument - no `url` parameter provided.");let t;if(s)if(xt(n)){if("undefined"==typeof fetch||"undefined"==typeof Response||!("body"in Response.prototype))throw new Error("getDocument - the Fetch API was disabled in Node.js, see `--no-experimental-fetch`.");t=Si}else t=Di;else t=xt(n)?Si:Mi;l=new t({url:n,length:U,httpHeaders:r,withCredentials:o,rangeChunkSize:d,disableRange:D,disableStream:R})}return t.then((t=>{if(e.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const s=new ge(i,t,c.port),n=new Xi(s,e,l,W,j);e._transport=n,s.send("Ready",null)}))})).catch(e._capability.reject),e}function Hi(t){if("string"!=typeof t)return null;if(t.endsWith("/"))return t;throw new Error(`Invalid factory url: "${t}" must include trailing slash.`)}const zi=t=>"object"==typeof t&&Number.isInteger(t?.num)&&t.num>=0&&Number.isInteger(t?.gen)&&t.gen>=0,Ui=function(t,e,i){if(!Array.isArray(i)||i.length<2)return!1;const[s,n,...a]=i;if(!t(s)&&!Number.isInteger(s))return!1;if(!e(n))return!1;const r=a.length;let o=!0;switch(n.name){case"XYZ":if(r<2||r>3)return!1;break;case"Fit":case"FitB":return 0===r;case"FitH":case"FitBH":case"FitV":case"FitBV":if(r>1)return!1;break;case"FitR":if(4!==r)return!1;o=!1;break;default:return!1}for(const t of a)if(!("number"==typeof t||o&&null===t))return!1;return!0}.bind(null,zi,(t=>"object"==typeof t&&"string"==typeof t?.name));class $i{static#fi=0;_capability=Promise.withResolvers();_transport=null;_worker=null;docId="d"+$i.#fi++;destroyed=!1;onPassword=null;onProgress=null;get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0),await(this._transport?.destroy())}catch(t){throw this._worker?.port&&delete this._worker._pendingDestroy,t}this._transport=null,this._worker?.destroy(),this._worker=null}async getData(){return this._transport.getData()}}class Gi{constructor(t,e,i=!1,s=null){this.length=t,this.initialData=e,this.progressiveDone=i,this.contentDispositionFilename=s,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const i of this._rangeListeners)i(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const i of this._progressListeners)i(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){G("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class ji{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return q(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class Vi{#ys=!1;constructor(t,e,i,s=!1){this._pageIndex=t,this._pageInfo=e,this._transport=i,this._stats=s?new _t:null,this._pdfBug=s,this.commonObjs=i.commonObjs,this.objs=new Yi,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:s=0,dontFlip:n=!1}={}){return new ft({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return q(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:i="display",annotationMode:s=g.ENABLE,transform:n=null,background:a=null,optionalContentConfigPromise:r=null,annotationCanvasMap:o=null,pageColors:h=null,printAnnotationStorage:d=null,isEditing:c=!1}){this._stats?.time("Overall");const u=this._transport.getRenderingIntent(i,s,d,c),{renderingIntent:p,cacheKey:m}=u;this.#ys=!1,r||=this._transport.getOptionalContentConfig(p);let f=this._intentStates.get(m);f||(f=Object.create(null),this._intentStates.set(m,f)),f.streamReaderCancelTimeout&&(clearTimeout(f.streamReaderCancelTimeout),f.streamReaderCancelTimeout=null);const b=!!(p&l);f.displayReadyCapability||(f.displayReadyCapability=Promise.withResolvers(),f.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(u));const v=t=>{f.renderTasks.delete(A),b&&(this.#ys=!0),this.#_s(),t?(A.capability.reject(t),this._abortOperatorList({intentState:f,reason:t instanceof Error?t:new Error(t)})):A.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},A=new Ji({callback:v,params:{canvasContext:t,viewport:e,transform:n,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:o,operatorList:f.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!b,pdfBug:this._pdfBug,pageColors:h});(f.renderTasks||=new Set).add(A);const w=A.task;return Promise.all([f.displayReadyCapability.promise,r]).then((([t,e])=>{if(this.destroyed)v();else{if(this._stats?.time("Rendering"),!(e.renderingIntent&p))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");A.initializeGraphics({transparency:t,optionalContentConfig:e}),A.operatorListChanged()}})).catch(v),w}getOperatorList({intent:t="display",annotationMode:e=g.ENABLE,printAnnotationStorage:i=null,isEditing:s=!1}={}){const n=this._transport.getRenderingIntent(t,e,i,s,!0);let a,r=this._intentStates.get(n.cacheKey);return r||(r=Object.create(null),this._intentStates.set(n.cacheKey,r)),r.opListReadCapability||(a=Object.create(null),a.operatorListChanged=function(){r.operatorList.lastChunk&&(r.opListReadCapability.resolve(r.operatorList),r.renderTasks.delete(a))},r.opListReadCapability=Promise.withResolvers(),(r.renderTasks||=new Set).add(a),r.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(n)),r.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then((t=>Ni.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,i){const s=e.getReader(),n={items:[],styles:Object.create(null),lang:null};!function e(){s.read().then((function({value:i,done:s}){s?t(n):(n.lang??=i.lang,Object.assign(n.styles,i.styles),n.items.push(...i.items),e())}),i)}()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const i of e.renderTasks)t.push(i.completed),i.cancel();return this.objs.clear(),this.#ys=!1,Promise.all(t)}cleanup(t=!1){this.#ys=!0;const e=this.#_s();return t&&e&&(this._stats&&=new _t),e}#_s(){if(!this.#ys||this.destroyed)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),this.#ys=!1,!0}_startRenderPage(t,e){const i=this._intentStates.get(e);i&&(this._stats?.timeEnd("Page Request"),i.displayReadyCapability?.resolve(t))}_renderPageChunk(t,e){for(let i=0,s=t.length;i<s;i++)e.operatorList.fnArray.push(t.fnArray[i]),e.operatorList.argsArray.push(t.argsArray[i]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const t of e.renderTasks)t.operatorListChanged();t.lastChunk&&this.#_s()}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:i,modifiedIds:s}){const{map:n,transfer:a}=i,r=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:n,modifiedIds:s},a).getReader(),o=this._intentStates.get(e);o.streamReader=r;const l=()=>{r.read().then((({value:t,done:e})=>{e?o.streamReader=null:this._transport.destroyed||(this._renderPageChunk(t,o),l())}),(t=>{if(o.streamReader=null,!this._transport.destroyed){if(o.operatorList){o.operatorList.lastChunk=!0;for(const t of o.renderTasks)t.operatorListChanged();this.#_s()}if(o.displayReadyCapability)o.displayReadyCapability.reject(t);else{if(!o.opListReadCapability)throw t;o.opListReadCapability.reject(t)}}}))};l()}_abortOperatorList({intentState:t,reason:e,force:i=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!i){if(t.renderTasks.size>0)return;if(e instanceof bt){let i=100;return e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),void(t.streamReaderCancelTimeout=setTimeout((()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})}),i))}}if(t.streamReader.cancel(new tt(e.message)).catch((()=>{})),t.streamReader=null,!this._transport.destroyed){for(const[e,i]of this._intentStates)if(i===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}class Wi{#xs=new Map;#Ss=Promise.resolve();postMessage(t,e){const i={data:structuredClone(t,e?{transfer:e}:null)};this.#Ss.then((()=>{for(const[t]of this.#xs)t.call(this,i)}))}addEventListener(t,e,i=null){let s=null;if(i?.signal instanceof AbortSignal){const{signal:n}=i;if(n.aborted)return void $("LoopbackPort - cannot use an `aborted` signal.");const a=()=>this.removeEventListener(t,e);s=()=>n.removeEventListener("abort",a),n.addEventListener("abort",a)}this.#xs.set(e,s)}removeEventListener(t,e){const i=this.#xs.get(e);i?.(),this.#xs.delete(e)}terminate(){for(const[,t]of this.#xs)t?.();this.#xs.clear()}}class qi{static#Es=0;static#Cs=!1;static#Ts;static{s&&(this.#Cs=!0,li.workerSrc||="./pdf.worker.mjs"),this._isSameOrigin=(t,e)=>{const i=URL.parse(t);if(!i?.origin||"null"===i.origin)return!1;const s=new URL(e,i);return i.origin===s.origin},this._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))}}constructor({name:t=null,port:e=null,verbosity:i=z()}={}){if(this.name=t,this.destroyed=!1,this.verbosity=i,this._readyCapability=Promise.withResolvers(),this._port=null,this._webWorker=null,this._messageHandler=null,e){if(qi.#Ts?.has(e))throw new Error("Cannot use more than one PDFWorker per port.");return(qi.#Ts||=new WeakMap).set(e,this),void this._initializeFromPort(e)}this._initialize()}get promise(){return this._readyCapability.promise}#Ms(){this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t,this._messageHandler=new ge("main","worker",t),this._messageHandler.on("ready",(function(){})),this.#Ms()}_initialize(){if(qi.#Cs||qi.#Ps)return void this._setupFakeWorker();let{workerSrc:t}=qi;try{qi._isSameOrigin(window.location,t)||(t=qi._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),i=new ge("main","worker",e),s=()=>{n.abort(),i.destroy(),e.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},n=new AbortController;e.addEventListener("error",(()=>{this._webWorker||s()}),{signal:n.signal}),i.on("test",(t=>{n.abort(),!this.destroyed&&t?(this._messageHandler=i,this._port=e,this._webWorker=e,this.#Ms()):s()})),i.on("ready",(t=>{if(n.abort(),this.destroyed)s();else try{a()}catch{this._setupFakeWorker()}}));const a=()=>{const t=new Uint8Array;i.send("test",t,[t.buffer])};return void a()}catch{U("The worker has been disabled.")}this._setupFakeWorker()}_setupFakeWorker(){qi.#Cs||($("Setting up fake worker."),qi.#Cs=!0),qi._setupFakeWorkerGlobal.then((t=>{if(this.destroyed)return void this._readyCapability.reject(new Error("Worker was destroyed"));const e=new Wi;this._port=e;const i="fake"+qi.#Es++,s=new ge(i+"_worker",i,e);t.setup(s,e),this._messageHandler=new ge(i,i+"_worker",e),this.#Ms()})).catch((t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0,this._webWorker?.terminate(),this._webWorker=null,qi.#Ts?.delete(this._port),this._port=null,this._messageHandler?.destroy(),this._messageHandler=null}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");const e=this.#Ts?.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new qi(t)}static get workerSrc(){if(li.workerSrc)return li.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get#Ps(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){return q(this,"_setupFakeWorkerGlobal",(async()=>{if(this.#Ps)return this.#Ps;return(await import(this.workerSrc)).WorkerMessageHandler})())}}class Xi{#Is=new Map;#ks=new Map;#Ds=new Map;#Rs=new Map;#Ls=null;constructor(t,e,i,s,n){this.messageHandler=t,this.loadingTask=e,this.commonObjs=new Yi,this.fontLoader=new te({ownerDocument:s.ownerDocument,styleElement:s.styleElement}),this.loadingParams=s.loadingParams,this._params=s,this.canvasFactory=n.canvasFactory,this.filterFactory=n.filterFactory,this.cMapReaderFactory=n.cMapReaderFactory,this.standardFontDataFactory=n.standardFontDataFactory,this.wasmFactory=n.wasmFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=i,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}#Fs(t,e=null){const i=this.#Is.get(t);if(i)return i;const s=this.messageHandler.sendWithPromise(t,e);return this.#Is.set(t,s),s}get annotationStorage(){return q(this,"annotationStorage",new Jt)}getRenderingIntent(t,e=g.ENABLE,i=null,s=!1,n=!1){let a=o,m=Qt;switch(t){case"any":a=r;break;case"display":break;case"print":a=l;break;default:$(`getRenderingIntent - invalid intent: ${t}`)}const f=a&l&&i instanceof Zt?i:this.annotationStorage;switch(e){case g.DISABLE:a+=c;break;case g.ENABLE:break;case g.ENABLE_FORMS:a+=h;break;case g.ENABLE_STORAGE:a+=d,m=f.serializable;break;default:$(`getRenderingIntent - invalid annotationMode: ${e}`)}s&&(a+=u),n&&(a+=p);const{ids:b,hash:v}=f.modifiedIds;return{renderingIntent:a,cacheKey:[a,m.hash,v].join("_"),annotationStorageSerializable:m,modifiedIds:b}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),this.#Ls?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const e of this.#ks.values())t.push(e._destroy());this.#ks.clear(),this.#Ds.clear(),this.#Rs.clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then((()=>{this.commonObjs.clear(),this.fontLoader.clear(),this.#Is.clear(),this.filterFactory.destroy(),Fi.cleanup(),this._networkStream?.cancelAllRequests(new tt("Worker was terminated.")),this.messageHandler?.destroy(),this.messageHandler=null,this.destroyCapability.resolve()}),this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{j(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}},e.onPull=()=>{this._fullReader.read().then((function({value:t,done:i}){i?e.close():(j(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t]))})).catch((t=>{e.error(t)}))},e.onCancel=t=>{this._fullReader.cancel(t),e.ready.catch((t=>{if(!this.destroyed)throw t}))}})),t.on("ReaderHeadersReady",(async t=>{await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:s,contentLength:n}=this._fullReader;return i&&s||(this._lastProgress&&e.onProgress?.(this._lastProgress),this._fullReader.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}),{isStreamingSupported:i,isRangeSupported:s,contentLength:n}})),t.on("GetRangeReader",((t,e)=>{j(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const i=this._networkStream.getRangeReader(t.begin,t.end);i?(e.onPull=()=>{i.read().then((function({value:t,done:i}){i?e.close():(j(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(t),1,[t]))})).catch((t=>{e.error(t)}))},e.onCancel=t=>{i.cancel(t),e.ready.catch((t=>{if(!this.destroyed)throw t}))}):e.close()})),t.on("GetDoc",(({pdfInfo:t})=>{this._numPages=t.numPages,this._htmlForXfa=t.htmlForXfa,delete t.htmlForXfa,e._capability.resolve(new ji(t,this))})),t.on("DocException",(t=>{e._capability.reject(pe(t))})),t.on("PasswordRequest",(t=>{this.#Ls=Promise.withResolvers();try{if(!e.onPassword)throw pe(t);const i=t=>{t instanceof Error?this.#Ls.reject(t):this.#Ls.resolve({password:t})};e.onPassword(i,t.code)}catch(t){this.#Ls.reject(t)}return this.#Ls.promise})),t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length}),this.downloadInfoCapability.resolve(t)})),t.on("StartRenderPage",(t=>{if(this.destroyed)return;this.#ks.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)})),t.on("commonobj",(([e,i,s])=>{if(this.destroyed)return null;if(this.commonObjs.has(e))return null;switch(i){case"Font":if("error"in s){const t=s.error;$(`Error during font loading: ${t}`),this.commonObjs.resolve(e,t);break}const n=this._params.pdfBug&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,a=new ee(s,n);this.fontLoader.bind(a).catch((()=>t.sendWithPromise("FontFallback",{id:e}))).finally((()=>{!a.fontExtraProperties&&a.data&&(a.data=null),this.commonObjs.resolve(e,a)}));break;case"CopyLocalImage":const{imageRef:r}=s;j(r,"The imageRef must be defined.");for(const t of this.#ks.values())for(const[,i]of t.objs)if(i?.ref===r)return i.dataLen?(this.commonObjs.resolve(e,structuredClone(i)),i.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,s);break;default:throw new Error(`Got unknown common object type ${i}`)}return null})),t.on("obj",(([t,e,i,s])=>{if(this.destroyed)return;const n=this.#ks.get(e);if(!n.objs.has(t))if(0!==n._intentStates.size)switch(i){case"Image":case"Pattern":n.objs.resolve(t,s);break;default:throw new Error(`Got unknown object type ${i}`)}else s?.bitmap?.close()})),t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})})),t.on("FetchBinaryData",(async t=>{if(this.destroyed)throw new Error("Worker was destroyed.");const e=this[t.type];if(!e)throw new Error(`${t.type} not initialized, see the \`useWorkerFetch\` parameter.`);return e.fetch(t)}))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&$("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,i=this.#Ds.get(e);if(i)return i;const s=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((i=>{if(this.destroyed)throw new Error("Transport destroyed");i.refStr&&this.#Rs.set(i.refStr,t);const s=new Vi(e,i,this,this._params.pdfBug);return this.#ks.set(e,s),s}));return this.#Ds.set(e,s),s}getPageIndex(t){return zi(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#Fs("GetFieldObjects")}hasJSActions(){return this.#Fs("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#Fs("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#Fs("GetOptionalContentConfig").then((e=>new ui(e,t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#Is.get(t);if(e)return e;const i=this.messageHandler.sendWithPromise(t,null).then((t=>({info:t[0],metadata:t[1]?new hi(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})));return this.#Is.set(t,i),i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of this.#ks.values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear(),t||this.fontLoader.clear(),this.#Is.clear(),this.filterFactory.destroy(!0),Fi.cleanup()}}cachedPageNumber(t){if(!zi(t))return null;const e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return this.#Rs.get(e)??null}}const Ki=Symbol("INITIAL_DATA");class Yi{#Ns=Object.create(null);#Os(t){return this.#Ns[t]||={...Promise.withResolvers(),data:Ki}}get(t,e=null){if(e){const i=this.#Os(t);return i.promise.then((()=>e(i.data))),null}const i=this.#Ns[t];if(!i||i.data===Ki)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){const e=this.#Ns[t];return!!e&&e.data!==Ki}delete(t){const e=this.#Ns[t];return!(!e||e.data===Ki)&&(delete this.#Ns[t],!0)}resolve(t,e=null){const i=this.#Os(t);i.data=e,i.resolve()}clear(){for(const t in this.#Ns){const{data:e}=this.#Ns[t];e?.bitmap?.close()}this.#Ns=Object.create(null)}*[Symbol.iterator](){for(const t in this.#Ns){const{data:e}=this.#Ns[t];e!==Ki&&(yield[t,e])}}}class Qi{#Bs=null;onContinue=null;onError=null;constructor(t){this.#Bs=t}get promise(){return this.#Bs.capability.promise}cancel(t=0){this.#Bs.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#Bs.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#Bs;return t.form||t.canvas&&e?.size>0}}class Ji{#Hs=null;static#zs=new WeakSet;constructor({callback:t,params:e,objs:i,commonObjs:s,annotationCanvasMap:n,operatorList:a,pageIndex:r,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:d=!1,pageColors:c=null}){this.callback=t,this.params=e,this.objs=i,this.commonObjs=s,this.annotationCanvasMap=n,this.operatorListIdx=null,this.operatorList=a,this._pageIndex=r,this.canvasFactory=o,this.filterFactory=l,this._pdfBug=d,this.pageColors=c,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window,this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new Qi(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(Ji.#zs.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");Ji.#zs.add(this._canvas)}this._pdfBug&&globalThis.StepperManager?.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:i,viewport:s,transform:n,background:a}=this.params;this.gfx=new oi(i,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:n,viewport:s,transparency:t,background:a}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1,this.cancelled=!0,this.gfx?.endDrawing(),this.#Hs&&(window.cancelAnimationFrame(this.#Hs),this.#Hs=null),Ji.#zs.delete(this._canvas),t||=new bt(`Rendering cancelled, page ${this._pageIndex+1}`,e),this.callback(t),this.task.onError?.(t)}operatorListChanged(){this.graphicsReady?(this.stepper?.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||=this._continueBound}_continue(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?this.#Hs=window.requestAnimationFrame((()=>{this.#Hs=null,this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),Ji.#zs.delete(this._canvas),this.callback())))}}const Zi="5.2.133",ts="4f7761353";function es(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function is(t){return Math.max(0,Math.min(255,255*t))}class ss{static CMYK_G([t,e,i,s]){return["G",1-Math.min(1,.3*t+.59*i+.11*e+s)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=is(t),t,t]}static G_HTML([t]){const e=es(t);return`#${e}${e}${e}`}static RGB_G([t,e,i]){return["G",.3*t+.59*e+.11*i]}static RGB_rgb(t){return t.map(is)}static RGB_HTML(t){return`#${t.map(es).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,i,s]){return["RGB",1-Math.min(1,t+s),1-Math.min(1,i+s),1-Math.min(1,e+s)]}static CMYK_rgb([t,e,i,s]){return[is(1-Math.min(1,t+s)),is(1-Math.min(1,i+s)),is(1-Math.min(1,e+s))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,i]){const s=1-t,n=1-e,a=1-i;return["CMYK",s,n,a,Math.min(s,n,a)]}}class ns{create(t,e,i=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const s=this._createSVG("svg:svg");return s.setAttribute("version","1.1"),i||(s.setAttribute("width",`${t}px`),s.setAttribute("height",`${e}px`)),s.setAttribute("preserveAspectRatio","none"),s.setAttribute("viewBox",`0 0 ${t} ${e}`),s}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){G("Abstract method `_createSVG` called.")}}class as extends ns{_createSVG(t){return document.createElementNS(pt,t)}}class rs{static setupStorage(t,e,i,s,n){const a=s.getValue(e,{value:null});switch(i.name){case"textarea":if(null!==a.value&&(t.textContent=a.value),"print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){if(a.value===i.attributes.xfaOn?t.setAttribute("checked",!0):a.value===i.attributes.xfaOff&&t.removeAttribute("checked"),"print"===n)break;t.addEventListener("change",(t=>{s.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{if(null!==a.value&&t.setAttribute("value",a.value),"print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==a.value){t.setAttribute("value",a.value);for(const t of i.children)t.attributes.value===a.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",(t=>{const i=t.target.options,n=-1===i.selectedIndex?"":i[i.selectedIndex].value;s.setValue(e,{value:n})}))}}static setAttributes({html:t,element:e,storage:i=null,intent:s,linkService:n}){const{attributes:a}=e,r=t instanceof HTMLAnchorElement;"radio"===a.type&&(a.name=`${a.name}-${s}`);for(const[e,i]of Object.entries(a))if(null!=i)switch(e){case"class":i.length&&t.setAttribute(e,i.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",i);break;case"style":Object.assign(t.style,i);break;case"textContent":t.textContent=i;break;default:(!r||"href"!==e&&"newWindow"!==e)&&t.setAttribute(e,i)}r&&n.addLinkAttributes(t,a.href,a.newWindow),i&&a.dataId&&this.setupStorage(t,a.dataId,e,i)}static render(t){const e=t.annotationStorage,i=t.linkService,s=t.xfaHtml,n=t.intent||"display",a=document.createElement(s.name);s.attributes&&this.setAttributes({html:a,element:s,intent:n,linkService:i});const r="richText"!==n,o=t.div;if(o.append(a),t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=e}r&&o.setAttribute("class","xfaLayer xfaFont");const l=[];if(0===s.children.length){if(s.value){const t=document.createTextNode(s.value);a.append(t),r&&Ni.shouldBuildText(s.name)&&l.push(t)}return{textDivs:l}}const h=[[s,-1,a]];for(;h.length>0;){const[t,s,a]=h.at(-1);if(s+1===t.children.length){h.pop();continue}const o=t.children[++h.at(-1)[1]];if(null===o)continue;const{name:d}=o;if("#text"===d){const t=document.createTextNode(o.value);l.push(t),a.append(t);continue}const c=o?.attributes?.xmlns?document.createElementNS(o.attributes.xmlns,d):document.createElement(d);if(a.append(c),o.attributes&&this.setAttributes({html:c,element:o,storage:e,intent:n,linkService:i}),o.children?.length>0)h.push([o,-1,c]);else if(o.value){const t=document.createTextNode(o.value);r&&Ni.shouldBuildText(d)&&l.push(t),c.append(t)}}for(const t of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:l}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const os=1e3,ls=new WeakSet;class hs{static create(t){switch(t.data.annotationType){case E.LINK:return new cs(t);case E.TEXT:return new us(t);case E.WIDGET:switch(t.data.fieldType){case"Tx":return new gs(t);case"Btn":return t.data.radioButton?new bs(t):t.data.checkBox?new fs(t):new vs(t);case"Ch":return new As(t);case"Sig":return new ms(t)}return new ps(t);case E.POPUP:return new ws(t);case E.FREETEXT:return new _s(t);case E.LINE:return new xs(t);case E.SQUARE:return new Ss(t);case E.CIRCLE:return new Es(t);case E.POLYLINE:return new Cs(t);case E.CARET:return new Ms(t);case E.INK:return new Ps(t);case E.POLYGON:return new Ts(t);case E.HIGHLIGHT:return new Is(t);case E.UNDERLINE:return new ks(t);case E.SQUIGGLY:return new Ds(t);case E.STRIKEOUT:return new Rs(t);case E.STAMP:return new Ls(t);case E.FILEATTACHMENT:return new Fs(t);default:return new ds(t)}}}class ds{#Us=null;#$s=!1;#Gs=null;constructor(t,{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:s=!1}={}){this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(i)),s&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:i}){return!!(t?.str||e?.str||i?.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return ds._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;this.#Us||={rect:this.data.rect.slice(0)};const{rect:e}=t;e&&this.#js(e),this.#Gs?.popup.updateEdited(t)}resetEdited(){this.#Us&&(this.#js(this.#Us.rect),this.#Gs?.popup.resetEdited(),this.#Us=null)}#js(t){const{container:{style:e},data:{rect:i,rotation:s},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:a,pageX:r,pageY:o}}}}=this;i?.splice(0,4,...t),e.left=100*(t[0]-r)/n+"%",e.top=100*(a-t[3]+o)/a+"%",0===s?(e.width=100*(t[2]-t[0])/n+"%",e.height=100*(t[3]-t[1])/a+"%"):this.setRotation(s)}_createContainer(t){const{data:e,parent:{page:i,viewport:s}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id),this instanceof ps||(n.tabIndex=os);const{style:a}=n;if(a.zIndex=this.parent.zIndex++,e.alternativeText&&(n.title=e.alternativeText),e.noRotate&&n.classList.add("norotate"),!e.rect||this instanceof ws){const{rotation:t}=e;return e.hasOwnCanvas||0===t||this.setRotation(t,n),n}const{width:r,height:o}=this;if(!t&&e.borderStyle.width>0){a.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;if(t>0||i>0){const e=`calc(${t}px * var(--total-scale-factor)) / calc(${i}px * var(--total-scale-factor))`;a.borderRadius=e}else if(this instanceof bs){const t=`calc(${r}px * var(--total-scale-factor)) / calc(${o}px * var(--total-scale-factor))`;a.borderRadius=t}switch(e.borderStyle.style){case C:a.borderStyle="solid";break;case T:a.borderStyle="dashed";break;case M:$("Unimplemented border style: beveled");break;case P:$("Unimplemented border style: inset");break;case I:a.borderBottomStyle="solid"}const s=e.borderColor||null;s?(this.#$s=!0,a.borderColor=at.makeHexColor(0|s[0],0|s[1],0|s[2])):a.borderWidth=0}const l=at.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]),{pageWidth:h,pageHeight:d,pageX:c,pageY:u}=s.rawDims;a.left=100*(l[0]-c)/h+"%",a.top=100*(l[1]-u)/d+"%";const{rotation:p}=e;return e.hasOwnCanvas||0===p?(a.width=100*r/h+"%",a.height=100*o/d+"%"):this.setRotation(p,n),n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:i,pageHeight:s}=this.parent.viewport.rawDims;let{width:n,height:a}=this;t%180!=0&&([n,a]=[a,n]),e.style.width=100*n/i+"%",e.style.height=100*a/s+"%",e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(t,e,i)=>{const s=i.detail[t],n=s[0],a=s.slice(1);i.target.style[e]=ss[`${n}_HTML`](a),this.annotationStorage.setValue(this.data.id,{[e]:ss[`${n}_rgb`](a)})};return q(this,"_commonActions",{display:t=>{const{display:e}=t.detail,i=e%2==1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e),this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const i=this._commonActions;for(const s of Object.keys(e.detail)){const n=t[s]||i[s];n?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const i=this._commonActions;for(const[s,n]of Object.entries(e)){const a=i[s];if(a){a({detail:{[s]:n},target:t}),delete e[s]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,i,s,n]=this.data.rect.map((t=>Math.fround(t)));if(8===t.length){const[a,r,o,l]=t.subarray(2,6);if(s===a&&n===r&&e===o&&i===l)return}const{style:a}=this.container;let r;if(this.#$s){const{borderColor:t,borderWidth:e}=a;a.borderWidth=0,r=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`],this.container.classList.add("hasBorder")}const o=s-e,l=n-i,{svgFactory:h}=this,d=h.createElement("svg");d.classList.add("quadrilateralsContainer"),d.setAttribute("width",0),d.setAttribute("height",0);const c=h.createElement("defs");d.append(c);const u=h.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p),u.setAttribute("clipPathUnits","objectBoundingBox"),c.append(u);for(let i=2,s=t.length;i<s;i+=8){const s=t[i],a=t[i+1],d=t[i+2],c=t[i+3],p=h.createElement("rect"),g=(d-e)/o,m=(n-a)/l,f=(s-d)/o,b=(a-c)/l;p.setAttribute("x",g),p.setAttribute("y",m),p.setAttribute("width",f),p.setAttribute("height",b),u.append(p),r?.push(`<rect vector-effect="non-scaling-stroke" x="${g}" y="${m}" width="${f}" height="${b}"/>`)}this.#$s&&(r.push("</g></svg>')"),a.backgroundImage=r.join("")),this.container.append(d),this.container.style.clipPath=`url(#${p})`}_createPopup(){const{data:t}=this,e=this.#Gs=new ws({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation},parent:this.parent,elements:[this]});this.parent.div.append(e.render())}render(){G("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const i=[];if(this._fieldObjects){const s=this._fieldObjects[t];if(s)for(const{page:t,id:n,exportValues:a}of s){if(-1===t)continue;if(n===e)continue;const s="string"==typeof a?a:null,r=document.querySelector(`[data-element-id="${n}"]`);!r||ls.has(r)?i.push({id:n,exportValue:s,domElement:r}):$(`_getElementsByName - element not allowed: ${n}`)}return i}for(const s of document.getElementsByName(t)){const{exportValue:t}=s,n=s.getAttribute("data-element-id");n!==e&&(ls.has(s)&&i.push({id:n,exportValue:t,domElement:s}))}return i}show(){this.container&&(this.container.hidden=!1),this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0),this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",(()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})}))}get width(){return this.data.rect[2]-this.data.rect[0]}get height(){return this.data.rect[3]-this.data.rect[1]}}class cs extends ds{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0}),this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let s=!1;return t.url?(e.addLinkAttributes(i,t.url,t.newWindow),s=!0):t.action?(this._bindNamedAction(i,t.action),s=!0):t.attachment?(this.#Vs(i,t.attachment,t.attachmentDest),s=!0):t.setOCGState?(this.#Ws(i,t.setOCGState),s=!0):t.dest?(this._bindLink(i,t.dest),s=!0):(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,t),s=!0),t.resetForm?(this._bindResetFormAction(i,t.resetForm),s=!0):this.isTooltipOnly&&!s&&(this._bindLink(i,""),s=!0)),this.container.classList.add("linkAnnotation"),s&&this.container.append(i),this.container}#qs(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e),t.onclick=()=>(e&&this.linkService.goToDestination(e),!1),(e||""===e)&&this.#qs()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeNamedAction(e),!1),this.#qs()}#Vs(t,e,i=null){t.href=this.linkService.getAnchorUrl(""),e.description&&(t.title=e.description),t.onclick=()=>(this.downloadManager?.openOrDownloadData(e.content,e.filename,i),!1),this.#qs()}#Ws(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeSetOCGState(e),!1),this.#qs()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const s of Object.keys(e.actions)){const n=i.get(s);n&&(t[n]=()=>(this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:s}}),!1))}t.onclick||(t.onclick=()=>!1),this.#qs()}_bindResetFormAction(t,e){const i=t.onclick;if(i||(t.href=this.linkService.getAnchorUrl("")),this.#qs(),!this._fieldObjects)return $('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),void(i||(t.onclick=()=>!1));t.onclick=()=>{i?.();const{fields:t,refs:s,include:n}=e,a=[];if(0!==t.length||0!==s.length){const e=new Set(s);for(const i of t){const t=this._fieldObjects[i]||[];for(const{id:i}of t)e.add(i)}for(const t of Object.values(this._fieldObjects))for(const i of t)e.has(i.id)===n&&a.push(i)}else for(const t of Object.values(this._fieldObjects))a.push(...t);const r=this.annotationStorage,o=[];for(const t of a){const{id:e}=t;switch(o.push(e),t.type){case"text":{const i=t.defaultValue||"";r.setValue(e,{value:i});break}case"checkbox":case"radiobutton":{const i=t.defaultValue===t.exportValues;r.setValue(e,{value:i});break}case"combobox":case"listbox":{const i=t.defaultValue||"";r.setValue(e,{value:i});break}default:continue}const i=document.querySelector(`[data-element-id="${e}"]`);i&&(ls.has(i)?i.dispatchEvent(new Event("resetform")):$(`_bindResetFormAction - element not allowed: ${e}`))}return this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:o,name:"ResetForm"}}),!1}}}class us extends ds{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class ps extends ds{render(){return this.container}showElementAndHideCanvas(t){this.data.hasOwnCanvas&&("CANVAS"===t.previousSibling?.nodeName&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return st.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,i,s,n){i.includes("mouse")?t.addEventListener(i,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(i,(t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}n&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t)}})}))}_setEventListeners(t,e,i,s){for(const[n,a]of i)("Action"===a||this.data.actions?.[a])&&("Focus"!==a&&"Blur"!==a||(e||={focused:!1}),this._setEventListener(t,e,n,a,s),"Focus"!==a||this.data.actions?.Blur?"Blur"!==a||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":at.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:i}=this.data.defaultAppearanceData,s=this.data.defaultAppearanceData.fontSize||9,n=t.style;let r;const o=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(a*s))||1);r=Math.min(s,o(e/a))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);r=Math.min(s,o(t/a))}n.fontSize=`calc(${r}px * var(--total-scale-factor))`,n.color=at.makeHexColor(i[0],i[1],i[2]),null!==this.data.textAlignment&&(n.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class gs extends ps{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,s){const n=this.annotationStorage;for(const a of this._getElementsByName(t.name,t.id))a.domElement&&(a.domElement[e]=i),n.setValue(a.id,{[s]:i})}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){const s=t.getValue(e,{value:this.data.fieldValue});let n=s.value||"";const a=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;a&&n.length>a&&(n=n.slice(0,a));let r=s.formattedValue||this.data.textContent?.join("\n")||null;r&&this.data.comb&&(r=r.replaceAll(/\s+/g,""));const o={userValue:n,formattedValue:r,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(i=document.createElement("textarea"),i.textContent=r??n,this.data.doNotScroll&&(i.style.overflowY="hidden")):(i=document.createElement("input"),i.type=this.data.password?"password":"text",i.setAttribute("value",r??n),this.data.doNotScroll&&(i.style.overflowX="hidden")),this.data.hasOwnCanvas&&(i.hidden=!0),ls.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,i.name=this.data.fieldName,i.tabIndex=os,this._setRequired(i,this.data.required),a&&(i.maxLength=a),i.addEventListener("input",(s=>{t.setValue(e,{value:s.target.value}),this.setPropertyOnSiblings(i,"value",s.target.value,"value"),o.formattedValue=null})),i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";i.value=o.userValue=e,o.formattedValue=null}));let l=t=>{const{formattedValue:e}=o;null!=e&&(t.target.value=e),t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",(t=>{if(o.focused)return;const{target:e}=t;o.userValue&&(e.value=o.userValue),o.lastCommittedValue=e.value,o.commitKey=1,this.data.actions?.Focus||(o.focused=!0)})),i.addEventListener("updatefromsandbox",(i=>{this.showElementAndHideCanvas(i.target);const s={value(i){o.userValue=i.detail.value??"",t.setValue(e,{value:o.userValue.toString()}),i.target.value=o.userValue},formattedValue(i){const{formattedValue:s}=i.detail;o.formattedValue=s,null!=s&&i.target!==document.activeElement&&(i.target.value=s),t.setValue(e,{formattedValue:s})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{const{charLimit:s}=i.detail,{target:n}=i;if(0===s)return void n.removeAttribute("maxLength");n.setAttribute("maxLength",s);let a=o.userValue;!a||a.length<=s||(a=a.slice(0,s),n.value=o.userValue=a,t.setValue(e,{value:a}),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,willCommit:!0,commitKey:1,selStart:n.selectionStart,selEnd:n.selectionEnd}}))}};this._dispatchEventFromSandbox(s,i)})),i.addEventListener("keydown",(t=>{o.commitKey=1;let i=-1;if("Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):i=2,-1===i)return;const{value:s}=t.target;o.lastCommittedValue!==s&&(o.lastCommittedValue=s,o.userValue=s,this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}))}));const s=l;l=null,i.addEventListener("blur",(t=>{if(!o.focused||!t.relatedTarget)return;this.data.actions?.Blur||(o.focused=!1);const{value:i}=t.target;o.userValue=i,o.lastCommittedValue!==i&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}),s(t)})),this.data.actions?.Keystroke&&i.addEventListener("beforeinput",(t=>{o.lastCommittedValue=null;const{data:i,target:s}=t,{value:n,selectionStart:a,selectionEnd:r}=s;let l=a,h=r;switch(t.inputType){case"deleteWordBackward":{const t=n.substring(0,a).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{const t=n.substring(a).match(/^[^\w]*\w*/);t&&(h+=t[0].length);break}case"deleteContentBackward":a===r&&(l-=1);break;case"deleteContentForward":a===r&&(h+=1)}t.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,change:i||"",willCommit:!1,selStart:l,selEnd:h}})})),this._setEventListeners(i,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}if(l&&i.addEventListener("blur",l),this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/a;i.classList.add("comb"),i.style.letterSpacing=`calc(${t}px * var(--total-scale-factor) - 1ch)`}}else i=document.createElement("div"),i.textContent=this.data.fieldValue,i.style.verticalAlign="middle",i.style.display="table-cell",this.data.hasOwnCanvas&&(i.hidden=!0);return this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class ms extends ps{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class fs extends ps{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;"string"==typeof s&&(s="Off"!==s,t.setValue(i,{value:s})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");return ls.add(n),n.setAttribute("data-element-id",i),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="checkbox",n.name=e.fieldName,s&&n.setAttribute("checked",!0),n.setAttribute("exportValue",e.exportValue),n.tabIndex=os,n.addEventListener("change",(s=>{const{name:n,checked:a}=s.target;for(const s of this._getElementsByName(n,i)){const i=a&&s.exportValue===e.exportValue;s.domElement&&(s.domElement.checked=i),t.setValue(s.id,{value:i})}t.setValue(i,{value:a})})),n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue})),this.enableScripting&&this.hasJSActions&&(n.addEventListener("updatefromsandbox",(e=>{const s={value(e){e.target.checked="Off"!==e.detail.value,t.setValue(i,{value:e.target.checked})}};this._dispatchEventFromSandbox(s,e)})),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class bs extends ps{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof s&&(s=s!==e.buttonValue,t.setValue(i,{value:s})),s)for(const s of this._getElementsByName(e.fieldName,i))t.setValue(s.id,{value:!1});const n=document.createElement("input");if(ls.add(n),n.setAttribute("data-element-id",i),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="radio",n.name=e.fieldName,s&&n.setAttribute("checked",!0),n.tabIndex=os,n.addEventListener("change",(e=>{const{name:s,checked:n}=e.target;for(const e of this._getElementsByName(s,i))t.setValue(e.id,{value:!1});t.setValue(i,{value:n})})),n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue})),this.enableScripting&&this.hasJSActions){const s=e.buttonValue;n.addEventListener("updatefromsandbox",(e=>{const n={value:e=>{const n=s===e.detail.value;for(const s of this._getElementsByName(e.target.name)){const e=n&&s.id===i;s.domElement&&(s.domElement.checked=e),t.setValue(s.id,{value:e})}}};this._dispatchEventFromSandbox(n,e)})),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}return this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class vs extends cs{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))),t}}class As extends ps{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),s=document.createElement("select");ls.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,this._setRequired(s,this.data.required),s.name=this.data.fieldName,s.tabIndex=os;let n=this.data.combo&&this.data.options.length>0;this.data.combo||(s.size=this.data.options.length,this.data.multiSelect&&(s.multiple=!0)),s.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const t of s.options)t.selected=t.value===e}));for(const t of this.data.options){const e=document.createElement("option");e.textContent=t.displayValue,e.value=t.exportValue,i.value.includes(t.exportValue)&&(e.setAttribute("selected",!0),n=!1),s.append(e)}let a=null;if(n){const t=document.createElement("option");t.value=" ",t.setAttribute("hidden",!0),t.setAttribute("selected",!0),s.prepend(t),a=()=>{t.remove(),s.removeEventListener("input",a),a=null},s.addEventListener("input",a)}const r=t=>{const e=t?"value":"textContent",{options:i,multiple:n}=s;return n?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[e])):-1===i.selectedIndex?null:i[i.selectedIndex][e]};let o=r(!1);const l=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};return this.enableScripting&&this.hasJSActions?(s.addEventListener("updatefromsandbox",(i=>{const n={value(i){a?.();const n=i.detail.value,l=new Set(Array.isArray(n)?n:[n]);for(const t of s.options)t.selected=l.has(t.value);t.setValue(e,{value:r(!0)}),o=r(!1)},multipleSelection(t){s.multiple=!0},remove(i){const n=s.options,a=i.detail.remove;if(n[a].selected=!1,s.remove(a),n.length>0){-1===Array.prototype.findIndex.call(n,(t=>t.selected))&&(n[0].selected=!0)}t.setValue(e,{value:r(!0),items:l(i)}),o=r(!1)},clear(i){for(;0!==s.length;)s.remove(0);t.setValue(e,{value:null,items:[]}),o=r(!1)},insert(i){const{index:n,displayValue:a,exportValue:h}=i.detail.insert,d=s.children[n],c=document.createElement("option");c.textContent=a,c.value=h,d?d.before(c):s.append(c),t.setValue(e,{value:r(!0),items:l(i)}),o=r(!1)},items(i){const{items:n}=i.detail;for(;0!==s.length;)s.remove(0);for(const t of n){const{displayValue:e,exportValue:i}=t,n=document.createElement("option");n.textContent=e,n.value=i,s.append(n)}s.options.length>0&&(s.options[0].selected=!0),t.setValue(e,{value:r(!0),items:l(i)}),o=r(!1)},indices(i){const s=new Set(i.detail.indices);for(const t of i.target.options)t.selected=s.has(t.index);t.setValue(e,{value:r(!0)}),o=r(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(n,i)})),s.addEventListener("input",(i=>{const s=r(!0),n=r(!1);t.setValue(e,{value:s}),i.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:o,change:n,changeEx:s,willCommit:!1,commitKey:1,keyDown:!1}})})),this._setEventListeners(s,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))):s.addEventListener("input",(function(i){t.setValue(e,{value:r(!0)})})),this.data.combo&&this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class ws extends ds{constructor(t){const{data:e,elements:i}=t;super(t,{isRenderable:ds._hasPopupData(e)}),this.elements=i,this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new ys({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const i of this.elements)i.popup=t,i.container.ariaHasPopup="dialog",e.push(i.data.id),i.addHighlightArea();return this.container.setAttribute("aria-controls",e.map((t=>`${dt}${t}`)).join(",")),this.container}}class ys{#Xs=this.#Ks.bind(this);#Ys=this.#Qs.bind(this);#Js=this.#Zs.bind(this);#tn=this.#en.bind(this);#in=null;#ft=null;#sn=null;#nn=null;#an=null;#rn=null;#on=null;#ln=!1;#hn=null;#T=null;#dn=null;#cn=null;#un=null;#Us=null;#pn=!1;constructor({container:t,color:e,elements:i,titleObj:s,modificationDate:n,contentsObj:a,richText:r,parent:o,rect:l,parentRect:h,open:d}){this.#ft=t,this.#un=s,this.#sn=a,this.#cn=r,this.#rn=o,this.#in=e,this.#dn=l,this.#on=h,this.#an=i,this.#nn=Ct.toDateObject(n),this.trigger=i.flatMap((t=>t.getElementsToTriggerPopup()));for(const t of this.trigger)t.addEventListener("click",this.#tn),t.addEventListener("mouseenter",this.#Js),t.addEventListener("mouseleave",this.#Ys),t.classList.add("popupTriggerArea");for(const t of i)t.container?.addEventListener("keydown",this.#Xs);this.#ft.hidden=!0,d&&this.#en()}render(){if(this.#hn)return;const t=this.#hn=document.createElement("div");if(t.className="popup",this.#in){const e=t.style.outlineColor=at.makeHexColor(...this.#in);t.style.backgroundColor=`color-mix(in srgb, ${e} 30%, white)`}const e=document.createElement("span");e.className="header";const i=document.createElement("h1");if(e.append(i),({dir:i.dir,str:i.textContent}=this.#un),t.append(e),this.#nn){const t=document.createElement("span");t.classList.add("popupDate"),t.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string"),t.setAttribute("data-l10n-args",JSON.stringify({dateObj:this.#nn.valueOf()})),e.append(t)}const s=this.#gn;if(s)rs.render({xfaHtml:s,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const e=this._formatContents(this.#sn);t.append(e)}this.#ft.append(t)}get#gn(){const t=this.#cn,e=this.#sn;return!t?.str||e?.str&&e.str!==t.str?null:this.#cn.html||null}get#mn(){return this.#gn?.attributes?.style?.fontSize||0}get#fn(){return this.#gn?.attributes?.style?.color||null}#bn(t){const e=[],i={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},s={style:{color:this.#fn,fontSize:this.#mn?`calc(${this.#mn}px * var(--total-scale-factor))`:""}};for(const i of t.split("\n"))e.push({name:"span",value:i,attributes:s});return i}_formatContents({str:t,dir:e}){const i=document.createElement("p");i.classList.add("popupContent"),i.dir=e;const s=t.split(/(?:\r\n?|\n)/);for(let t=0,e=s.length;t<e;++t){const n=s[t];i.append(document.createTextNode(n)),t<e-1&&i.append(document.createElement("br"))}return i}#Ks(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#ln)&&this.#en()}updateEdited({rect:t,popupContent:e}){this.#Us||={contentsObj:this.#sn,richText:this.#cn},t&&(this.#T=null),e&&(this.#cn=this.#bn(e),this.#sn=null),this.#hn?.remove(),this.#hn=null}resetEdited(){this.#Us&&(({contentsObj:this.#sn,richText:this.#cn}=this.#Us),this.#Us=null,this.#hn?.remove(),this.#hn=null,this.#T=null)}#vn(){if(null!==this.#T)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:i,pageX:s,pageY:n}}}=this.#rn;let a=!!this.#on,r=a?this.#on:this.#dn;for(const t of this.#an)if(!r||null!==at.intersect(t.data.rect,r)){r=t.data.rect,a=!0;break}const o=at.normalizeRect([r[0],t[3]-r[1]+t[1],r[2],t[3]-r[3]+t[1]]),l=a?r[2]-r[0]+5:0,h=o[0]+l,d=o[1];this.#T=[100*(h-s)/e,100*(d-n)/i];const{style:c}=this.#ft;c.left=`${this.#T[0]}%`,c.top=`${this.#T[1]}%`}#en(){this.#ln=!this.#ln,this.#ln?(this.#Zs(),this.#ft.addEventListener("click",this.#tn),this.#ft.addEventListener("keydown",this.#Xs)):(this.#Qs(),this.#ft.removeEventListener("click",this.#tn),this.#ft.removeEventListener("keydown",this.#Xs))}#Zs(){this.#hn||this.render(),this.isVisible?this.#ln&&this.#ft.classList.add("focused"):(this.#vn(),this.#ft.hidden=!1,this.#ft.style.zIndex=parseInt(this.#ft.style.zIndex)+1e3)}#Qs(){this.#ft.classList.remove("focused"),!this.#ln&&this.isVisible&&(this.#ft.hidden=!0,this.#ft.style.zIndex=parseInt(this.#ft.style.zIndex)-1e3)}forceHide(){this.#pn=this.isVisible,this.#pn&&(this.#ft.hidden=!0)}maybeShow(){this.#pn&&(this.#hn||this.#Zs(),this.#pn=!1,this.#ft.hidden=!1)}get isVisible(){return!1===this.#ft.hidden}}class _s extends ds{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=m.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const i=document.createElement("span");i.textContent=e,t.append(i)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class xs extends ds{#An=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),n=this.#An=this.svgFactory.createElement("svg:line");return n.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]),n.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]),n.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]),n.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]),n.setAttribute("stroke-width",t.borderStyle.width||1),n.setAttribute("stroke","transparent"),n.setAttribute("fill","transparent"),s.append(n),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#An}addHighlightArea(){this.container.classList.add("highlightArea")}}class Ss extends ds{#wn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,a=this.#wn=this.svgFactory.createElement("svg:rect");return a.setAttribute("x",n/2),a.setAttribute("y",n/2),a.setAttribute("width",e-n),a.setAttribute("height",i-n),a.setAttribute("stroke-width",n||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),s.append(a),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#wn}addHighlightArea(){this.container.classList.add("highlightArea")}}class Es extends ds{#yn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const{data:t,width:e,height:i}=this,s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,a=this.#yn=this.svgFactory.createElement("svg:ellipse");return a.setAttribute("cx",e/2),a.setAttribute("cy",i/2),a.setAttribute("rx",e/2-n/2),a.setAttribute("ry",i/2-n/2),a.setAttribute("stroke-width",n||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),s.append(a),this.container.append(s),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#yn}addHighlightArea(){this.container.classList.add("highlightArea")}}class Cs extends ds{#_n=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,vertices:e,borderStyle:i,popupRef:s},width:n,height:a}=this;if(!e)return this.container;const r=this.svgFactory.create(n,a,!0);let o=[];for(let i=0,s=e.length;i<s;i+=2){const s=e[i]-t[0],n=t[3]-e[i+1];o.push(`${s},${n}`)}o=o.join(" ");const l=this.#_n=this.svgFactory.createElement(this.svgElementName);return l.setAttribute("points",o),l.setAttribute("stroke-width",i.width||1),l.setAttribute("stroke","transparent"),l.setAttribute("fill","transparent"),r.append(l),this.container.append(r),!s&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#_n}addHighlightArea(){this.container.classList.add("highlightArea")}}class Ts extends Cs{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class Ms extends ds{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class Ps extends ds{#xn=null;#Sn=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType="InkHighlight"===this.data.it?m.HIGHLIGHT:m.INK}#En(t,e){switch(t){case 90:return{transform:`rotate(90) translate(${-e[0]},${e[1]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};case 180:return{transform:`rotate(180) translate(${-e[2]},${e[1]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]};case 270:return{transform:`rotate(270) translate(${-e[2]},${e[3]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};default:return{transform:`translate(${-e[0]},${e[3]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]}}}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,rotation:e,inkLists:i,borderStyle:s,popupRef:n}}=this,{transform:a,width:r,height:o}=this.#En(e,t),l=this.svgFactory.create(r,o,!0),h=this.#xn=this.svgFactory.createElement("svg:g");l.append(h),h.setAttribute("stroke-width",s.width||1),h.setAttribute("stroke-linecap","round"),h.setAttribute("stroke-linejoin","round"),h.setAttribute("stroke-miterlimit",10),h.setAttribute("stroke","transparent"),h.setAttribute("fill","transparent"),h.setAttribute("transform",a);for(let t=0,e=i.length;t<e;t++){const e=this.svgFactory.createElement(this.svgElementName);this.#Sn.push(e),e.setAttribute("points",i[t].join(",")),h.append(e)}return!n&&this.hasPopupData&&this._createPopup(),this.container.append(l),this._editOnDoubleClick(),this.container}updateEdited(t){super.updateEdited(t);const{thickness:e,points:i,rect:s}=t,n=this.#xn;if(e>=0&&n.setAttribute("stroke-width",e||1),i)for(let t=0,e=this.#Sn.length;t<e;t++)this.#Sn[t].setAttribute("points",i[t].join(","));if(s){const{transform:t,width:e,height:i}=this.#En(this.data.rotation,s);n.parentElement.setAttribute("viewBox",`0 0 ${e} ${i}`),n.setAttribute("transform",t)}}getElementsToTriggerPopup(){return this.#Sn}addHighlightArea(){this.container.classList.add("highlightArea")}}class Is extends ds{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}),this.annotationEditorType=m.HIGHLIGHT}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this._editOnDoubleClick(),this.container}}class ks extends ds{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class Ds extends ds{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class Rs extends ds{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class Ls extends ds{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.annotationEditorType=m.STAMP}render(){return this.container.classList.add("stampAnnotation"),this.container.setAttribute("role","img"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class Fs extends ds{#Cn=null;constructor(t){super(t,{isRenderable:!0});const{file:e}=this.data;this.filename=e.filename,this.content=e.content,this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let i;e.hasAppearance||0===e.fillAlpha?i=document.createElement("div"):(i=document.createElement("img"),i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`,e.fillAlpha&&e.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)),i.addEventListener("dblclick",this.#Tn.bind(this)),this.#Cn=i;const{isMac:s}=st.platform;return t.addEventListener("keydown",(t=>{"Enter"===t.key&&(s?t.metaKey:t.ctrlKey)&&this.#Tn()})),!e.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea"),t.append(i),t}getElementsToTriggerPopup(){return this.#Cn}addHighlightArea(){this.container.classList.add("highlightArea")}#Tn(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class Ns{#Mn=null;#Pn=null;#In=new Map;#kn=null;constructor({div:t,accessibilityManager:e,annotationCanvasMap:i,annotationEditorUIManager:s,page:n,viewport:a,structTreeLayer:r}){this.div=t,this.#Mn=e,this.#Pn=i,this.#kn=r||null,this.page=n,this.viewport=a,this.zIndex=0,this._annotationEditorUIManager=s}hasEditableAnnotations(){return this.#In.size>0}async#Dn(t,e){const i=t.firstChild||t,s=i.id=`${dt}${e}`,n=await(this.#kn?.getAriaAttributes(s));if(n)for(const[t,e]of n)i.setAttribute(t,e);this.div.append(t),this.#Mn?.moveElementInDOM(this.div,t,i,!1)}async render(t){const{annotations:e}=t,i=this.div;kt(i,this.viewport);const s=new Map,n={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new as,annotationStorage:t.annotationStorage||new Jt,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const t of e){if(t.noHTML)continue;const e=t.annotationType===E.POPUP;if(e){const e=s.get(t.id);if(!e)continue;n.elements=e}else if(t.rect[2]===t.rect[0]||t.rect[3]===t.rect[1])continue;n.data=t;const i=hs.create(n);if(!i.isRenderable)continue;if(!e&&t.popupRef){const e=s.get(t.popupRef);e?e.push(i):s.set(t.popupRef,[i])}const a=i.render();t.hidden&&(a.style.visibility="hidden"),await this.#Dn(a,t.id),i._isEditable&&(this.#In.set(i.data.id,i),this._annotationEditorUIManager?.renderAnnotationElement(i))}this.#Rn()}async addLinkAnnotations(t,e){const i={data:null,layer:this.div,linkService:e,svgFactory:new as,parent:this};for(const e of t){e.borderStyle||=Ns._defaultBorderStyle,i.data=e;const t=hs.create(i);if(!t.isRenderable)continue;const s=t.render();await this.#Dn(s,e.id)}}update({viewport:t}){const e=this.div;this.viewport=t,kt(e,{rotation:t.rotation}),this.#Rn(),e.hidden=!1}#Rn(){if(!this.#Pn)return;const t=this.div;for(const[e,i]of this.#Pn){const s=t.querySelector(`[data-annotation-id="${e}"]`);if(!s)continue;i.className="annotationContent";const{firstChild:n}=s;n?"CANVAS"===n.nodeName?n.replaceWith(i):n.classList.contains("annotationContent")?n.after(i):n.before(i):s.append(i);const a=this.#In.get(e);a&&(a._hasNoCanvas?(this._annotationEditorUIManager?.setMissingCanvas(e,s.id,i),a._hasNoCanvas=!1):a.canvas=i)}this.#Pn.clear()}getEditableAnnotations(){return Array.from(this.#In.values())}getEditableAnnotation(t){return this.#In.get(t)}static get _defaultBorderStyle(){return q(this,"_defaultBorderStyle",Object.freeze({width:1,rawWidth:1,style:C,dashArray:[3],horizontalCornerRadius:0,verticalCornerRadius:0}))}}const Os=/\r\n?|\n/g;class Bs extends Vt{#in;#Ln="";#Fn=`${this.id}-editor`;#Nn=null;#mn;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=Bs.prototype,e=t=>t.isEmpty(),i=$t.TRANSLATE_SMALL,s=$t.TRANSLATE_BIG;return q(this,"_keyboardManager",new zt([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-s,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[s,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-s],checker:e}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,s],checker:e}]]))}static _type="freetext";static _editorType=m.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"}),this.#in=t.color||Bs._defaultColor||Vt._defaultLineColor,this.#mn=t.fontSize||Bs._defaultFontSize}static initialize(t,e){Vt.initialize(t,e);const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case f.FREETEXT_SIZE:Bs._defaultFontSize=e;break;case f.FREETEXT_COLOR:Bs._defaultColor=e}}updateParams(t,e){switch(t){case f.FREETEXT_SIZE:this.#On(e);break;case f.FREETEXT_COLOR:this.#Bn(e)}}static get defaultPropertiesToUpdate(){return[[f.FREETEXT_SIZE,Bs._defaultFontSize],[f.FREETEXT_COLOR,Bs._defaultColor||Vt._defaultLineColor]]}get propertiesToUpdate(){return[[f.FREETEXT_SIZE,this.#mn],[f.FREETEXT_COLOR,this.#in]]}#On(t){const e=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--total-scale-factor))`,this.translate(0,-(t-this.#mn)*this.parentScale),this.#mn=t,this.#Hn()},i=this.#mn;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#Bn(t){const e=t=>{this.#in=this.editorDiv.style.color=t},i=this.#in;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-Bs._internalPadding*t,-(Bs._internalPadding+this.#mn)*t]}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(this.isInEditMode())return;this.parent.setEditingState(!1),this.parent.updateToolbar(m.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.#Nn=new AbortController;const t=this._uiManager.combinedSignal(this.#Nn);this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:t}),this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:t}),this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:t}),this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:t}),this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:t})}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",this.#Fn),this._isDraggable=!0,this.#Nn?.abort(),this.#Nn=null,this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"))}focusin(t){this._focusEventsAllowed&&(super.focusin(t),t.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(t){this.width||(this.enableEditMode(),t&&this.editorDiv.focus(),this._initialOptions?.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}#zn(){const t=[];this.editorDiv.normalize();let e=null;for(const i of this.editorDiv.childNodes)e?.nodeType===Node.TEXT_NODE&&"BR"===i.nodeName||(t.push(Bs.#Un(i)),e=i);return t.join("\n")}#Hn(){const[t,e]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,s=e.style.display,n=e.classList.contains("hidden");e.classList.remove("hidden"),e.style.display="hidden",t.div.append(this.div),i=e.getBoundingClientRect(),e.remove(),e.style.display=s,e.classList.toggle("hidden",n)}this.rotation%180==this.parentRotation%180?(this.width=i.width/t,this.height=i.height/e):(this.width=i.height/t,this.height=i.width/e),this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const t=this.#Ln,e=this.#Ln=this.#zn().trimEnd();if(t===e)return;const i=t=>{this.#Ln=t,t?(this.#$n(),this._uiManager.rebuild(this),this.#Hn()):this.remove()};this.addCommands({cmd:()=>{i(e)},undo:()=>{i(t)},mustExec:!1}),this.#Hn()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(t){this.enterInEditMode()}keydown(t){t.target===this.div&&"Enter"===t.key&&(this.enterInEditMode(),t.preventDefault())}editorDivKeydown(t){Bs._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let t,e;(this._isCopy||this.annotationElementId)&&(t=this.x,e=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",this.#Fn),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2"),this.editorDiv.setAttribute("data-l10n-attrs","default-content"),this.enableEditing(),this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;if(i.fontSize=`calc(${this.#mn}px * var(--total-scale-factor))`,i.color=this.#in,this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),Nt(this,this.div,["dblclick","keydown"]),this._isCopy||this.annotationElementId){const[i,s]=this.parentDimensions;if(this.annotationElementId){const{position:n}=this._initialData;let[a,r]=this.getInitialTranslation();[a,r]=this.pageTranslationToScreen(a,r);const[o,l]=this.pageDimensions,[h,d]=this.pageTranslation;let c,u;switch(this.rotation){case 0:c=t+(n[0]-h)/o,u=e+this.height-(n[1]-d)/l;break;case 90:c=t+(n[0]-h)/o,u=e-(n[1]-d)/l,[a,r]=[r,-a];break;case 180:c=t-this.width+(n[0]-h)/o,u=e-(n[1]-d)/l,[a,r]=[-a,-r];break;case 270:c=t+(n[0]-h-this.height*l)/o,u=e+(n[1]-d-this.width*o)/l,[a,r]=[-r,a]}this.setAt(c*i,u*s,a,r)}else this._moveAfterPaste(t,e);this.#$n(),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}static#Un(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(Os,"")}editorDivPaste(t){const e=t.clipboardData||window.clipboardData,{types:i}=e;if(1===i.length&&"text/plain"===i[0])return;t.preventDefault();const s=Bs.#Gn(e.getData("text")||"").replaceAll(Os,"\n");if(!s)return;const n=window.getSelection();if(!n.rangeCount)return;this.editorDiv.normalize(),n.deleteFromDocument();const a=n.getRangeAt(0);if(!s.includes("\n"))return a.insertNode(document.createTextNode(s)),this.editorDiv.normalize(),void n.collapseToStart();const{startContainer:r,startOffset:o}=a,l=[],h=[];if(r.nodeType===Node.TEXT_NODE){const t=r.parentElement;if(h.push(r.nodeValue.slice(o).replaceAll(Os,"")),t!==this.editorDiv){let e=l;for(const i of this.editorDiv.childNodes)i!==t?e.push(Bs.#Un(i)):e=h}l.push(r.nodeValue.slice(0,o).replaceAll(Os,""))}else if(r===this.editorDiv){let t=l,e=0;for(const i of this.editorDiv.childNodes)e++===o&&(t=h),t.push(Bs.#Un(i))}this.#Ln=`${l.join("\n")}${s}${h.join("\n")}`,this.#$n();const d=new Range;let c=Math.sumPrecise(l.map((t=>t.length)));for(const{firstChild:t}of this.editorDiv.childNodes)if(t.nodeType===Node.TEXT_NODE){const e=t.nodeValue.length;if(c<=e){d.setStart(t,c),d.setEnd(t,c);break}c-=e}n.removeAllRanges(),n.addRange(d)}#$n(){if(this.editorDiv.replaceChildren(),this.#Ln)for(const t of this.#Ln.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br")),this.editorDiv.append(e)}}#jn(){return this.#Ln.replaceAll(" "," ")}static#Gn(t){return t.replaceAll(" "," ")}get contentDiv(){return this.editorDiv}static async deserialize(t,e,i){let s=null;if(t instanceof _s){const{data:{defaultAppearanceData:{fontSize:e,fontColor:i},rect:n,rotation:a,id:r,popupRef:o},textContent:l,textPosition:h,parent:{page:{pageNumber:d}}}=t;if(!l||0===l.length)return null;s=t={annotationType:m.FREETEXT,color:Array.from(i),fontSize:e,value:l.join("\n"),position:h,pageIndex:d-1,rect:n.slice(0),rotation:a,id:r,deleted:!1,popupRef:o}}const n=await super.deserialize(t,e,i);return n.#mn=t.fontSize,n.#in=at.makeHexColor(...t.color),n.#Ln=Bs.#Gn(t.value),n.annotationElementId=t.id||null,n._initialData=s,n}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const e=Bs._internalPadding*this.parentScale,i=this.getRect(e,e),s=Vt._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#in),n={annotationType:m.FREETEXT,color:s,fontSize:this.#mn,value:this.#jn(),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(n.isCopy=!0,n):this.annotationElementId&&!this.#Vn(n)?null:(n.id=this.annotationElementId,n)}#Vn(t){const{value:e,fontSize:i,color:s,pageIndex:n}=this._initialData;return this._hasBeenMoved||t.value!==e||t.fontSize!==i||t.color.some(((t,e)=>t!==s[e]))||t.pageIndex!==n}renderAnnotationElement(t){const e=super.renderAnnotationElement(t);if(this.deleted)return e;const{style:i}=e;i.fontSize=`calc(${this.#mn}px * var(--total-scale-factor))`,i.color=this.#in,e.replaceChildren();for(const t of this.#Ln.split("\n")){const i=document.createElement("div");i.append(t?document.createTextNode(t):document.createElement("br")),e.append(i)}const s=Bs._internalPadding*this.parentScale;return t.updateEdited({rect:this.getRect(s,s),popupContent:this.#Ln}),e}resetAnnotationElement(t){super.resetAnnotationElement(t),t.resetEdited()}}class Hs{static PRECISION=1e-4;toSVGPath(){G("Abstract method `toSVGPath` must be implemented.")}get box(){G("Abstract getter `box` must be implemented.")}serialize(t,e){G("Abstract method `serialize` must be implemented.")}static _rescale(t,e,i,s,n,a){a||=new Float32Array(t.length);for(let r=0,o=t.length;r<o;r+=2)a[r]=e+t[r]*s,a[r+1]=i+t[r+1]*n;return a}static _rescaleAndSwap(t,e,i,s,n,a){a||=new Float32Array(t.length);for(let r=0,o=t.length;r<o;r+=2)a[r]=e+t[r+1]*s,a[r+1]=i+t[r]*n;return a}static _translate(t,e,i,s){s||=new Float32Array(t.length);for(let n=0,a=t.length;n<a;n+=2)s[n]=e+t[n],s[n+1]=i+t[n+1];return s}static svgRound(t){return Math.round(1e4*t)}static _normalizePoint(t,e,i,s,n){switch(n){case 90:return[1-e/i,t/s];case 180:return[1-t/i,1-e/s];case 270:return[e/i,1-t/s];default:return[t/i,e/s]}}static _normalizePagePoint(t,e,i){switch(i){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,i,s,n,a){return[(t+5*i)/6,(e+5*s)/6,(5*i+n)/6,(5*s+a)/6,(i+n)/2,(s+a)/2]}}class zs{#Wn;#qn=[];#Xn;#Kn;#Yn=[];#Qn=new Float32Array(18);#Jn;#Zn;#ta;#ea;#ia;#sa;#na=[];static#aa=8;static#ra=2;static#oa=zs.#aa+zs.#ra;constructor({x:t,y:e},i,s,n,a,r=0){this.#Wn=i,this.#sa=n*s,this.#Kn=a,this.#Qn.set([NaN,NaN,NaN,NaN,t,e],6),this.#Xn=r,this.#ea=zs.#aa*s,this.#ta=zs.#oa*s,this.#ia=s,this.#na.push(t,e)}isEmpty(){return isNaN(this.#Qn[8])}#la(){const t=this.#Qn.subarray(4,6),e=this.#Qn.subarray(16,18),[i,s,n,a]=this.#Wn;return[(this.#Jn+(t[0]-e[0])/2-i)/n,(this.#Zn+(t[1]-e[1])/2-s)/a,(this.#Jn+(e[0]-t[0])/2-i)/n,(this.#Zn+(e[1]-t[1])/2-s)/a]}add({x:t,y:e}){this.#Jn=t,this.#Zn=e;const[i,s,n,a]=this.#Wn;let[r,o,l,h]=this.#Qn.subarray(8,12);const d=t-l,c=e-h,u=Math.hypot(d,c);if(u<this.#ta)return!1;const p=u-this.#ea,g=p/u,m=g*d,f=g*c;let b=r,v=o;r=l,o=h,l+=m,h+=f,this.#na?.push(t,e);const A=m/p,w=-f/p*this.#sa,y=A*this.#sa;if(this.#Qn.set(this.#Qn.subarray(2,8),0),this.#Qn.set([l+w,h+y],4),this.#Qn.set(this.#Qn.subarray(14,18),12),this.#Qn.set([l-w,h-y],16),isNaN(this.#Qn[6]))return 0===this.#Yn.length&&(this.#Qn.set([r+w,o+y],2),this.#Yn.push(NaN,NaN,NaN,NaN,(r+w-i)/n,(o+y-s)/a),this.#Qn.set([r-w,o-y],14),this.#qn.push(NaN,NaN,NaN,NaN,(r-w-i)/n,(o-y-s)/a)),this.#Qn.set([b,v,r,o,l,h],6),!this.isEmpty();this.#Qn.set([b,v,r,o,l,h],6);return Math.abs(Math.atan2(v-o,b-r)-Math.atan2(f,m))<Math.PI/2?([r,o,l,h]=this.#Qn.subarray(2,6),this.#Yn.push(NaN,NaN,NaN,NaN,((r+l)/2-i)/n,((o+h)/2-s)/a),[r,o,b,v]=this.#Qn.subarray(14,18),this.#qn.push(NaN,NaN,NaN,NaN,((b+r)/2-i)/n,((v+o)/2-s)/a),!0):([b,v,r,o,l,h]=this.#Qn.subarray(0,6),this.#Yn.push(((b+5*r)/6-i)/n,((v+5*o)/6-s)/a,((5*r+l)/6-i)/n,((5*o+h)/6-s)/a,((r+l)/2-i)/n,((o+h)/2-s)/a),[l,h,r,o,b,v]=this.#Qn.subarray(12,18),this.#qn.push(((b+5*r)/6-i)/n,((v+5*o)/6-s)/a,((5*r+l)/6-i)/n,((5*o+h)/6-s)/a,((r+l)/2-i)/n,((o+h)/2-s)/a),!0)}toSVGPath(){if(this.isEmpty())return"";const t=this.#Yn,e=this.#qn;if(isNaN(this.#Qn[6])&&!this.isEmpty())return this.#ha();const i=[];i.push(`M${t[4]} ${t[5]}`);for(let e=6;e<t.length;e+=6)isNaN(t[e])?i.push(`L${t[e+4]} ${t[e+5]}`):i.push(`C${t[e]} ${t[e+1]} ${t[e+2]} ${t[e+3]} ${t[e+4]} ${t[e+5]}`);this.#da(i);for(let t=e.length-6;t>=6;t-=6)isNaN(e[t])?i.push(`L${e[t+4]} ${e[t+5]}`):i.push(`C${e[t]} ${e[t+1]} ${e[t+2]} ${e[t+3]} ${e[t+4]} ${e[t+5]}`);return this.#ca(i),i.join(" ")}#ha(){const[t,e,i,s]=this.#Wn,[n,a,r,o]=this.#la();return`M${(this.#Qn[2]-t)/i} ${(this.#Qn[3]-e)/s} L${(this.#Qn[4]-t)/i} ${(this.#Qn[5]-e)/s} L${n} ${a} L${r} ${o} L${(this.#Qn[16]-t)/i} ${(this.#Qn[17]-e)/s} L${(this.#Qn[14]-t)/i} ${(this.#Qn[15]-e)/s} Z`}#ca(t){const e=this.#qn;t.push(`L${e[4]} ${e[5]} Z`)}#da(t){const[e,i,s,n]=this.#Wn,a=this.#Qn.subarray(4,6),r=this.#Qn.subarray(16,18),[o,l,h,d]=this.#la();t.push(`L${(a[0]-e)/s} ${(a[1]-i)/n} L${o} ${l} L${h} ${d} L${(r[0]-e)/s} ${(r[1]-i)/n}`)}newFreeDrawOutline(t,e,i,s,n,a){return new Us(t,e,i,s,n,a)}getOutlines(){const t=this.#Yn,e=this.#qn,i=this.#Qn,[s,n,a,r]=this.#Wn,o=new Float32Array((this.#na?.length??0)+2);for(let t=0,e=o.length-2;t<e;t+=2)o[t]=(this.#na[t]-s)/a,o[t+1]=(this.#na[t+1]-n)/r;if(o[o.length-2]=(this.#Jn-s)/a,o[o.length-1]=(this.#Zn-n)/r,isNaN(i[6])&&!this.isEmpty())return this.#ua(o);const l=new Float32Array(this.#Yn.length+24+this.#qn.length);let h=t.length;for(let e=0;e<h;e+=2)isNaN(t[e])?l[e]=l[e+1]=NaN:(l[e]=t[e],l[e+1]=t[e+1]);h=this.#pa(l,h);for(let t=e.length-6;t>=6;t-=6)for(let i=0;i<6;i+=2)isNaN(e[t+i])?(l[h]=l[h+1]=NaN,h+=2):(l[h]=e[t+i],l[h+1]=e[t+i+1],h+=2);return this.#ga(l,h),this.newFreeDrawOutline(l,o,this.#Wn,this.#ia,this.#Xn,this.#Kn)}#ua(t){const e=this.#Qn,[i,s,n,a]=this.#Wn,[r,o,l,h]=this.#la(),d=new Float32Array(36);return d.set([NaN,NaN,NaN,NaN,(e[2]-i)/n,(e[3]-s)/a,NaN,NaN,NaN,NaN,(e[4]-i)/n,(e[5]-s)/a,NaN,NaN,NaN,NaN,r,o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,(e[16]-i)/n,(e[17]-s)/a,NaN,NaN,NaN,NaN,(e[14]-i)/n,(e[15]-s)/a],0),this.newFreeDrawOutline(d,t,this.#Wn,this.#ia,this.#Xn,this.#Kn)}#ga(t,e){const i=this.#qn;return t.set([NaN,NaN,NaN,NaN,i[4],i[5]],e),e+6}#pa(t,e){const i=this.#Qn.subarray(4,6),s=this.#Qn.subarray(16,18),[n,a,r,o]=this.#Wn,[l,h,d,c]=this.#la();return t.set([NaN,NaN,NaN,NaN,(i[0]-n)/r,(i[1]-a)/o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,(s[0]-n)/r,(s[1]-a)/o],e),e+24}}class Us extends Hs{#Wn;#ma=new Float32Array(4);#Xn;#Kn;#na;#ia;#fa;constructor(t,e,i,s,n,a){super(),this.#fa=t,this.#na=e,this.#Wn=i,this.#ia=s,this.#Xn=n,this.#Kn=a,this.lastPoint=[NaN,NaN],this.#ba(a);const[r,o,l,h]=this.#ma;for(let e=0,i=t.length;e<i;e+=2)t[e]=(t[e]-r)/l,t[e+1]=(t[e+1]-o)/h;for(let t=0,i=e.length;t<i;t+=2)e[t]=(e[t]-r)/l,e[t+1]=(e[t+1]-o)/h}toSVGPath(){const t=[`M${this.#fa[4]} ${this.#fa[5]}`];for(let e=6,i=this.#fa.length;e<i;e+=6)isNaN(this.#fa[e])?t.push(`L${this.#fa[e+4]} ${this.#fa[e+5]}`):t.push(`C${this.#fa[e]} ${this.#fa[e+1]} ${this.#fa[e+2]} ${this.#fa[e+3]} ${this.#fa[e+4]} ${this.#fa[e+5]}`);return t.push("Z"),t.join(" ")}serialize([t,e,i,s],n){const a=i-t,r=s-e;let o,l;switch(n){case 0:o=Hs._rescale(this.#fa,t,s,a,-r),l=Hs._rescale(this.#na,t,s,a,-r);break;case 90:o=Hs._rescaleAndSwap(this.#fa,t,e,a,r),l=Hs._rescaleAndSwap(this.#na,t,e,a,r);break;case 180:o=Hs._rescale(this.#fa,i,e,-a,r),l=Hs._rescale(this.#na,i,e,-a,r);break;case 270:o=Hs._rescaleAndSwap(this.#fa,i,s,-a,-r),l=Hs._rescaleAndSwap(this.#na,i,s,-a,-r)}return{outline:Array.from(o),points:[Array.from(l)]}}#ba(t){const e=this.#fa;let i=e[4],s=e[5];const n=[i,s,i,s];let a=i,r=s;const o=t?Math.max:Math.min;for(let t=6,l=e.length;t<l;t+=6){const l=e[t+4],h=e[t+5];if(isNaN(e[t]))at.pointBoundingBox(l,h,n),r<h?(a=l,r=h):r===h&&(a=o(a,l));else{const l=[1/0,1/0,-1/0,-1/0];at.bezierBoundingBox(i,s,...e.slice(t,t+6),l),at.rectBoundingBox(...l,n),r<l[3]?(a=l[2],r=l[3]):r===l[3]&&(a=o(a,l[2]))}i=l,s=h}const l=this.#ma;l[0]=n[0]-this.#Xn,l[1]=n[1]-this.#Xn,l[2]=n[2]-n[0]+2*this.#Xn,l[3]=n[3]-n[1]+2*this.#Xn,this.lastPoint=[a,r]}get box(){return this.#ma}newOutliner(t,e,i,s,n,a=0){return new zs(t,e,i,s,n,a)}getNewOutline(t,e){const[i,s,n,a]=this.#ma,[r,o,l,h]=this.#Wn,d=n*l,c=a*h,u=i*l+r,p=s*h+o,g=this.newOutliner({x:this.#na[0]*d+u,y:this.#na[1]*c+p},this.#Wn,this.#ia,t,this.#Kn,e??this.#Xn);for(let t=2;t<this.#na.length;t+=2)g.add({x:this.#na[t]*d+u,y:this.#na[t+1]*c+p});return g.getOutlines()}}class $s{#Wn;#va;#Aa=[];#wa=[];constructor(t,e=0,i=0,s=!0){const n=[1/0,1/0,-1/0,-1/0],a=10**-4;for(const{x:i,y:s,width:r,height:o}of t){const t=Math.floor((i-e)/a)*a,l=Math.ceil((i+r+e)/a)*a,h=Math.floor((s-e)/a)*a,d=Math.ceil((s+o+e)/a)*a,c=[t,h,d,!0],u=[l,h,d,!1];this.#Aa.push(c,u),at.rectBoundingBox(t,h,l,d,n)}const r=n[2]-n[0]+2*i,o=n[3]-n[1]+2*i,l=n[0]-i,h=n[1]-i,d=this.#Aa.at(s?-1:-2),c=[d[0],d[2]];for(const t of this.#Aa){const[e,i,s]=t;t[0]=(e-l)/r,t[1]=(i-h)/o,t[2]=(s-h)/o}this.#Wn=new Float32Array([l,h,r,o]),this.#va=c}getOutlines(){this.#Aa.sort(((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]));const t=[];for(const e of this.#Aa)e[3]?(t.push(...this.#ya(e)),this.#_a(e)):(this.#xa(e),t.push(...this.#ya(e)));return this.#Sa(t)}#Sa(t){const e=[],i=new Set;for(const i of t){const[t,s,n]=i;e.push([t,s,i],[t,n,i])}e.sort(((t,e)=>t[1]-e[1]||t[0]-e[0]));for(let t=0,s=e.length;t<s;t+=2){const s=e[t][2],n=e[t+1][2];s.push(n),n.push(s),i.add(s),i.add(n)}const s=[];let n;for(;i.size>0;){const t=i.values().next().value;let[e,a,r,o,l]=t;i.delete(t);let h=e,d=a;for(n=[e,r],s.push(n);;){let t;if(i.has(o))t=o;else{if(!i.has(l))break;t=l}i.delete(t),[e,a,r,o,l]=t,h!==e&&(n.push(h,d,e,d===a?a:r),h=e),d=d===a?r:a}n.push(h,d)}return new Gs(s,this.#Wn,this.#va)}#Ea(t){const e=this.#wa;let i=0,s=e.length-1;for(;i<=s;){const n=i+s>>1,a=e[n][0];if(a===t)return n;a<t?i=n+1:s=n-1}return s+1}#_a([,t,e]){const i=this.#Ea(t);this.#wa.splice(i,0,[t,e])}#xa([,t,e]){const i=this.#Ea(t);for(let s=i;s<this.#wa.length;s++){const[i,n]=this.#wa[s];if(i!==t)break;if(i===t&&n===e)return void this.#wa.splice(s,1)}for(let s=i-1;s>=0;s--){const[i,n]=this.#wa[s];if(i!==t)break;if(i===t&&n===e)return void this.#wa.splice(s,1)}}#ya(t){const[e,i,s]=t,n=[[e,i,s]],a=this.#Ea(s);for(let t=0;t<a;t++){const[i,s]=this.#wa[t];for(let t=0,a=n.length;t<a;t++){const[,r,o]=n[t];if(!(s<=r||o<=i))if(r>=i)if(o>s)n[t][1]=s;else{if(1===a)return[];n.splice(t,1),t--,a--}else n[t][2]=i,o>s&&n.push([e,s,o])}}return n}}class Gs extends Hs{#Wn;#Ca;constructor(t,e,i){super(),this.#Ca=t,this.#Wn=e,this.lastPoint=i}toSVGPath(){const t=[];for(const e of this.#Ca){let[i,s]=e;t.push(`M${i} ${s}`);for(let n=2;n<e.length;n+=2){const a=e[n],r=e[n+1];a===i?(t.push(`V${r}`),s=r):r===s&&(t.push(`H${a}`),i=a)}t.push("Z")}return t.join(" ")}serialize([t,e,i,s],n){const a=[],r=i-t,o=s-e;for(const e of this.#Ca){const i=new Array(e.length);for(let n=0;n<e.length;n+=2)i[n]=t+e[n]*r,i[n+1]=s-e[n+1]*o;a.push(i)}return a}get box(){return this.#Wn}get classNamesForOutlining(){return["highlightOutline"]}}class js extends zs{newFreeDrawOutline(t,e,i,s,n,a){return new Vs(t,e,i,s,n,a)}}class Vs extends Us{newOutliner(t,e,i,s,n,a=0){return new js(t,e,i,s,n,a)}}class Ws{#Ta=null;#Ma=null;#Pa;#Ia=null;#ka=!1;#Da=!1;#a=null;#Ra;#La=null;#f=null;#Fa;static#Na=null;static get _keyboardManager(){return q(this,"_keyboardManager",new zt([[["Escape","mac+Escape"],Ws.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],Ws.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],Ws.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],Ws.prototype._moveToPrevious],[["Home","mac+Home"],Ws.prototype._moveToBeginning],[["End","mac+End"],Ws.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){t?(this.#Da=!1,this.#Fa=f.HIGHLIGHT_COLOR,this.#a=t):(this.#Da=!0,this.#Fa=f.HIGHLIGHT_DEFAULT_COLOR),this.#f=t?._uiManager||e,this.#Ra=this.#f._eventBus,this.#Pa=t?.color||this.#f?.highlightColors.values().next().value||"#FFFF98",Ws.#Na||=Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"})}renderButton(){const t=this.#Ta=document.createElement("button");t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.setAttribute("aria-haspopup",!0);const e=this.#f._signal;t.addEventListener("click",this.#Oa.bind(this),{signal:e}),t.addEventListener("keydown",this.#Ks.bind(this),{signal:e});const i=this.#Ma=document.createElement("span");return i.className="swatch",i.setAttribute("aria-hidden",!0),i.style.backgroundColor=this.#Pa,t.append(i),t}renderMainDropdown(){const t=this.#Ia=this.#Ba();return t.setAttribute("aria-orientation","horizontal"),t.setAttribute("aria-labelledby","highlightColorPickerLabel"),t}#Ba(){const t=document.createElement("div"),e=this.#f._signal;t.addEventListener("contextmenu",St,{signal:e}),t.className="dropdown",t.role="listbox",t.setAttribute("aria-multiselectable",!1),t.setAttribute("aria-orientation","vertical"),t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[i,s]of this.#f.highlightColors){const n=document.createElement("button");n.tabIndex="0",n.role="option",n.setAttribute("data-color",s),n.title=i,n.setAttribute("data-l10n-id",Ws.#Na[i]);const a=document.createElement("span");n.append(a),a.className="swatch",a.style.backgroundColor=s,n.setAttribute("aria-selected",s===this.#Pa),n.addEventListener("click",this.#Ha.bind(this,s),{signal:e}),t.append(n)}return t.addEventListener("keydown",this.#Ks.bind(this),{signal:e}),t}#Ha(t,e){e.stopPropagation(),this.#Ra.dispatch("switchannotationeditorparams",{source:this,type:this.#Fa,value:t})}_colorSelectFromKeyboard(t){if(t.target===this.#Ta)return void this.#Oa(t);const e=t.target.getAttribute("data-color");e&&this.#Ha(e,t)}_moveToNext(t){this.#za?t.target!==this.#Ta?t.target.nextSibling?.focus():this.#Ia.firstChild?.focus():this.#Oa(t)}_moveToPrevious(t){t.target!==this.#Ia?.firstChild&&t.target!==this.#Ta?(this.#za||this.#Oa(t),t.target.previousSibling?.focus()):this.#za&&this._hideDropdownFromKeyboard()}_moveToBeginning(t){this.#za?this.#Ia.firstChild?.focus():this.#Oa(t)}_moveToEnd(t){this.#za?this.#Ia.lastChild?.focus():this.#Oa(t)}#Ks(t){Ws._keyboardManager.exec(this,t)}#Oa(t){if(this.#za)return void this.hideDropdown();if(this.#ka=0===t.detail,this.#La||(this.#La=new AbortController,window.addEventListener("pointerdown",this.#d.bind(this),{signal:this.#f.combinedSignal(this.#La)})),this.#Ia)return void this.#Ia.classList.remove("hidden");const e=this.#Ia=this.#Ba();this.#Ta.append(e)}#d(t){this.#Ia?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#Ia?.classList.add("hidden"),this.#La?.abort(),this.#La=null}get#za(){return this.#Ia&&!this.#Ia.classList.contains("hidden")}_hideDropdownFromKeyboard(){this.#Da||(this.#za?(this.hideDropdown(),this.#Ta.focus({preventScroll:!0,focusVisible:this.#ka})):this.#a?.unselect())}updateColor(t){if(this.#Ma&&(this.#Ma.style.backgroundColor=t),!this.#Ia)return;const e=this.#f.highlightColors.values();for(const i of this.#Ia.children)i.setAttribute("aria-selected",e.next().value===t)}destroy(){this.#Ta?.remove(),this.#Ta=null,this.#Ma=null,this.#Ia?.remove(),this.#Ia=null}}class qs extends Vt{#Ua=null;#$a=0;#Ga;#ja=null;#n=null;#Va=null;#Wa=null;#qa=0;#Xa=null;#Ka=null;#w=null;#Ya=!1;#va=null;#Qa;#Ja=null;#Za="";#sa;#tr="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _type="highlight";static _editorType=m.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){const t=qs.prototype;return q(this,"_keyboardManager",new zt([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"}),this.color=t.color||qs._defaultColor,this.#sa=t.thickness||qs._defaultThickness,this.#Qa=t.opacity||qs._defaultOpacity,this.#Ga=t.boxes||null,this.#tr=t.methodOfCreation||"",this.#Za=t.text||"",this._isDraggable=!1,this.defaultL10nId="pdfjs-editor-highlight-editor",t.highlightId>-1?(this.#Ya=!0,this.#er(t),this.#ir()):this.#Ga&&(this.#Ua=t.anchorNode,this.#$a=t.anchorOffset,this.#Wa=t.focusNode,this.#qa=t.focusOffset,this.#sr(),this.#ir(),this.rotate(this.rotation))}get telemetryInitialData(){return{action:"added",type:this.#Ya?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:this.#sa,methodOfCreation:this.#tr}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#sr(){const t=new $s(this.#Ga,.001);this.#Ka=t.getOutlines(),[this.x,this.y,this.width,this.height]=this.#Ka.box;const e=new $s(this.#Ga,.0025,.001,"ltr"===this._uiManager.direction);this.#Va=e.getOutlines();const{lastPoint:i}=this.#Va;this.#va=[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height]}#er({highlightOutlines:t,highlightId:e,clipPathId:i}){this.#Ka=t;if(this.#Va=t.getNewOutline(this.#sa/2****,.0025),e>=0)this.#w=e,this.#ja=i,this.parent.drawLayer.finalizeDraw(e,{bbox:t.box,path:{d:t.toSVGPath()}}),this.#Ja=this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:this.#Va.box,path:{d:this.#Va.toSVGPath()}},!0);else if(this.parent){const e=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(this.#w,{bbox:qs.#nr(this.#Ka.box,(e-this.rotation+360)%360),path:{d:t.toSVGPath()}}),this.parent.drawLayer.updateProperties(this.#Ja,{bbox:qs.#nr(this.#Va.box,e),path:{d:this.#Va.toSVGPath()}})}const[s,n,a,r]=t.box;switch(this.rotation){case 0:this.x=s,this.y=n,this.width=a,this.height=r;break;case 90:{const[t,e]=this.parentDimensions;this.x=n,this.y=1-s,this.width=a*e/t,this.height=r*t/e;break}case 180:this.x=1-s,this.y=1-n,this.width=a,this.height=r;break;case 270:{const[t,e]=this.parentDimensions;this.x=1-n,this.y=s,this.width=a*e/t,this.height=r*t/e;break}}const{lastPoint:o}=this.#Va;this.#va=[(o[0]-s)/a,(o[1]-n)/r]}static initialize(t,e){Vt.initialize(t,e),qs._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case f.HIGHLIGHT_DEFAULT_COLOR:qs._defaultColor=e;break;case f.HIGHLIGHT_THICKNESS:qs._defaultThickness=e}}translateInPage(t,e){}get toolbarPosition(){return this.#va}updateParams(t,e){switch(t){case f.HIGHLIGHT_COLOR:this.#Bn(e);break;case f.HIGHLIGHT_THICKNESS:this.#ar(e)}}static get defaultPropertiesToUpdate(){return[[f.HIGHLIGHT_DEFAULT_COLOR,qs._defaultColor],[f.HIGHLIGHT_THICKNESS,qs._defaultThickness]]}get propertiesToUpdate(){return[[f.HIGHLIGHT_COLOR,this.color||qs._defaultColor],[f.HIGHLIGHT_THICKNESS,this.#sa||qs._defaultThickness],[f.HIGHLIGHT_FREE,this.#Ya]]}#Bn(t){const e=(t,e)=>{this.color=t,this.#Qa=e,this.parent?.drawLayer.updateProperties(this.#w,{root:{fill:t,"fill-opacity":e}}),this.#n?.updateColor(t)},i=this.color,s=this.#Qa;this.addCommands({cmd:e.bind(this,t,qs._defaultOpacity),undo:e.bind(this,i,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)}#ar(t){const e=this.#sa,i=t=>{this.#sa=t,this.#rr(t)};this.addCommands({cmd:i.bind(this,t),undo:i.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:f.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}async addEditToolbar(){const t=await super.addEditToolbar();return t?(this._uiManager.highlightColors&&(this.#n=new Ws({editor:this}),t.addColorPicker(this.#n)),t):null}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#or())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#or())}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),t&&this.div.focus()}remove(){this.#lr(),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.#ir(),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?this.#lr():t&&(this.#ir(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),this.show(this._isVisible),e&&this.select()}#rr(t){if(!this.#Ya)return;this.#er({highlightOutlines:this.#Ka.getNewOutline(t/2)}),this.fixAndSetPosition();const[e,i]=this.parentDimensions;this.setDims(this.width*e,this.height*i)}#lr(){null!==this.#w&&this.parent&&(this.parent.drawLayer.remove(this.#w),this.#w=null,this.parent.drawLayer.remove(this.#Ja),this.#Ja=null)}#ir(t=this.parent){null===this.#w&&(({id:this.#w,clipPathId:this.#ja}=t.drawLayer.draw({bbox:this.#Ka.box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":this.#Qa},rootClass:{highlight:!0,free:this.#Ya},path:{d:this.#Ka.toSVGPath()}},!1,!0)),this.#Ja=t.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:this.#Ya},bbox:this.#Va.box,path:{d:this.#Va.toSVGPath()}},this.#Ya),this.#Xa&&(this.#Xa.style.clipPath=this.#ja))}static#nr([t,e,i,s],n){switch(n){case 90:return[1-e-s,t,s,i];case 180:return[1-t-i,1-e-s,i,s];case 270:return[e,1-t-i,s,i]}return[t,e,i,s]}rotate(t){const{drawLayer:e}=this.parent;let i;this.#Ya?(t=(t-this.rotation+360)%360,i=qs.#nr(this.#Ka.box,t)):i=qs.#nr([this.x,this.y,this.width,this.height],t),e.updateProperties(this.#w,{bbox:i,root:{"data-main-rotation":t}}),e.updateProperties(this.#Ja,{bbox:qs.#nr(this.#Va.box,t),root:{"data-main-rotation":t}})}render(){if(this.div)return this.div;const t=super.render();this.#Za&&(t.setAttribute("aria-label",this.#Za),t.setAttribute("role","mark")),this.#Ya?t.classList.add("free"):this.div.addEventListener("keydown",this.#hr.bind(this),{signal:this._uiManager._signal});const e=this.#Xa=document.createElement("div");t.append(e),e.setAttribute("aria-hidden","true"),e.className="internal",e.style.clipPath=this.#ja;const[i,s]=this.parentDimensions;return this.setDims(this.width*i,this.height*s),Nt(this,this.#Xa,["pointerover","pointerleave"]),this.enableEditing(),t}pointerover(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#Ja,{rootClass:{hovered:!0}})}pointerleave(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#Ja,{rootClass:{hovered:!1}})}#hr(t){qs._keyboardManager.exec(this,t)}_moveCaret(t){switch(this.parent.unselect(this),t){case 0:case 2:this.#dr(!0);break;case 1:case 3:this.#dr(!1)}}#dr(t){if(!this.#Ua)return;const e=window.getSelection();t?e.setPosition(this.#Ua,this.#$a):e.setPosition(this.#Wa,this.#qa)}select(){super.select(),this.#Ja&&this.parent?.drawLayer.updateProperties(this.#Ja,{rootClass:{hovered:!1,selected:!0}})}unselect(){super.unselect(),this.#Ja&&(this.parent?.drawLayer.updateProperties(this.#Ja,{rootClass:{selected:!1}}),this.#Ya||this.#dr(!1))}get _mustFixPosition(){return!this.#Ya}show(t=this._isVisible){super.show(t),this.parent&&(this.parent.drawLayer.updateProperties(this.#w,{rootClass:{hidden:!t}}),this.parent.drawLayer.updateProperties(this.#Ja,{rootClass:{hidden:!t}}))}#or(){return this.#Ya?this.rotation:0}#cr(){if(this.#Ya)return null;const[t,e]=this.pageDimensions,[i,s]=this.pageTranslation,n=this.#Ga,a=new Float32Array(8*n.length);let r=0;for(const{x:o,y:l,width:h,height:d}of n){const n=o*t+i,c=(1-l)*e+s;a[r]=a[r+4]=n,a[r+1]=a[r+3]=c,a[r+2]=a[r+6]=n+h*t,a[r+5]=a[r+7]=c-d*e,r+=8}return a}#ur(t){return this.#Ka.serialize(t,this.#or())}static startHighlighting(t,e,{target:i,x:s,y:n}){const{x:a,y:r,width:o,height:l}=i.getBoundingClientRect(),h=new AbortController,d=t.combinedSignal(h),c=e=>{h.abort(),this.#pr(t,e)};window.addEventListener("blur",c,{signal:d}),window.addEventListener("pointerup",c,{signal:d}),window.addEventListener("pointerdown",Et,{capture:!0,passive:!1,signal:d}),window.addEventListener("contextmenu",St,{signal:d}),i.addEventListener("pointermove",this.#gr.bind(this,t),{signal:d}),this._freeHighlight=new js({x:s,y:n},[a,r,o,l],t.scale,this._defaultThickness/2,e,.001),({id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0))}static#gr(t,e){this._freeHighlight.add(e)&&t.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})}static#pr(t,e){this._freeHighlight.isEmpty()?t.drawLayer.remove(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""}static async deserialize(t,e,i){let s=null;if(t instanceof Is){const{data:{quadPoints:e,rect:i,rotation:n,id:a,color:r,opacity:o,popupRef:l},parent:{page:{pageNumber:h}}}=t;s=t={annotationType:m.HIGHLIGHT,color:Array.from(r),opacity:o,quadPoints:e,boxes:null,pageIndex:h-1,rect:i.slice(0),rotation:n,id:a,deleted:!1,popupRef:l}}else if(t instanceof Ps){const{data:{inkLists:e,rect:i,rotation:n,id:a,color:r,borderStyle:{rawWidth:o},popupRef:l},parent:{page:{pageNumber:h}}}=t;s=t={annotationType:m.HIGHLIGHT,color:Array.from(r),thickness:o,inkLists:e,boxes:null,pageIndex:h-1,rect:i.slice(0),rotation:n,id:a,deleted:!1,popupRef:l}}const{color:n,quadPoints:a,inkLists:r,opacity:o}=t,l=await super.deserialize(t,e,i);l.color=at.makeHexColor(...n),l.#Qa=o||1,r&&(l.#sa=t.thickness),l.annotationElementId=t.id||null,l._initialData=s;const[h,d]=l.pageDimensions,[c,u]=l.pageTranslation;if(a){const t=l.#Ga=[];for(let e=0;e<a.length;e+=8)t.push({x:(a[e]-c)/h,y:1-(a[e+1]-u)/d,width:(a[e+2]-a[e])/h,height:(a[e+1]-a[e+5])/d});l.#sr(),l.#ir(),l.rotate(l.rotation)}else if(r){l.#Ya=!0;const t=r[0],i={x:t[0]-c,y:d-(t[1]-u)},s=new js(i,[0,0,h,d],1,l.#sa/2,!0,.001);for(let e=0,n=t.length;e<n;e+=2)i.x=t[e]-c,i.y=d-(t[e+1]-u),s.add(i);const{id:n,clipPathId:a}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:l.color,"fill-opacity":l._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:s.toSVGPath()}},!0,!0);l.#er({highlightOutlines:s.getOutlines(),highlightId:n,clipPathId:a}),l.#ir(),l.rotate(l.parentRotation)}return l}serialize(t=!1){if(this.isEmpty()||t)return null;if(this.deleted)return this.serializeDeleted();const e=this.getRect(0,0),i=Vt._colorManager.convert(this.color),s={annotationType:m.HIGHLIGHT,color:i,opacity:this.#Qa,thickness:this.#sa,quadPoints:this.#cr(),outlines:this.#ur(e),pageIndex:this.pageIndex,rect:e,rotation:this.#or(),structTreeParentId:this._structTreeParentId};return this.annotationElementId&&!this.#Vn(s)?null:(s.id=this.annotationElementId,s)}#Vn(t){const{color:e}=this._initialData;return t.color.some(((t,i)=>t!==e[i]))}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}}class Xs{#mr=Object.create(null);updateProperty(t,e){this[t]=e,this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,i]of Object.entries(t))e.startsWith("_")||this.updateProperty(e,i)}updateSVGProperty(t,e){this.#mr[t]=e}toSVGProperties(){const t=this.#mr;return this.#mr=Object.create(null),{root:t}}reset(){this.#mr=Object.create(null)}updateAll(t=this){this.updateProperties(t)}clone(){G("Not implemented")}}class Ks extends Vt{#fr=null;#br;_drawId=null;static _currentDrawId=-1;static _currentParent=null;static#vr=null;static#Ar=null;static#wr=null;static#yr=NaN;static#_r=null;static#xr=null;static#Sr=NaN;static _INNER_MARGIN=3;constructor(t){super(t),this.#br=t.mustBeCommitted||!1,this._addOutlines(t)}_addOutlines(t){t.drawOutlines&&(this.#Er(t),this.#ir())}#Er({drawOutlines:t,drawId:e,drawingOptions:i}){this.#fr=t,this._drawingOptions||=i,e>=0?(this._drawId=e,this.parent.drawLayer.finalizeDraw(e,t.defaultProperties)):this._drawId=this.#Cr(t,this.parent),this.#Tr(t.box)}#Cr(t,e){const{id:i}=e.drawLayer.draw(Ks._mergeSVGProperties(this._drawingOptions.toSVGProperties(),t.defaultSVGProperties),!1,!1);return i}static _mergeSVGProperties(t,e){const i=new Set(Object.keys(t));for(const[s,n]of Object.entries(e))i.has(s)?Object.assign(t[s],n):t[s]=n;return t}static getDefaultDrawingOptions(t){G("Not implemented")}static get typesMap(){G("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(t,e){const i=this.typesMap.get(t);i&&this._defaultDrawingOptions.updateProperty(i,e),this._currentParent&&(Ks.#vr.updateProperty(i,e),this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}updateParams(t,e){const i=this.constructor.typesMap.get(t);i&&this._updateProperty(t,i,e)}static get defaultPropertiesToUpdate(){const t=[],e=this._defaultDrawingOptions;for(const[i,s]of this.typesMap)t.push([i,e[s]]);return t}get propertiesToUpdate(){const t=[],{_drawingOptions:e}=this;for(const[i,s]of this.constructor.typesMap)t.push([i,e[s]]);return t}_updateProperty(t,e,i){const s=this._drawingOptions,n=s[e],a=t=>{s.updateProperty(e,t);const i=this.#fr.updateProperty(e,t);i&&this.#Tr(i),this.parent?.drawLayer.updateProperties(this._drawId,s.toSVGProperties())};this.addCommands({cmd:a.bind(this,i),undo:a.bind(this,n),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:t,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){this.parent?.drawLayer.updateProperties(this._drawId,Ks._mergeSVGProperties(this.#fr.getPathResizingSVGProperties(this.#Mr()),{bbox:this.#Pr()}))}_onResized(){this.parent?.drawLayer.updateProperties(this._drawId,Ks._mergeSVGProperties(this.#fr.getPathResizedSVGProperties(this.#Mr()),{bbox:this.#Pr()}))}_onTranslating(t,e){this.parent?.drawLayer.updateProperties(this._drawId,{bbox:this.#Pr()})}_onTranslated(){this.parent?.drawLayer.updateProperties(this._drawId,Ks._mergeSVGProperties(this.#fr.getPathTranslatedSVGProperties(this.#Mr(),this.parentDimensions),{bbox:this.#Pr()}))}_onStartDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit(),this.disableEditMode(),this.disableEditing()}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),this._isDraggable=!0,this.#br&&(this.#br=!1,this.commit(),this.parent.setSelected(this),t&&this.isOnScreen&&this.div.focus())}remove(){this.#lr(),super.remove()}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.#ir(),this.#Tr(this.#fr.box),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?(this._uiManager.removeShouldRescale(this),this.#lr()):t&&(this._uiManager.addShouldRescale(this),this.#ir(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),e&&this.select()}#lr(){null!==this._drawId&&this.parent&&(this.parent.drawLayer.remove(this._drawId),this._drawId=null,this._drawingOptions.reset())}#ir(t=this.parent){null!==this._drawId&&this.parent===t||(null===this._drawId?(this._drawingOptions.updateAll(),this._drawId=this.#Cr(this.#fr,t)):this.parent.drawLayer.updateParent(this._drawId,t.drawLayer))}#Ir([t,e,i,s]){const{parentDimensions:[n,a],rotation:r}=this;switch(r){case 90:return[e,1-t,i*(a/n),s*(n/a)];case 180:return[1-t,1-e,i,s];case 270:return[1-e,t,i*(a/n),s*(n/a)];default:return[t,e,i,s]}}#Mr(){const{x:t,y:e,width:i,height:s,parentDimensions:[n,a],rotation:r}=this;switch(r){case 90:return[1-e,t,i*(n/a),s*(a/n)];case 180:return[1-t,1-e,i,s];case 270:return[e,1-t,i*(n/a),s*(a/n)];default:return[t,e,i,s]}}#Tr(t){if([this.x,this.y,this.width,this.height]=this.#Ir(t),this.div){this.fixAndSetPosition();const[t,e]=this.parentDimensions;this.setDims(this.width*t,this.height*e)}this._onResized()}#Pr(){const{x:t,y:e,width:i,height:s,rotation:n,parentRotation:a,parentDimensions:[r,o]}=this;switch((4*n+a)/90){case 1:return[1-e-s,t,s,i];case 2:return[1-t-i,1-e-s,i,s];case 3:return[e,1-t-i,s,i];case 4:return[t,e-i*(r/o),s*(o/r),i*(r/o)];case 5:return[1-e,t,i*(r/o),s*(o/r)];case 6:return[1-t-s*(o/r),1-e,s*(o/r),i*(r/o)];case 7:return[e-i*(r/o),1-t-s*(o/r),i*(r/o),s*(o/r)];case 8:return[t-i,e-s,i,s];case 9:return[1-e,t-i,s,i];case 10:return[1-t,1-e,i,s];case 11:return[e-s,1-t,s,i];case 12:return[t-s*(o/r),e,s*(o/r),i*(r/o)];case 13:return[1-e-i*(r/o),t-s*(o/r),i*(r/o),s*(o/r)];case 14:return[1-t,1-e-i*(r/o),s*(o/r),i*(r/o)];case 15:return[e,1-t,i*(r/o),s*(o/r)];default:return[t,e,i,s]}}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,Ks._mergeSVGProperties({bbox:this.#Pr()},this.#fr.updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&this.#Tr(this.#fr.updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;let t,e;this._isCopy&&(t=this.x,e=this.y);const i=super.render();i.classList.add("draw");const s=document.createElement("div");i.append(s),s.setAttribute("aria-hidden","true"),s.className="internal";const[n,a]=this.parentDimensions;return this.setDims(this.width*n,this.height*a),this._uiManager.addShouldRescale(this),this.disableEditing(),this._isCopy&&this._moveAfterPaste(t,e),i}static createDrawerInstance(t,e,i,s,n){G("Not implemented")}static startDrawing(t,e,i,s){const{target:n,offsetX:a,offsetY:r,pointerId:o,pointerType:l}=s;if(Ks.#_r&&Ks.#_r!==l)return;const{viewport:{rotation:h}}=t,{width:d,height:c}=n.getBoundingClientRect(),u=Ks.#Ar=new AbortController,p=t.combinedSignal(u);Ks.#yr||=o,Ks.#_r??=l,window.addEventListener("pointerup",(t=>{Ks.#yr===t.pointerId?this._endDraw(t):Ks.#xr?.delete(t.pointerId)}),{signal:p}),window.addEventListener("pointercancel",(t=>{Ks.#yr===t.pointerId?this._currentParent.endDrawingSession():Ks.#xr?.delete(t.pointerId)}),{signal:p}),window.addEventListener("pointerdown",(t=>{Ks.#_r===t.pointerType&&((Ks.#xr||=new Set).add(t.pointerId),Ks.#vr.isCancellable()&&(Ks.#vr.removeLastElement(),Ks.#vr.isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)))}),{capture:!0,passive:!1,signal:p}),window.addEventListener("contextmenu",St,{signal:p}),n.addEventListener("pointermove",this._drawMove.bind(this),{signal:p}),n.addEventListener("touchmove",(t=>{t.timeStamp===Ks.#Sr&&Et(t)}),{signal:p}),t.toggleDrawing(),e._editorUndoBar?.hide(),Ks.#vr?t.drawLayer.updateProperties(this._currentDrawId,Ks.#vr.startNew(a,r,d,c,h)):(e.updateUIForDefaultProperties(this),Ks.#vr=this.createDrawerInstance(a,r,d,c,h),Ks.#wr=this.getDefaultDrawingOptions(),this._currentParent=t,({id:this._currentDrawId}=t.drawLayer.draw(this._mergeSVGProperties(Ks.#wr.toSVGProperties(),Ks.#vr.defaultSVGProperties),!0,!1)))}static _drawMove(t){if(Ks.#Sr=-1,!Ks.#vr)return;const{offsetX:e,offsetY:i,pointerId:s}=t;Ks.#yr===s&&(Ks.#xr?.size>=1?this._endDraw(t):(this._currentParent.drawLayer.updateProperties(this._currentDrawId,Ks.#vr.add(e,i)),Ks.#Sr=t.timeStamp,Et(t)))}static _cleanup(t){t&&(this._currentDrawId=-1,this._currentParent=null,Ks.#vr=null,Ks.#wr=null,Ks.#_r=null,Ks.#Sr=NaN),Ks.#Ar&&(Ks.#Ar.abort(),Ks.#Ar=null,Ks.#yr=NaN,Ks.#xr=null)}static _endDraw(t){const e=this._currentParent;if(e)if(e.toggleDrawing(!0),this._cleanup(!1),t?.target===e.div&&e.drawLayer.updateProperties(this._currentDrawId,Ks.#vr.end(t.offsetX,t.offsetY)),this.supportMultipleDrawings){const t=Ks.#vr,i=this._currentDrawId,s=t.getLastElement();e.addCommands({cmd:()=>{e.drawLayer.updateProperties(i,t.setLastElement(s))},undo:()=>{e.drawLayer.updateProperties(i,t.removeLastElement())},mustExec:!1,type:f.DRAW_STEP})}else this.endDrawing(!1)}static endDrawing(t){const e=this._currentParent;if(!e)return null;if(e.toggleDrawing(!0),e.cleanUndoStack(f.DRAW_STEP),!Ks.#vr.isEmpty()){const{pageDimensions:[i,s],scale:n}=e,a=e.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:Ks.#vr.getOutlines(i*n,s*n,n,this._INNER_MARGIN),drawingOptions:Ks.#wr,mustBeCommitted:!t});return this._cleanup(!0),a}return e.drawLayer.remove(this._currentDrawId),this._cleanup(!0),null}createDrawingOptions(t){}static deserializeDraw(t,e,i,s,n,a){G("Not implemented")}static async deserialize(t,e,i){const{rawDims:{pageWidth:s,pageHeight:n,pageX:a,pageY:r}}=e.viewport,o=this.deserializeDraw(a,r,s,n,this._INNER_MARGIN,t),l=await super.deserialize(t,e,i);return l.createDrawingOptions(t),l.#Er({drawOutlines:o}),l.#ir(),l.onScaleChanging(),l.rotate(),l}serializeDraw(t){const[e,i]=this.pageTranslation,[s,n]=this.pageDimensions;return this.#fr.serialize([e,i,s,n],t)}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}}class Ys{#Qn=new Float64Array(6);#An;#kr;#es;#sa;#na;#Dr="";#Rr=0;#Ca=new Qs;#Lr;#Fr;constructor(t,e,i,s,n,a){this.#Lr=i,this.#Fr=s,this.#es=n,this.#sa=a,[t,e]=this.#Nr(t,e);const r=this.#An=[NaN,NaN,NaN,NaN,t,e];this.#na=[t,e],this.#kr=[{line:r,points:this.#na}],this.#Qn.set(r,0)}updateProperty(t,e){"stroke-width"===t&&(this.#sa=e)}#Nr(t,e){return Hs._normalizePoint(t,e,this.#Lr,this.#Fr,this.#es)}isEmpty(){return!this.#kr||0===this.#kr.length}isCancellable(){return this.#na.length<=10}add(t,e){[t,e]=this.#Nr(t,e);const[i,s,n,a]=this.#Qn.subarray(2,6),r=t-n,o=e-a;return Math.hypot(this.#Lr*r,this.#Fr*o)<=2?null:(this.#na.push(t,e),isNaN(i)?(this.#Qn.set([n,a,t,e],2),this.#An.push(NaN,NaN,NaN,NaN,t,e),{path:{d:this.toSVGPath()}}):(isNaN(this.#Qn[0])&&this.#An.splice(6,6),this.#Qn.set([i,s,n,a,t,e],0),this.#An.push(...Hs.createBezierPoints(i,s,n,a,t,e)),{path:{d:this.toSVGPath()}}))}end(t,e){const i=this.add(t,e);return i||(2===this.#na.length?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,i,s,n){this.#Lr=i,this.#Fr=s,this.#es=n,[t,e]=this.#Nr(t,e);const a=this.#An=[NaN,NaN,NaN,NaN,t,e];this.#na=[t,e];const r=this.#kr.at(-1);return r&&(r.line=new Float32Array(r.line),r.points=new Float32Array(r.points)),this.#kr.push({line:a,points:this.#na}),this.#Qn.set(a,0),this.#Rr=0,this.toSVGPath(),null}getLastElement(){return this.#kr.at(-1)}setLastElement(t){return this.#kr?(this.#kr.push(t),this.#An=t.line,this.#na=t.points,this.#Rr=0,{path:{d:this.toSVGPath()}}):this.#Ca.setLastElement(t)}removeLastElement(){if(!this.#kr)return this.#Ca.removeLastElement();this.#kr.pop(),this.#Dr="";for(let t=0,e=this.#kr.length;t<e;t++){const{line:e,points:i}=this.#kr[t];this.#An=e,this.#na=i,this.#Rr=0,this.toSVGPath()}return{path:{d:this.#Dr}}}toSVGPath(){const t=Hs.svgRound(this.#An[4]),e=Hs.svgRound(this.#An[5]);if(2===this.#na.length)return this.#Dr=`${this.#Dr} M ${t} ${e} Z`,this.#Dr;if(this.#na.length<=6){const i=this.#Dr.lastIndexOf("M");this.#Dr=`${this.#Dr.slice(0,i)} M ${t} ${e}`,this.#Rr=6}if(4===this.#na.length){const t=Hs.svgRound(this.#An[10]),e=Hs.svgRound(this.#An[11]);return this.#Dr=`${this.#Dr} L ${t} ${e}`,this.#Rr=12,this.#Dr}const i=[];0===this.#Rr&&(i.push(`M ${t} ${e}`),this.#Rr=6);for(let t=this.#Rr,e=this.#An.length;t<e;t+=6){const[e,s,n,a,r,o]=this.#An.slice(t,t+6).map(Hs.svgRound);i.push(`C${e} ${s} ${n} ${a} ${r} ${o}`)}return this.#Dr+=i.join(" "),this.#Rr=this.#An.length,this.#Dr}getOutlines(t,e,i,s){const n=this.#kr.at(-1);return n.line=new Float32Array(n.line),n.points=new Float32Array(n.points),this.#Ca.build(this.#kr,t,e,i,this.#es,this.#sa,s),this.#Qn=null,this.#An=null,this.#kr=null,this.#Dr=null,this.#Ca}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}class Qs extends Hs{#ma;#Or=0;#Xn;#kr;#Lr;#Fr;#Br;#es;#sa;build(t,e,i,s,n,a,r){this.#Lr=e,this.#Fr=i,this.#Br=s,this.#es=n,this.#sa=a,this.#Xn=r??0,this.#kr=t,this.#Hr()}get thickness(){return this.#sa}setLastElement(t){return this.#kr.push(t),{path:{d:this.toSVGPath()}}}removeLastElement(){return this.#kr.pop(),{path:{d:this.toSVGPath()}}}toSVGPath(){const t=[];for(const{line:e}of this.#kr)if(t.push(`M${Hs.svgRound(e[4])} ${Hs.svgRound(e[5])}`),6!==e.length)if(12===e.length&&isNaN(e[6]))t.push(`L${Hs.svgRound(e[10])} ${Hs.svgRound(e[11])}`);else for(let i=6,s=e.length;i<s;i+=6){const[s,n,a,r,o,l]=e.subarray(i,i+6).map(Hs.svgRound);t.push(`C${s} ${n} ${a} ${r} ${o} ${l}`)}else t.push("Z");return t.join("")}serialize([t,e,i,s],n){const a=[],r=[],[o,l,h,d]=this.#zr();let c,u,p,g,m,f,b,v,A;switch(this.#es){case 0:A=Hs._rescale,c=t,u=e+s,p=i,g=-s,m=t+o*i,f=e+(1-l-d)*s,b=t+(o+h)*i,v=e+(1-l)*s;break;case 90:A=Hs._rescaleAndSwap,c=t,u=e,p=i,g=s,m=t+l*i,f=e+o*s,b=t+(l+d)*i,v=e+(o+h)*s;break;case 180:A=Hs._rescale,c=t+i,u=e,p=-i,g=s,m=t+(1-o-h)*i,f=e+l*s,b=t+(1-o)*i,v=e+(l+d)*s;break;case 270:A=Hs._rescaleAndSwap,c=t+i,u=e+s,p=-i,g=-s,m=t+(1-l-d)*i,f=e+(1-o-h)*s,b=t+(1-l)*i,v=e+(1-o)*s}for(const{line:t,points:e}of this.#kr)a.push(A(t,c,u,p,g,n?new Array(t.length):null)),r.push(A(e,c,u,p,g,n?new Array(e.length):null));return{lines:a,points:r,rect:[m,f,b,v]}}static deserialize(t,e,i,s,n,{paths:{lines:a,points:r},rotation:o,thickness:l}){const h=[];let d,c,u,p,g;switch(o){case 0:g=Hs._rescale,d=-t/i,c=e/s+1,u=1/i,p=-1/s;break;case 90:g=Hs._rescaleAndSwap,d=-e/s,c=-t/i,u=1/s,p=1/i;break;case 180:g=Hs._rescale,d=t/i+1,c=-e/s,u=-1/i,p=1/s;break;case 270:g=Hs._rescaleAndSwap,d=e/s+1,c=t/i+1,u=-1/s,p=-1/i}if(!a){a=[];for(const t of r){const e=t.length;if(2===e){a.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1]]));continue}if(4===e){a.push(new Float32Array([NaN,NaN,NaN,NaN,t[0],t[1],NaN,NaN,NaN,NaN,t[2],t[3]]));continue}const i=new Float32Array(3*(e-2));a.push(i);let[s,n,r,o]=t.subarray(0,4);i.set([NaN,NaN,NaN,NaN,s,n],0);for(let a=4;a<e;a+=2){const e=t[a],l=t[a+1];i.set(Hs.createBezierPoints(s,n,r,o,e,l),3*(a-2)),[s,n,r,o]=[r,o,e,l]}}}for(let t=0,e=a.length;t<e;t++)h.push({line:g(a[t].map((t=>t??NaN)),d,c,u,p),points:g(r[t].map((t=>t??NaN)),d,c,u,p)});const m=new this.prototype.constructor;return m.build(h,i,s,1,o,l,n),m}#Ur(t=this.#sa){const e=this.#Xn+t/2*this.#Br;return this.#es%180==0?[e/this.#Lr,e/this.#Fr]:[e/this.#Fr,e/this.#Lr]}#zr(){const[t,e,i,s]=this.#ma,[n,a]=this.#Ur(0);return[t+n,e+a,i-2*n,s-2*a]}#Hr(){const t=this.#ma=new Float32Array([1/0,1/0,-1/0,-1/0]);for(const{line:e}of this.#kr){if(e.length<=12){for(let i=4,s=e.length;i<s;i+=6)at.pointBoundingBox(e[i],e[i+1],t);continue}let i=e[4],s=e[5];for(let n=6,a=e.length;n<a;n+=6){const[a,r,o,l,h,d]=e.subarray(n,n+6);at.bezierBoundingBox(i,s,a,r,o,l,h,d,t),i=h,s=d}}const[e,i]=this.#Ur();t[0]=ct(t[0]-e,0,1),t[1]=ct(t[1]-i,0,1),t[2]=ct(t[2]+e,0,1),t[3]=ct(t[3]+i,0,1),t[2]-=t[0],t[3]-=t[1]}get box(){return this.#ma}updateProperty(t,e){return"stroke-width"===t?this.#ar(e):null}#ar(t){const[e,i]=this.#Ur();this.#sa=t;const[s,n]=this.#Ur(),[a,r]=[s-e,n-i],o=this.#ma;return o[0]-=a,o[1]-=r,o[2]+=2*a,o[3]+=2*r,o}updateParentDimensions([t,e],i){const[s,n]=this.#Ur();this.#Lr=t,this.#Fr=e,this.#Br=i;const[a,r]=this.#Ur(),o=a-s,l=r-n,h=this.#ma;return h[0]-=o,h[1]-=l,h[2]+=2*o,h[3]+=2*l,h}updateRotation(t){return this.#Or=t,{path:{transform:this.rotationTransform}}}get viewBox(){return this.#ma.map(Hs.svgRound).join(" ")}get defaultProperties(){const[t,e]=this.#ma;return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Hs.svgRound(t)} ${Hs.svgRound(e)}`}}}get rotationTransform(){const[,,t,e]=this.#ma;let i=0,s=0,n=0,a=0,r=0,o=0;switch(this.#Or){case 90:s=e/t,n=-t/e,r=t;break;case 180:i=-1,a=-1,r=t,o=e;break;case 270:s=-e/t,n=t/e,o=e;break;default:return""}return`matrix(${i} ${s} ${n} ${a} ${Hs.svgRound(r)} ${Hs.svgRound(o)})`}getPathResizingSVGProperties([t,e,i,s]){const[n,a]=this.#Ur(),[r,o,l,h]=this.#ma;if(Math.abs(l-n)<=Hs.PRECISION||Math.abs(h-a)<=Hs.PRECISION){const n=t+i/2-(r+l/2),a=e+s/2-(o+h/2);return{path:{"transform-origin":`${Hs.svgRound(t)} ${Hs.svgRound(e)}`,transform:`${this.rotationTransform} translate(${n} ${a})`}}}const d=(i-2*n)/(l-2*n),c=(s-2*a)/(h-2*a),u=l/i,p=h/s;return{path:{"transform-origin":`${Hs.svgRound(r)} ${Hs.svgRound(o)}`,transform:`${this.rotationTransform} scale(${u} ${p}) translate(${Hs.svgRound(n)} ${Hs.svgRound(a)}) scale(${d} ${c}) translate(${Hs.svgRound(-n)} ${Hs.svgRound(-a)})`}}}getPathResizedSVGProperties([t,e,i,s]){const[n,a]=this.#Ur(),r=this.#ma,[o,l,h,d]=r;if(r[0]=t,r[1]=e,r[2]=i,r[3]=s,Math.abs(h-n)<=Hs.PRECISION||Math.abs(d-a)<=Hs.PRECISION){const n=t+i/2-(o+h/2),a=e+s/2-(l+d/2);for(const{line:t,points:e}of this.#kr)Hs._translate(t,n,a,t),Hs._translate(e,n,a,e);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Hs.svgRound(t)} ${Hs.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const c=(i-2*n)/(h-2*n),u=(s-2*a)/(d-2*a),p=-c*(o+n)+t+n,g=-u*(l+a)+e+a;if(1!==c||1!==u||0!==p||0!==g)for(const{line:t,points:e}of this.#kr)Hs._rescale(t,p,g,c,u,t),Hs._rescale(e,p,g,c,u,e);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${Hs.svgRound(t)} ${Hs.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([t,e],i){const[s,n]=i,a=this.#ma,r=t-a[0],o=e-a[1];if(this.#Lr===s&&this.#Fr===n)for(const{line:t,points:e}of this.#kr)Hs._translate(t,r,o,t),Hs._translate(e,r,o,e);else{const t=this.#Lr/s,e=this.#Fr/n;this.#Lr=s,this.#Fr=n;for(const{line:i,points:s}of this.#kr)Hs._rescale(i,r,o,t,e,i),Hs._rescale(s,r,o,t,e,s);a[2]*=t,a[3]*=e}return a[0]=t,a[1]=e,{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${Hs.svgRound(t)} ${Hs.svgRound(e)}`}}}get defaultSVGProperties(){const t=this.#ma;return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${Hs.svgRound(t[0])} ${Hs.svgRound(t[1])}`,transform:this.rotationTransform||null},bbox:t}}}class Js extends Xs{constructor(t){super(),this._viewParameters=t,super.updateProperties({fill:"none",stroke:Vt._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){"stroke-width"===t&&(e??=this["stroke-width"],e*=this._viewParameters.realScale),super.updateSVGProperty(t,e)}clone(){const t=new Js(this._viewParameters);return t.updateAll(this),t}}class Zs extends Ks{static _type="ink";static _editorType=m.INK;static _defaultDrawingOptions=null;constructor(t){super({...t,name:"inkEditor"}),this._willKeepAspectRatio=!0,this.defaultL10nId="pdfjs-editor-ink-editor"}static initialize(t,e){Vt.initialize(t,e),this._defaultDrawingOptions=new Js(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!0}static get typesMap(){return q(this,"typesMap",new Map([[f.INK_THICKNESS,"stroke-width"],[f.INK_COLOR,"stroke"],[f.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(t,e,i,s,n){return new Ys(t,e,i,s,n,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(t,e,i,s,n,a){return Qs.deserialize(t,e,i,s,n,a)}static async deserialize(t,e,i){let s=null;if(t instanceof Ps){const{data:{inkLists:e,rect:i,rotation:n,id:a,color:r,opacity:o,borderStyle:{rawWidth:l},popupRef:h},parent:{page:{pageNumber:d}}}=t;s=t={annotationType:m.INK,color:Array.from(r),thickness:l,opacity:o,paths:{points:e},boxes:null,pageIndex:d-1,rect:i.slice(0),rotation:n,id:a,deleted:!1,popupRef:h}}const n=await super.deserialize(t,e,i);return n.annotationElementId=t.id||null,n._initialData=s,n}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:t,_drawingOptions:e,parent:i}=this;e.updateSVGProperty("stroke-width"),i.drawLayer.updateProperties(t,e.toSVGProperties())}static onScaleChangingWhenDrawing(){const t=this._currentParent;t&&(super.onScaleChangingWhenDrawing(),this._defaultDrawingOptions.updateSVGProperty("stroke-width"),t.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}createDrawingOptions({color:t,thickness:e,opacity:i}){this._drawingOptions=Zs.getDefaultDrawingOptions({stroke:at.makeHexColor(...t),"stroke-width":e,"stroke-opacity":i})}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:e,points:i,rect:s}=this.serializeDraw(t),{_drawingOptions:{stroke:n,"stroke-opacity":a,"stroke-width":r}}=this,o={annotationType:m.INK,color:Vt._colorManager.convert(n),opacity:a,thickness:r,paths:{lines:e,points:i},pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(o.isCopy=!0,o):this.annotationElementId&&!this.#Vn(o)?null:(o.id=this.annotationElementId,o)}#Vn(t){const{color:e,thickness:i,opacity:s,pageIndex:n}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||t.color.some(((t,i)=>t!==e[i]))||t.thickness!==i||t.opacity!==s||t.pageIndex!==n}renderAnnotationElement(t){const{points:e,rect:i}=this.serializeDraw(!1);return t.updateEdited({rect:i,thickness:this._drawingOptions["stroke-width"],points:e}),null}}class tn extends Qs{toSVGPath(){let t=super.toSVGPath();return t.endsWith("Z")||(t+="Z"),t}}class en{static#$r={maxDim:512,sigmaSFactor:.02,sigmaR:25,kernelSize:16};static#Gr(t,e,i,s){return s-=e,0===(i-=t)?s>0?0:4:1===i?s+6:2-s}static#jr=new Int32Array([0,1,-1,1,-1,0,-1,-1,0,-1,1,-1,1,0,1,1]);static#Vr(t,e,i,s,n,a,r){const o=this.#Gr(i,s,n,a);for(let n=0;n<8;n++){const a=(-n+o-r+16)%8;if(0!==t[(i+this.#jr[2*a])*e+(s+this.#jr[2*a+1])])return a}return-1}static#Wr(t,e,i,s,n,a,r){const o=this.#Gr(i,s,n,a);for(let n=0;n<8;n++){const a=(n+o+r+16)%8;if(0!==t[(i+this.#jr[2*a])*e+(s+this.#jr[2*a+1])])return a}return-1}static#qr(t,e,i,s){const n=t.length,a=new Int32Array(n);for(let e=0;e<n;e++)a[e]=t[e]<=s?1:0;for(let t=1;t<i-1;t++)a[t*e]=a[t*e+e-1]=0;for(let t=0;t<e;t++)a[t]=a[e*i-1-t]=0;let r,o=1;const l=[];for(let t=1;t<i-1;t++){r=1;for(let i=1;i<e-1;i++){const s=t*e+i,n=a[s];if(0===n)continue;let h=t,d=i;if(1===n&&0===a[s-1])o+=1,d-=1;else{if(!(n>=1&&0===a[s+1])){1!==n&&(r=Math.abs(n));continue}o+=1,d+=1,n>1&&(r=n)}const c=[i,t],u=d===i+1,p={isHole:u,points:c,id:o,parent:0};let g;l.push(p);for(const t of l)if(t.id===r){g=t;break}g?g.isHole?p.parent=u?g.parent:r:p.parent=u?r:g.parent:p.parent=u?r:0;const m=this.#Vr(a,e,t,i,h,d,0);if(-1===m){a[s]=-o,1!==a[s]&&(r=Math.abs(a[s]));continue}let f=this.#jr[2*m],b=this.#jr[2*m+1];const v=t+f,A=i+b;h=v,d=A;let w=t,y=i;for(;;){const n=this.#Wr(a,e,w,y,h,d,1);f=this.#jr[2*n],b=this.#jr[2*n+1];const l=w+f,u=y+b;c.push(u,l);const p=w*e+y;if(0===a[p+1]?a[p]=-o:1===a[p]&&(a[p]=o),l===t&&u===i&&w===v&&y===A){1!==a[s]&&(r=Math.abs(a[s]));break}h=w,d=y,w=l,y=u}}}return l}static#Xr(t,e,i,s){if(i-e<=4){for(let n=e;n<i-2;n+=2)s.push(t[n],t[n+1]);return}const n=t[e],a=t[e+1],r=t[i-4]-n,o=t[i-3]-a,l=Math.hypot(r,o),h=r/l,d=o/l,c=h*a-d*n,u=o/r,p=1/l,g=Math.atan(u),m=Math.cos(g),f=Math.sin(g),b=p*(Math.abs(m)+Math.abs(f)),v=p*(1-b+b**2),A=Math.max(Math.atan(Math.abs(f+m)*v),Math.atan(Math.abs(f-m)*v));let w=0,y=e;for(let s=e+2;s<i-2;s+=2){const e=Math.abs(c-h*t[s+1]+d*t[s]);e>w&&(y=s,w=e)}w>(l*A)**2?(this.#Xr(t,e,y+2,s),this.#Xr(t,y,i,s)):s.push(n,a)}static#Kr(t){const e=[],i=t.length;return this.#Xr(t,0,i,e),e.push(t[i-2],t[i-1]),e.length<=4?null:e}static#Yr(t,e,i,s,n,a){const r=new Float32Array(a**2),o=-2*s**2,l=a>>1;for(let t=0;t<a;t++){const e=(t-l)**2;for(let i=0;i<a;i++)r[t*a+i]=Math.exp((e+(i-l)**2)/o)}const h=new Float32Array(256),d=-2*n**2;for(let t=0;t<256;t++)h[t]=Math.exp(t**2/d);const c=t.length,u=new Uint8Array(c),p=new Uint32Array(256);for(let s=0;s<i;s++)for(let n=0;n<e;n++){const o=s*e+n,d=t[o];let c=0,g=0;for(let o=0;o<a;o++){const u=s+o-l;if(!(u<0||u>=i))for(let i=0;i<a;i++){const s=n+i-l;if(s<0||s>=e)continue;const p=t[u*e+s],m=r[o*a+i]*h[Math.abs(p-d)];c+=p*m,g+=m}}p[u[o]=Math.round(c/g)]++}return[u,p]}static#Qr(t){const e=new Uint32Array(256);for(const i of t)e[i]++;return e}static#Jr(t){const e=t.length,i=new Uint8ClampedArray(e>>2);let s=-1/0,n=1/0;for(let e=0,a=i.length;e<a;e++){if(0===t[3+(e<<2)]){s=i[e]=255;continue}const a=i[e]=t[e<<2];a>s&&(s=a),a<n&&(n=a)}const a=255/(s-n);for(let t=0;t<e;t++)i[t]=(i[t]-n)*a;return i}static#Zr(t){let e,i=-1/0,s=-1/0;const n=t.findIndex((t=>0!==t));let a=n,r=n;for(e=n;e<256;e++){const n=t[e];n>i&&(e-a>s&&(s=e-a,r=e-1),i=n,a=e)}for(e=r-1;e>=0&&!(t[e]>t[e+1]);e--);return e}static#to(t){const e=t,{width:i,height:s}=t,{maxDim:n}=this.#$r;let a=i,r=s;if(i>n||s>n){let o=i,l=s,h=Math.log2(Math.max(i,s)/n);const d=Math.floor(h);h=h===d?d-1:d;for(let i=0;i<h;i++){a=o,r=l,a>n&&(a=Math.ceil(a/2)),r>n&&(r=Math.ceil(r/2));const i=new OffscreenCanvas(a,r);i.getContext("2d").drawImage(t,0,0,o,l,0,0,a,r),o=a,l=r,t!==e&&t.close(),t=i.transferToImageBitmap()}const c=Math.min(n/a,n/r);a=Math.round(a*c),r=Math.round(r*c)}const o=new OffscreenCanvas(a,r).getContext("2d",{willReadFrequently:!0});o.filter="grayscale(1)",o.drawImage(t,0,0,t.width,t.height,0,0,a,r);const l=o.getImageData(0,0,a,r).data;return[this.#Jr(l),a,r]}static extractContoursFromText(t,{fontFamily:e,fontStyle:i,fontWeight:s},n,a,r,o){let l=new OffscreenCanvas(1,1),h=l.getContext("2d",{alpha:!1});const d=h.font=`${i} ${s} 200px ${e}`,{actualBoundingBoxLeft:c,actualBoundingBoxRight:u,actualBoundingBoxAscent:p,actualBoundingBoxDescent:g,fontBoundingBoxAscent:m,fontBoundingBoxDescent:f,width:b}=h.measureText(t),v=1.5,A=Math.ceil(Math.max(Math.abs(c)+Math.abs(u)||0,b)*v),w=Math.ceil(Math.max(Math.abs(p)+Math.abs(g)||200,Math.abs(m)+Math.abs(f)||200)*v);l=new OffscreenCanvas(A,w),h=l.getContext("2d",{alpha:!0,willReadFrequently:!0}),h.font=d,h.filter="grayscale(1)",h.fillStyle="white",h.fillRect(0,0,A,w),h.fillStyle="black",h.fillText(t,.5*A/2,1.5*w/2);const y=this.#Jr(h.getImageData(0,0,A,w).data),_=this.#Qr(y),x=this.#Zr(_),S=this.#qr(y,A,w,x);return this.processDrawnLines({lines:{curves:S,width:A,height:w},pageWidth:n,pageHeight:a,rotation:r,innerMargin:o,mustSmooth:!0,areContours:!0})}static process(t,e,i,s,n){const[a,r,o]=this.#to(t),[l,h]=this.#Yr(a,r,o,Math.hypot(r,o)*this.#$r.sigmaSFactor,this.#$r.sigmaR,this.#$r.kernelSize),d=this.#Zr(h),c=this.#qr(l,r,o,d);return this.processDrawnLines({lines:{curves:c,width:r,height:o},pageWidth:e,pageHeight:i,rotation:s,innerMargin:n,mustSmooth:!0,areContours:!0})}static processDrawnLines({lines:t,pageWidth:e,pageHeight:i,rotation:s,innerMargin:n,mustSmooth:a,areContours:r}){s%180!=0&&([e,i]=[i,e]);const{curves:o,width:l,height:h}=t,d=t.thickness??0,c=[],u=Math.min(e/l,i/h),p=u/e,g=u/i,m=[];for(const{points:t}of o){const e=a?this.#Kr(t):t;if(!e)continue;m.push(e);const i=e.length,s=new Float32Array(i),n=new Float32Array(3*(2===i?2:i-2));if(c.push({line:n,points:s}),2===i){s[0]=e[0]*p,s[1]=e[1]*g,n.set([NaN,NaN,NaN,NaN,s[0],s[1]],0);continue}let[r,o,l,h]=e;r*=p,o*=g,l*=p,h*=g,s.set([r,o,l,h],0),n.set([NaN,NaN,NaN,NaN,r,o],0);for(let t=4;t<i;t+=2){const i=s[t]=e[t]*p,a=s[t+1]=e[t+1]*g;n.set(Hs.createBezierPoints(r,o,l,h,i,a),3*(t-2)),[r,o,l,h]=[l,h,i,a]}}if(0===c.length)return null;const f=r?new tn:new Qs;return f.build(c,e,i,1,s,r?0:d,n),{outline:f,newCurves:m,areContours:r,thickness:d,width:l,height:h}}static async compressSignature({outlines:t,areContours:e,thickness:i,width:s,height:n}){let a,r=1/0,o=-1/0,l=0;for(const e of t){l+=e.length;for(let t=2,i=e.length;t<i;t++){const i=e[t]-e[t-2];r=Math.min(r,i),o=Math.max(o,i)}}a=r>=-128&&o<=127?Int8Array:r>=-32768&&o<=32767?Int16Array:Int32Array;const h=t.length,d=8+3*h,c=new Uint32Array(d);let u=0;c[u++]=d*Uint32Array.BYTES_PER_ELEMENT+(l-2*h)*a.BYTES_PER_ELEMENT,c[u++]=0,c[u++]=s,c[u++]=n,c[u++]=e?0:1,c[u++]=Math.max(0,Math.floor(i??0)),c[u++]=h,c[u++]=a.BYTES_PER_ELEMENT;for(const e of t)c[u++]=e.length-2,c[u++]=e[0],c[u++]=e[1];const p=new CompressionStream("deflate-raw"),g=p.writable.getWriter();await g.ready,g.write(c);const m=a.prototype.constructor;for(const e of t){const t=new m(e.length-2);for(let i=2,s=e.length;i<s;i++)t[i-2]=e[i]-e[i-2];g.write(t)}g.close();const f=await new Response(p.readable).arrayBuffer();return ut(new Uint8Array(f))}static async decompressSignature(t){try{const i=(e=t,Uint8Array.fromBase64?Uint8Array.fromBase64(e):it(atob(e))),{readable:s,writable:n}=new DecompressionStream("deflate-raw"),a=n.getWriter();await a.ready,a.write(i).then((async()=>{await a.ready,await a.close()})).catch((()=>{}));let r=null,o=0;for await(const t of s)r||=new Uint8Array(new Uint32Array(t.buffer,0,4)[0]),r.set(t,o),o+=t.length;const l=new Uint32Array(r.buffer,0,r.length>>2),h=l[1];if(0!==h)throw new Error(`Invalid version: ${h}`);const d=l[2],c=l[3],u=0===l[4],p=l[5],g=l[6],m=l[7],f=[],b=(8+3*g)*Uint32Array.BYTES_PER_ELEMENT;let v;switch(m){case Int8Array.BYTES_PER_ELEMENT:v=new Int8Array(r.buffer,b);break;case Int16Array.BYTES_PER_ELEMENT:v=new Int16Array(r.buffer,b);break;case Int32Array.BYTES_PER_ELEMENT:v=new Int32Array(r.buffer,b)}o=0;for(let t=0;t<g;t++){const e=l[3*t+8],i=new Float32Array(e+2);f.push(i);for(let e=0;e<2;e++)i[e]=l[3*t+8+e+1];for(let t=0;t<e;t++)i[t+2]=i[t]+v[o++]}return{areContours:u,thickness:p,outlines:f,width:d,height:c}}catch(t){return $(`decompressSignature: ${t}`),null}var e}}class sn extends Xs{constructor(){super(),super.updateProperties({fill:Vt._defaultLineColor,"stroke-width":0})}clone(){const t=new sn;return t.updateAll(this),t}}class nn extends Js{constructor(t){super(t),super.updateProperties({stroke:Vt._defaultLineColor,"stroke-width":1})}clone(){const t=new nn(this._viewParameters);return t.updateAll(this),t}}class an extends Ks{#eo=!1;#io=null;#so=null;#no=null;static _type="signature";static _editorType=m.SIGNATURE;static _defaultDrawingOptions=null;constructor(t){super({...t,mustBeCommitted:!0,name:"signatureEditor"}),this._willKeepAspectRatio=!0,this.#so=t.signatureData||null,this.#io=null,this.defaultL10nId="pdfjs-editor-signature-editor1"}static initialize(t,e){Vt.initialize(t,e),this._defaultDrawingOptions=new sn,this._defaultDrawnSignatureOptions=new nn(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!1}static get typesMap(){return q(this,"typesMap",new Map)}static get isDrawer(){return!1}get telemetryFinalData(){return{type:"signature",hasDescription:!!this.#io}}static computeTelemetryFinalData(t){const e=t.get("hasDescription");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}get isResizable(){return!0}onScaleChanging(){null!==this._drawId&&super.onScaleChanging()}render(){if(this.div)return this.div;let t,e;const{_isCopy:i}=this;if(i&&(this._isCopy=!1,t=this.x,e=this.y),super.render(),null===this._drawId)if(this.#so){const{lines:t,mustSmooth:e,areContours:i,description:s,uuid:n,heightInPage:a}=this.#so,{rawDims:{pageWidth:r,pageHeight:o},rotation:l}=this.parent.viewport,h=en.processDrawnLines({lines:t,pageWidth:r,pageHeight:o,rotation:l,innerMargin:an._INNER_MARGIN,mustSmooth:e,areContours:i});this.addSignature(h,a,s,n)}else this.div.setAttribute("data-l10n-args",JSON.stringify({description:""})),this.div.hidden=!0,this._uiManager.getSignature(this);return i&&(this._isCopy=!0,this._moveAfterPaste(t,e)),this.div}setUuid(t){this.#no=t,this.addEditToolbar()}getUuid(){return this.#no}get description(){return this.#io}set description(t){this.#io=t,super.addEditToolbar().then((e=>{e?.updateEditSignatureButton(t)}))}getSignaturePreview(){const{newCurves:t,areContours:e,thickness:i,width:s,height:n}=this.#so,a=Math.max(s,n);return{areContours:e,outline:en.processDrawnLines({lines:{curves:t.map((t=>({points:t}))),thickness:i,width:s,height:n},pageWidth:a,pageHeight:a,rotation:0,innerMargin:0,mustSmooth:!1,areContours:e}).outline}}async addEditToolbar(){const t=await super.addEditToolbar();return t?(this._uiManager.signatureManager&&null!==this.#io&&(await t.addEditSignatureButton(this._uiManager.signatureManager,this.#no,this.#io),t.show()),t):null}addSignature(t,e,i,s){const{x:n,y:a}=this,{outline:r}=this.#so=t;let o;this.#eo=r instanceof tn,this.#io=i,this.div.setAttribute("data-l10n-args",JSON.stringify({description:i})),this.#eo?o=an.getDefaultDrawingOptions():(o=an._defaultDrawnSignatureOptions.clone(),o.updateProperties({"stroke-width":r.thickness})),this._addOutlines({drawOutlines:r,drawingOptions:o});const[l,h]=this.parentDimensions,[,d]=this.pageDimensions;let c=e/d;c=c>=1?.5:c,this.width*=c/this.height,this.width>=1&&(c*=.9/this.width,this.width=.9),this.height=c,this.setDims(l*this.width,h*this.height),this.x=n,this.y=a,this.center(),this._onResized(),this.onScaleChanging(),this.rotate(),this._uiManager.addToAnnotationStorage(this),this.setUuid(s),this._reportTelemetry({action:"pdfjs.signature.inserted",data:{hasBeenSaved:!!s,hasDescription:!!i}}),this.div.hidden=!1}getFromImage(t){const{rawDims:{pageWidth:e,pageHeight:i},rotation:s}=this.parent.viewport;return en.process(t,e,i,s,an._INNER_MARGIN)}getFromText(t,e){const{rawDims:{pageWidth:i,pageHeight:s},rotation:n}=this.parent.viewport;return en.extractContoursFromText(t,e,i,s,n,an._INNER_MARGIN)}getDrawnSignature(t){const{rawDims:{pageWidth:e,pageHeight:i},rotation:s}=this.parent.viewport;return en.processDrawnLines({lines:t,pageWidth:e,pageHeight:i,rotation:s,innerMargin:an._INNER_MARGIN,mustSmooth:!1,areContours:!1})}createDrawingOptions({areContours:t,thickness:e}){t?this._drawingOptions=an.getDefaultDrawingOptions():(this._drawingOptions=an._defaultDrawnSignatureOptions.clone(),this._drawingOptions.updateProperties({"stroke-width":e}))}serialize(t=!1){if(this.isEmpty())return null;const{lines:e,points:i,rect:s}=this.serializeDraw(t),{_drawingOptions:{"stroke-width":n}}=this,a={annotationType:m.SIGNATURE,isSignature:!0,areContours:this.#eo,color:[0,0,0],thickness:this.#eo?0:n,pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(a.paths={lines:e,points:i},a.uuid=this.#no,a.isCopy=!0):a.lines=e,this.#io&&(a.accessibilityData={type:"Figure",alt:this.#io}),a}static deserializeDraw(t,e,i,s,n,a){return a.areContours?tn.deserialize(t,e,i,s,n,a):Qs.deserialize(t,e,i,s,n,a)}static async deserialize(t,e,i){const s=await super.deserialize(t,e,i);return s.#eo=t.areContours,s.#io=t.accessibilityData?.alt||"",s.#no=t.uuid,s}}class rn extends Vt{#ao=null;#ro=null;#oo=null;#lo=null;#ho=null;#do="";#co=null;#uo=!1;#po=null;#go=!1;#mo=!1;static _type="stamp";static _editorType=m.STAMP;constructor(t){super({...t,name:"stampEditor"}),this.#lo=t.bitmapUrl,this.#ho=t.bitmapFile,this.defaultL10nId="pdfjs-editor-stamp-editor"}static initialize(t,e){Vt.initialize(t,e)}static isHandlingMimeForPasting(t){return Rt.includes(t)}static paste(t,e){e.pasteEditor(m.STAMP,{bitmapFile:t.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1),super.altTextFinish()}get telemetryFinalData(){return{type:"stamp",hasAltText:!!this.altTextData?.altText}}static computeTelemetryFinalData(t){const e=t.get("hasAltText");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}#fo(t,e=!1){t?(this.#ao=t.bitmap,e||(this.#ro=t.id,this.#go=t.isSvg),t.file&&(this.#do=t.file.name),this.#bo()):this.remove()}#vo(){if(this.#oo=null,this._uiManager.enableWaiting(!1),this.#co){if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#ao)return this._editToolbar.hide(),void this._uiManager.editAltText(this,!0);if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#ao){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}}async mlGuessAltText(t=null,e=!0){if(this.hasAltTextData())return null;const{mlManager:i}=this._uiManager;if(!i)throw new Error("No ML.");if(!await i.isEnabledFor("altText"))throw new Error("ML isn't enabled for alt text.");const{data:s,width:n,height:a}=t||this.copyCanvas(null,null,!0).imageData,r=await i.guess({name:"altText",request:{data:s,width:n,height:a,channels:s.length/(n*a)}});if(!r)throw new Error("No response from the AI service.");if(r.error)throw new Error("Error from the AI service.");if(r.cancel)return null;if(!r.output)throw new Error("No valid response from the AI service.");const o=r.output;return await this.setGuessedAltText(o),e&&!this.hasAltTextData()&&(this.altTextData={alt:o,decorative:!1}),o}#Ao(){if(this.#ro)return this._uiManager.enableWaiting(!0),void this._uiManager.imageManager.getFromId(this.#ro).then((t=>this.#fo(t,!0))).finally((()=>this.#vo()));if(this.#lo){const t=this.#lo;return this.#lo=null,this._uiManager.enableWaiting(!0),void(this.#oo=this._uiManager.imageManager.getFromUrl(t).then((t=>this.#fo(t))).finally((()=>this.#vo())))}if(this.#ho){const t=this.#ho;return this.#ho=null,this._uiManager.enableWaiting(!0),void(this.#oo=this._uiManager.imageManager.getFromFile(t).then((t=>this.#fo(t))).finally((()=>this.#vo())))}const t=document.createElement("input");t.type="file",t.accept=Rt.join(",");const e=this._uiManager._signal;this.#oo=new Promise((i=>{t.addEventListener("change",(async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}}),this.#fo(e)}else this.remove();i()}),{signal:e}),t.addEventListener("cancel",(()=>{this.remove(),i()}),{signal:e})})).finally((()=>this.#vo())),t.click()}remove(){this.#ro&&(this.#ao=null,this._uiManager.imageManager.deleteId(this.#ro),this.#co?.remove(),this.#co=null,this.#po&&(clearTimeout(this.#po),this.#po=null)),super.remove()}rebuild(){this.parent?(super.rebuild(),null!==this.div&&(this.#ro&&null===this.#co&&this.#Ao(),this.isAttachedToDOM||this.parent.add(this))):this.#ro&&this.#Ao()}onceAdded(t){this._isDraggable=!0,t&&this.div.focus()}isEmpty(){return!(this.#oo||this.#ao||this.#lo||this.#ho||this.#ro||this.#uo)}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;return this._isCopy&&(t=this.x,e=this.y),super.render(),this.div.hidden=!0,this.addAltTextButton(),this.#uo||(this.#ao?this.#bo():this.#Ao()),this._isCopy&&this._moveAfterPaste(t,e),this._uiManager.addShouldRescale(this),this.div}setCanvas(t,e){const{id:i,bitmap:s}=this._uiManager.imageManager.getFromCanvas(t,e);e.remove(),i&&this._uiManager.imageManager.isValidId(i)&&(this.#ro=i,s&&(this.#ao=s),this.#uo=!1,this.#bo())}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;null!==this.#po&&clearTimeout(this.#po);this.#po=setTimeout((()=>{this.#po=null,this.#wo()}),200)}#bo(){const{div:t}=this;let{width:e,height:i}=this.#ao;const[s,n]=this.pageDimensions,a=.75;if(this.width)e=this.width*s,i=this.height*n;else if(e>a*s||i>a*n){const t=Math.min(a*s/e,a*n/i);e*=t,i*=t}const[r,o]=this.parentDimensions;this.setDims(e*r/s,i*o/n),this._uiManager.enableWaiting(!1);const l=this.#co=document.createElement("canvas");l.setAttribute("role","img"),this.addContainer(l),this.width=e/s,this.height=i/n,this._initialOptions?.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&!this.annotationElementId||(t.hidden=!1),this.#wo(),this.#mo||(this.parent.addUndoableEditor(this),this.#mo=!0),this._reportTelemetry({action:"inserted_image"}),this.#do&&this.div.setAttribute("aria-description",this.#do)}copyCanvas(t,e,i=!1){t||(t=224);const{width:s,height:n}=this.#ao,a=new Dt;let r=this.#ao,o=s,l=n,h=null;if(e){if(s>e||n>e){const t=Math.min(e/s,e/n);o=Math.floor(s*t),l=Math.floor(n*t)}h=document.createElement("canvas");const t=h.width=Math.ceil(o*a.sx),i=h.height=Math.ceil(l*a.sy);this.#go||(r=this.#yo(t,i));const d=h.getContext("2d");d.filter=this._uiManager.hcmFilter;let c="white",u="#cfcfd8";"none"!==this._uiManager.hcmFilter?u="black":window.matchMedia?.("(prefers-color-scheme: dark)").matches&&(c="#8f8f9d",u="#42414d");const p=15,g=p*a.sx,m=p*a.sy,f=new OffscreenCanvas(2*g,2*m),b=f.getContext("2d");b.fillStyle=c,b.fillRect(0,0,2*g,2*m),b.fillStyle=u,b.fillRect(0,0,g,m),b.fillRect(g,m,g,m),d.fillStyle=d.createPattern(f,"repeat"),d.fillRect(0,0,t,i),d.drawImage(r,0,0,r.width,r.height,0,0,t,i)}let d=null;if(i){let e,i;if(a.symmetric&&r.width<t&&r.height<t)e=r.width,i=r.height;else if(r=this.#ao,s>t||n>t){const a=Math.min(t/s,t/n);e=Math.floor(s*a),i=Math.floor(n*a),this.#go||(r=this.#yo(e,i))}const o=new OffscreenCanvas(e,i).getContext("2d",{willReadFrequently:!0});o.drawImage(r,0,0,r.width,r.height,0,0,e,i),d={width:e,height:i,data:o.getImageData(0,0,e,i).data}}return{canvas:h,width:o,height:l,imageData:d}}#yo(t,e){const{width:i,height:s}=this.#ao;let n=i,a=s,r=this.#ao;for(;n>2*t||a>2*e;){const i=n,s=a;n>2*t&&(n=n>=16384?Math.floor(n/2)-1:Math.ceil(n/2)),a>2*e&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2));const o=new OffscreenCanvas(n,a);o.getContext("2d").drawImage(r,0,0,i,s,0,0,n,a),r=o.transferToImageBitmap()}return r}#wo(){const[t,e]=this.parentDimensions,{width:i,height:s}=this,n=new Dt,a=Math.ceil(i*t*n.sx),r=Math.ceil(s*e*n.sy),o=this.#co;if(!o||o.width===a&&o.height===r)return;o.width=a,o.height=r;const l=this.#go?this.#ao:this.#yo(a,r),h=o.getContext("2d");h.filter=this._uiManager.hcmFilter,h.drawImage(l,0,0,l.width,l.height,0,0,a,r)}#_o(t){if(t){if(this.#go){const t=this._uiManager.imageManager.getSvgUrl(this.#ro);if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=this.#ao);return t.getContext("2d").drawImage(this.#ao,0,0),t.toDataURL()}if(this.#go){const[t,e]=this.pageDimensions,i=Math.round(this.width*t*gt.PDF_TO_CSS_UNITS),s=Math.round(this.height*e*gt.PDF_TO_CSS_UNITS),n=new OffscreenCanvas(i,s);return n.getContext("2d").drawImage(this.#ao,0,0,this.#ao.width,this.#ao.height,0,0,i,s),n.transferToImageBitmap()}return structuredClone(this.#ao)}static async deserialize(t,e,i){let s=null,n=!1;if(t instanceof Ls){const{data:{rect:a,rotation:r,id:o,structParent:l,popupRef:h},container:d,parent:{page:{pageNumber:c}},canvas:u}=t;let p,g;u?(delete t.canvas,({id:p,bitmap:g}=i.imageManager.getFromCanvas(d.id,u)),u.remove()):(n=!0,t._hasNoCanvas=!0);const f=(await e._structTree.getAriaAttributes(`${dt}${o}`))?.get("aria-label")||"";s=t={annotationType:m.STAMP,bitmapId:p,bitmap:g,pageIndex:c-1,rect:a.slice(0),rotation:r,id:o,deleted:!1,accessibilityData:{decorative:!1,altText:f},isSvg:!1,structParent:l,popupRef:h}}const a=await super.deserialize(t,e,i),{rect:r,bitmap:o,bitmapUrl:l,bitmapId:h,isSvg:d,accessibilityData:c}=t;n?(i.addMissingCanvas(t.id,a),a.#uo=!0):h&&i.imageManager.isValidId(h)?(a.#ro=h,o&&(a.#ao=o)):a.#lo=l,a.#go=d;const[u,p]=a.pageDimensions;return a.width=(r[2]-r[0])/u,a.height=(r[3]-r[1])/p,a.annotationElementId=t.id||null,c&&(a.altTextData=c),a._initialData=s,a.#mo=!!s,a}serialize(t=!1,e=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const i={annotationType:m.STAMP,bitmapId:this.#ro,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#go,structTreeParentId:this._structTreeParentId};if(t)return i.bitmapUrl=this.#_o(!0),i.accessibilityData=this.serializeAltText(!0),i.isCopy=!0,i;const{decorative:s,altText:n}=this.serializeAltText(!1);if(!s&&n&&(i.accessibilityData={type:"Figure",alt:n}),this.annotationElementId){const t=this.#Vn(i);if(t.isSame)return null;t.isSameAltText?delete i.accessibilityData:i.accessibilityData.structParent=this._initialData.structParent??-1}if(i.id=this.annotationElementId,null===e)return i;e.stamps||=new Map;const a=this.#go?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(e.stamps.has(this.#ro)){if(this.#go){const t=e.stamps.get(this.#ro);a>t.area&&(t.area=a,t.serialized.bitmap.close(),t.serialized.bitmap=this.#_o(!1))}}else e.stamps.set(this.#ro,{area:a,serialized:i}),i.bitmap=this.#_o(!1);return i}#Vn(t){const{pageIndex:e,accessibilityData:{altText:i}}=this._initialData,s=t.pageIndex===e,n=(t.accessibilityData?.alt||"")===i;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&s&&n,isSameAltText:n}}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}}class on{#Mn;#xo=!1;#So=null;#Eo=null;#Co=null;#To=new Map;#Mo=!1;#Po=!1;#Io=!1;#ko=null;#Do=null;#Ro=null;#Lo=null;#f;static _initialized=!1;static#$=new Map([Bs,Zs,rn,qs,an].map((t=>[t._editorType,t])));constructor({uiManager:t,pageIndex:e,div:i,structTreeLayer:s,accessibilityManager:n,annotationLayer:a,drawLayer:r,textLayer:o,viewport:l,l10n:h}){const d=[...on.#$.values()];if(!on._initialized){on._initialized=!0;for(const e of d)e.initialize(h,t)}t.registerEditorTypes(d),this.#f=t,this.pageIndex=e,this.div=i,this.#Mn=n,this.#So=a,this.viewport=l,this.#Ro=o,this.drawLayer=r,this._structTree=s,this.#f.addLayer(this)}get isEmpty(){return 0===this.#To.size}get isInvisible(){return this.isEmpty&&this.#f.getMode()===m.NONE}updateToolbar(t){this.#f.updateToolbar(t)}updateMode(t=this.#f.getMode()){switch(this.#Fo(),t){case m.NONE:return this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),void this.disableClick();case m.INK:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick();break;case m.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const i of on.#$.values())e.toggle(`${i._type}Editing`,t===i._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#Ro?.div}setEditingState(t){this.#f.setEditingState(t)}addCommands(t){this.#f.addCommands(t)}cleanUndoStack(t){this.#f.cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#So?.div.classList.toggle("disabled",!t)}async enable(){this.#Io=!0,this.div.tabIndex=0,this.togglePointerEvents(!0);const t=new Set;for(const e of this.#To.values())e.enableEditing(),e.show(!0),e.annotationElementId&&(this.#f.removeChangedExistingAnnotation(e),t.add(e.annotationElementId));if(!this.#So)return void(this.#Io=!1);const e=this.#So.getEditableAnnotations();for(const i of e){if(i.hide(),this.#f.isDeletedAnnotationElement(i.data.id))continue;if(t.has(i.data.id))continue;const e=await this.deserialize(i);e&&(this.addOrRebuild(e),e.enableEditing())}this.#Io=!1}disable(){this.#Po=!0,this.div.tabIndex=-1,this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const i of this.#To.values())i.disableEditing(),i.annotationElementId&&(null===i.serialize()?(e.set(i.annotationElementId,i),this.getEditableAnnotation(i.annotationElementId)?.show(),i.remove()):t.set(i.annotationElementId,i));if(this.#So){const i=this.#So.getEditableAnnotations();for(const s of i){const{id:i}=s.data;if(this.#f.isDeletedAnnotationElement(i))continue;let n=e.get(i);n?(n.resetAnnotationElement(s),n.show(!1),s.show()):(n=t.get(i),n&&(this.#f.addChangedExistingAnnotation(n),n.renderAnnotationElement(s)&&n.show(!1)),s.show())}}this.#Fo(),this.isEmpty&&(this.div.hidden=!0);const{classList:i}=this.div;for(const t of on.#$.values())i.remove(`${t._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),this.#Po=!1}getEditableAnnotation(t){return this.#So?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#f.getActive()!==t&&this.#f.setActiveEditor(t)}enableTextSelection(){if(this.div.tabIndex=-1,this.#Ro?.div&&!this.#Lo){this.#Lo=new AbortController;const t=this.#f.combinedSignal(this.#Lo);this.#Ro.div.addEventListener("pointerdown",this.#No.bind(this),{signal:t}),this.#Ro.div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0,this.#Ro?.div&&this.#Lo&&(this.#Lo.abort(),this.#Lo=null,this.#Ro.div.classList.remove("highlighting"))}#No(t){this.#f.unselectAll();const{target:e}=t;if(e===this.#Ro.div||("img"===e.getAttribute("role")||e.classList.contains("endOfContent"))&&this.#Ro.div.contains(e)){const{isMac:e}=st.platform;if(0!==t.button||t.ctrlKey&&e)return;this.#f.showAllEditors("highlight",!0,!0),this.#Ro.div.classList.add("free"),this.toggleDrawing(),qs.startHighlighting(this,"ltr"===this.#f.direction,{target:this.#Ro.div,x:t.x,y:t.y}),this.#Ro.div.addEventListener("pointerup",(()=>{this.#Ro.div.classList.remove("free"),this.toggleDrawing(!0)}),{once:!0,signal:this.#f._signal}),t.preventDefault()}}enableClick(){if(this.#Eo)return;this.#Eo=new AbortController;const t=this.#f.combinedSignal(this.#Eo);this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t}),this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){this.#Eo?.abort(),this.#Eo=null}attach(t){this.#To.set(t.id,t);const{annotationElementId:e}=t;e&&this.#f.isDeletedAnnotationElement(e)&&this.#f.removeDeletedAnnotationElement(t)}detach(t){this.#To.delete(t.id),this.#Mn?.removePointerInTextLayer(t.contentDiv),!this.#Po&&t.annotationElementId&&this.#f.addDeletedAnnotationElement(t)}remove(t){this.detach(t),this.#f.removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1}changeParent(t){t.parent!==this&&(t.parent&&t.annotationElementId&&(this.#f.addDeletedAnnotationElement(t.annotationElementId),Vt.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),t.parent?.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(t.parent!==this||!t.isAttachedToDOM){if(this.changeParent(t),this.#f.addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(!this.#Io),this.#f.addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&!this.#Co&&(t._focusEventsAllowed=!1,this.#Co=setTimeout((()=>{this.#Co=null,t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this.#f._signal}),e.focus())}),0)),t._structTreeParentId=this.#Mn?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||=this,t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#f.getId()}get#Oo(){return on.#$.get(this.#f.getMode())}combinedSignal(t){return this.#f.combinedSignal(t)}#Bo(t){const e=this.#Oo;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#Oo?.canCreateNewEmptyEditor()}async pasteEditor(t,e){this.#f.updateToolbar(t),await this.#f.updateMode(t);const{offsetX:i,offsetY:s}=this.#Ho(),n=this.getNextId(),a=this.#Bo({parent:this,id:n,x:i,y:s,uiManager:this.#f,isCentered:!0,...e});a&&this.add(a)}async deserialize(t){return await(on.#$.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#f))||null}createAndAddNewEditor(t,e,i={}){const s=this.getNextId(),n=this.#Bo({parent:this,id:s,x:t.offsetX,y:t.offsetY,uiManager:this.#f,isCentered:e,...i});return n&&this.add(n),n}#Ho(){const{x:t,y:e,width:i,height:s}=this.div.getBoundingClientRect(),n=Math.max(0,t),a=Math.max(0,e),r=(n+Math.min(window.innerWidth,t+i))/2-t,o=(a+Math.min(window.innerHeight,e+s))/2-e,[l,h]=this.viewport.rotation%180==0?[r,o]:[o,r];return{offsetX:l,offsetY:h}}addNewEditor(t={}){this.createAndAddNewEditor(this.#Ho(),!0,t)}setSelected(t){this.#f.setSelected(t)}toggleSelected(t){this.#f.toggleSelected(t)}unselect(t){this.#f.unselect(t)}pointerup(t){const{isMac:e}=st.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;if(!this.#Mo)return;if(this.#Mo=!1,this.#Oo?.isDrawer&&this.#Oo.supportMultipleDrawings)return;if(!this.#xo)return void(this.#xo=!0);const i=this.#f.getMode();i!==m.STAMP&&i!==m.SIGNATURE?this.createAndAddNewEditor(t,!1):this.#f.unselectAll()}pointerdown(t){if(this.#f.getMode()===m.HIGHLIGHT&&this.enableTextSelection(),this.#Mo)return void(this.#Mo=!1);const{isMac:e}=st.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;if(this.#Mo=!0,this.#Oo?.isDrawer)return void this.startDrawingSession(t);const i=this.#f.getActive();this.#xo=!i||i.isEmpty()}startDrawingSession(t){if(this.div.focus({preventScroll:!0}),this.#ko)return void this.#Oo.startDrawing(this,this.#f,!1,t);this.#f.setCurrentDrawingSession(this),this.#ko=new AbortController;const e=this.#f.combinedSignal(this.#ko);this.div.addEventListener("blur",(({relatedTarget:t})=>{t&&!this.div.contains(t)&&(this.#Do=null,this.commitOrRemove())}),{signal:e}),this.#Oo.startDrawing(this,this.#f,!1,t)}pause(t){if(t){const{activeElement:t}=document;this.div.contains(t)&&(this.#Do=t)}else this.#Do&&setTimeout((()=>{this.#Do?.focus(),this.#Do=null}),0)}endDrawingSession(t=!1){return this.#ko?(this.#f.setCurrentDrawingSession(null),this.#ko.abort(),this.#ko=null,this.#Do=null,this.#Oo.endDrawing(t)):null}findNewParent(t,e,i){const s=this.#f.findParent(e,i);return null!==s&&s!==this&&(s.changeParent(t),!0)}commitOrRemove(){return!!this.#ko&&(this.endDrawingSession(),!0)}onScaleChanging(){this.#ko&&this.#Oo.onScaleChangingWhenDrawing(this)}destroy(){this.commitOrRemove(),this.#f.getActive()?.parent===this&&(this.#f.commitOrRemove(),this.#f.setActiveEditor(null)),this.#Co&&(clearTimeout(this.#Co),this.#Co=null);for(const t of this.#To.values())this.#Mn?.removePointerInTextLayer(t.contentDiv),t.setParent(null),t.isAttachedToDOM=!1,t.div.remove();this.div=null,this.#To.clear(),this.#f.removeLayer(this)}#Fo(){for(const t of this.#To.values())t.isEmpty()&&t.remove()}render({viewport:t}){this.viewport=t,kt(this.div,t);for(const t of this.#f.getEditors(this.pageIndex))this.add(t),t.rebuild();this.updateMode()}update({viewport:t}){this.#f.commitOrRemove(),this.#Fo();const e=this.viewport.rotation,i=t.rotation;if(this.viewport=t,kt(this.div,{rotation:i}),e!==i)for(const t of this.#To.values())t.rotate(i)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#f.viewParameters.realScale}}class ln{#rn=null;#zo=new Map;#Uo=new Map;static#w=0;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(this.#rn){if(this.#rn!==t){if(this.#zo.size>0)for(const e of this.#zo.values())e.remove(),t.append(e);this.#rn=t}}else this.#rn=t}static get _svgFactory(){return q(this,"_svgFactory",new as)}static#$o(t,[e,i,s,n]){const{style:a}=t;a.top=100*i+"%",a.left=100*e+"%",a.width=100*s+"%",a.height=100*n+"%"}#Go(){const t=ln._svgFactory.create(1,1,!0);return this.#rn.append(t),t.setAttribute("aria-hidden",!0),t}#jo(t,e){const i=ln._svgFactory.createElement("clipPath");t.append(i);const s=`clip_${e}`;i.setAttribute("id",s),i.setAttribute("clipPathUnits","objectBoundingBox");const n=ln._svgFactory.createElement("use");return i.append(n),n.setAttribute("href",`#${e}`),n.classList.add("clip"),s}#Vo(t,e){for(const[i,s]of Object.entries(e))null===s?t.removeAttribute(i):t.setAttribute(i,s)}draw(t,e=!1,i=!1){const s=ln.#w++,n=this.#Go(),a=ln._svgFactory.createElement("defs");n.append(a);const r=ln._svgFactory.createElement("path");a.append(r);const o=`path_p${this.pageIndex}_${s}`;r.setAttribute("id",o),r.setAttribute("vector-effect","non-scaling-stroke"),e&&this.#Uo.set(s,r);const l=i?this.#jo(a,o):null,h=ln._svgFactory.createElement("use");return n.append(h),h.setAttribute("href",`#${o}`),this.updateProperties(n,t),this.#zo.set(s,n),{id:s,clipPathId:`url(#${l})`}}drawOutline(t,e){const i=ln.#w++,s=this.#Go(),n=ln._svgFactory.createElement("defs");s.append(n);const a=ln._svgFactory.createElement("path");n.append(a);const r=`path_p${this.pageIndex}_${i}`;let o;if(a.setAttribute("id",r),a.setAttribute("vector-effect","non-scaling-stroke"),e){const t=ln._svgFactory.createElement("mask");n.append(t),o=`mask_p${this.pageIndex}_${i}`,t.setAttribute("id",o),t.setAttribute("maskUnits","objectBoundingBox");const e=ln._svgFactory.createElement("rect");t.append(e),e.setAttribute("width","1"),e.setAttribute("height","1"),e.setAttribute("fill","white");const s=ln._svgFactory.createElement("use");t.append(s),s.setAttribute("href",`#${r}`),s.setAttribute("stroke","none"),s.setAttribute("fill","black"),s.setAttribute("fill-rule","nonzero"),s.classList.add("mask")}const l=ln._svgFactory.createElement("use");s.append(l),l.setAttribute("href",`#${r}`),o&&l.setAttribute("mask",`url(#${o})`);const h=l.cloneNode();return s.append(h),l.classList.add("mainOutline"),h.classList.add("secondaryOutline"),this.updateProperties(s,t),this.#zo.set(i,s),i}finalizeDraw(t,e){this.#Uo.delete(t),this.updateProperties(t,e)}updateProperties(t,e){if(!e)return;const{root:i,bbox:s,rootClass:n,path:a}=e,r="number"==typeof t?this.#zo.get(t):t;if(r){if(i&&this.#Vo(r,i),s&&ln.#$o(r,s),n){const{classList:t}=r;for(const[e,i]of Object.entries(n))t.toggle(e,i)}if(a){const t=r.firstChild.firstChild;this.#Vo(t,a)}}}updateParent(t,e){if(e===this)return;const i=this.#zo.get(t);i&&(e.#rn.append(i),this.#zo.delete(t),e.#zo.set(t,i))}remove(t){this.#Uo.delete(t),null!==this.#rn&&(this.#zo.get(t).remove(),this.#zo.delete(t))}destroy(){this.#rn=null;for(const t of this.#zo.values())t.remove();this.#zo.clear(),this.#Uo.clear()}}globalThis.pdfjsTestingUtils={HighlightOutliner:$s},globalThis.pdfjsLib={AbortException:tt,AnnotationEditorLayer:on,AnnotationEditorParamsType:f,AnnotationEditorType:m,AnnotationEditorUIManager:$t,AnnotationLayer:Ns,AnnotationMode:g,AnnotationType:E,build:ts,ColorPicker:Ws,createValidAbsoluteUrl:V,DOMSVGFactory:as,DrawLayer:ln,FeatureTest:st,fetchData:mt,getDocument:Bi,getFilenameFromUrl:wt,getPdfFilenameFromUrl:yt,getUuid:ht,getXfaPageViewport:Tt,GlobalWorkerOptions:li,ImageKind:S,InvalidPDFException:Q,isDataScheme:vt,isPdfFile:At,isValidExplicitDest:Ui,MathClamp:ct,noContextMenu:St,normalizeUnicode:lt,OPS:D,OutputScale:Dt,PasswordResponses:O,PDFDataRangeTransport:Gi,PDFDateString:Ct,PDFWorker:qi,PermissionFlag:b,PixelsPerInch:gt,RenderingCancelledException:bt,ResponseException:J,setLayerDimensions:kt,shadow:q,SignatureExtractor:en,stopEvent:Et,SupportedImageMimeTypes:Rt,TextLayer:Fi,TouchManager:jt,updateUrlHash:W,Util:at,VerbosityLevel:k,version:Zi,XfaLayer:rs}}}]);