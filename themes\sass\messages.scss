// out:  ../messages.css
@import '_common.scss';

// Chatbot
.mwai-messages-theme {
	--mwai-spacing: 10px;
	--mwai-fontSize: 13px;
	--mwai-lineHeight: 1.5;
	--mwai-borderRadius: 10px;
	--mwai-width: 460px;
	--mwai-maxHeight: 40vh;
	--mwai-iconTextColor: black;
	--mwai-iconTextBackgroundColor: white;
	--mwai-fontColor: black;
	--mwai-backgroundPrimaryColor: #fafafa;
	--mwai-backgroundHeaderColor: #0084ff;
	--mwai-bubbleColor: #0084ff;
	--mwai-headerButtonsColor: white;
	--mwai-conversationsBackgroundColor: white;
	--mwai-backgroundUserColor: #0084ff;
	--mwai-backgroundAiColor: #eee;
	--mwai-backgroundAiSecondaryColor: #ddd;
	--mwai-errorBackgroundColor: #6d2f2a;
	--mwai-errorTextColor: #FFFFFF;

	* {
		box-sizing: border-box;
	}

	.mwai-body {
		display: flex;
		background: var(--mwai-backgroundPrimaryColor);
		font-size: var(--mwai-fontSize);
		color: var(--mwai-fontColor);
		border-radius: var(--mwai-borderRadius);
		flex-direction: column;
	}

	.mwai-shortcuts {
		display: flex;
		flex-direction: column;
		align-items: flex-end;

		.mwai-shortcut {
			margin-bottom: 5px;
			font-size: var(--mwai-fontSize);
			height: inherit;
			min-height: inherit;
			width: inherit;
			min-width: 90px;
			border-radius: var(--mwai-borderRadius);
			padding: 7px 12px;
			cursor: pointer;
			display: flex;
			align-items: center;
			justify-content: end;

			&.mwai-success {
				background: #4caf50;
				color: white;
				box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.15);
			}

			&.mwai-danger {
				background: #f44336;
				color: white;
				box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.15);
			}

			&.mwai-warning {
				background: #ff9800;
				color: white;
				box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.15);
			}

			&.mwai-info {
				background: #2196f3;
				color: white;
				box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.15);
			}

			.mwai-icon {
				margin-right: 5px;

				img {
					max-height: 16px;
					width: auto;
				}
			}

			&:hover {
				filter: brightness(1.1);
			}
		}
	}

	.mwai-blocks {
		display: flex;
		flex-direction: column;
		padding: var(--mwai-spacing);

		.mwai-block {
			p:first-child {
				margin-top: 0;
			}
		}

		button {
			cursor: pointer;
		}
	}

	.mwai-conversation {
		display: flex;
		flex-direction: column;
		overflow: auto;
		max-height: var(--mwai-maxHeight);
		padding: var(--mwai-spacing) var(--mwai-spacing) 0;

		.mwai-reply {
			margin-bottom: var(--mwai-spacing);
			padding: 7px 12px;
			border-radius: 15px;
			font-size: var(--mwai-fontSize);
			color: var(--mwai-fontColor);
			position: relative;

			.mwai-name,
			.mwai-name-text {
				display: none;
			}

			& * > p {
				&:first-child {
					margin-top: 0;
				}
				&:last-child {
					margin-bottom: 0;
				}
			}

			&.mwai-ai {
				align-self: flex-start;
				background: var(--mwai-backgroundAiColor);
				margin-left: 5px;

				&::before,
				&::after {
					content: "";
					position: absolute;
					z-index: 1;
					bottom: 0;
					left: -10px;
					width: 10px;
					height: 20px;
					background: var(--mwai-backgroundPrimaryColor);
					border-bottom-right-radius: 10px;
				}

				&::before {
					z-index: 0;
					left: -7px;
					height: 20px;
					width: 20px;
					background: var(--mwai-backgroundAiColor);
					border-bottom-right-radius: 15px;
				}
			}

			&.mwai-user {
				align-self: flex-end;
				background: var(--mwai-backgroundUserColor);
				color: white;
				margin-right: 10px;

				&::before,
				&::after {
					content: "";
					position: absolute;
					z-index: 1;
					bottom: 0;
					right: -10px;
					width: 10px;
					height: 20px;
					background: var(--mwai-backgroundPrimaryColor);
					border-bottom-left-radius: 10px;
				}

				&::before {
					z-index: 0;
					right: -10px;
					height: 20px;
					width: 20px;
					background: var(--mwai-backgroundUserColor);
					background-attachment: fixed;
					border-bottom-left-radius: 15px;
				}
			}
		}
	}

	.mwai-text {
		flex: auto;

		.mwai-image {
			display: block;
			max-width: 250px;
			height: auto;
			margin: 0 0 10px 0;
			border-radius: var(--mwai-borderRadius);
		}

		.mwai-filename {
			display: flex;
			text-decoration: none;
			border: 1px solid var(--mwai-backgroundPrimaryColor);
			border-radius: var(--mwai-borderRadius);
			color: white;
			padding: 5px 10px;
			margin-bottom: 10px;
		}

		> span > p > *:first-child {
			margin-top: 0;
		}

		a {
			color: #2196f3;
		}

		h1 {
			font-size: 200%;
		}

		h2 {
			font-size: 160%;
		}

		h3 {
			font-size: 140%;
		}

		h4 {
			font-size: 120%;
		}

		p {
			font-size: var(--mwai-fontSize);
			line-height: var(--mwai-lineHeight);

			code {
				background: var(--mwai-backgroundAiSecondaryColor);
				padding: 2px 6px;
				border-radius: 8px;
				font-size: calc(var(--mwai-fontSize) * 0.9);
				font-family: system-ui;
			}
		}

		pre {
			color: var(--mwai-fontColor);
			border-radius: var(--mwai-borderRadius);
			break-after: auto;
			white-space: pre-wrap;
			max-width: 100%;
			width: 100%;
			font-family: system-ui;
			background: var(--mwai-backgroundAiSecondaryColor);
			padding: var(--mwai-spacing);

			code {
				padding: 0 !important;
				font-family: system-ui;
				background: var(--mwai-backgroundAiSecondaryColor);
			}
		}

		ol {
			padding: 0;
			margin: 0 0 0 20px;
		}

		table {
			width: 100%;
			border: 2px solid var(--mwai-backgroundAiSecondaryColor);
			border-collapse: collapse;
		}

		thead {
			background: var(--mwai-backgroundAiSecondaryColor);
		}

		tr,
		td {
			padding: 2px 5px;
		}

		td {
			border: 2px solid var(--mwai-backgroundAiSecondaryColor);
		}

		.mwai-typewriter {
			display: inline-block;

			> :first-child {
				margin-top: 0;
			}
		}

		> * {
			&:first-child {
				margin-top: 0;
			}
			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	.mwai-input {
		display: flex;
		align-items: center;
		padding: var(--mwai-spacing);

		.mwai-input-text {
			flex: auto;
			position: relative;
			display: flex;
			background: var(--mwai-backgroundPrimaryColor);
			border-radius: var(--mwai-borderRadius);
			border: 1px solid var(--mwai-backgroundAiSecondaryColor);
			overflow: hidden;

			&.mwai-blocked img {
				filter: grayscale(100%);
				opacity: 0.5;
			}

			&.mwai-dragging {
				border: 1px dashed var(--mwai-backgroundAiSecondaryColor);
			}

			textarea {
				background: var(--mwai-backgroundPrimaryColor);
				color: var(--mwai-fontColor);
				flex: auto;
				padding: var(--mwai-spacing);
				border: none;
				font-size: var(--mwai-fontSize);
				resize: none;
				font-family: inherit;
				margin: 0;
				overflow: hidden;
				min-height: inherit;

				&:focus {
					outline: none;
					box-shadow: none;
				}

				&::placeholder {
					color: var(--mwai-fontColor);
					opacity: 0.5;
				}
			}

			.mwai-microphone {
				@include microphone;
				margin-right: 5px;
			}

			.mwai-file-upload-icon {
				$icon-size: 32px;
				background: url('icons/white-icons.svg');
				background-size: 500%; // 5 icons in the row
				background-position: -0 * $icon-size -3 * $icon-size;
				width: $icon-size;
				height: $icon-size;
				margin-top: calc(var(--mwai-spacing) / 2);
				margin-left: 5px;
				z-index: 100;
				@include file-upload-icon-sprites;
			}
		}

		.mwai-input-submit {
			width: 70px;
		}
	}

	button {
		margin-left: var(--mwai-spacing);
		padding: 5px 15px;
		background-color: var(--mwai-backgroundUserColor);
		color: white;
		border: none;
		border-radius: var(--mwai-borderRadius);
		cursor: pointer;
		height: 32px;
		display: flex;
		justify-content: center;
		align-items: center;

		.mwai-timer {
			margin-left: 5px;
			margin-right: 5px;
			font-size: 11px;
		}

		&:hover {
			filter: brightness(1.2);
		}
	}

	button[disabled] {
		cursor: not-allowed;

		span {
			opacity: 0.5;
		}

		&.mwai-busy span {
			display: none;
		}

		&.mwai-busy:before {
			content: '';
			width: 18px;
			height: 18px;
			margin: auto;
			border: 3px solid transparent;
			border-top-color: var(--mwai-fontColor);
			border-radius: 50%;
			animation: mwai-button-spinner 1s ease infinite;
		}
	}

	.mwai-compliance {
		opacity: 0.50;
		margin-top: calc(-1 * var(--mwai-spacing));
		padding: calc(var(--mwai-spacing) / 1.5) var(--mwai-spacing);
		font-size: smaller;
		color: var(--mwai-fontColor);
		text-align: left;
	}

	.mwai-gallery {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		grid-gap: 5px;

		img {
			width: 100%;
		}
	}
}

// Discussions

// Common
.mwai-messages-theme {
	@include common-styles;
	@include code-light;
	@include reply-actions;
	@include realtime;
	@include discussions-styles;
}

// Common Overrides
.mwai-messages-theme {

	.mwai-realtime {

		.mwai-visualizer {

			hr {
				border: 1px solid var(--mwai-backgroundAiSecondaryColor);
			}

			.mwai-animation {
				background: var(--mwai-backgroundAiSecondaryColor);
			}
		}

		.mwai-controls {
			button {
				color: var(--mwai-backgroundPrimaryColor);
				background: var(--mwai-backgroundUserColor);

				&:hover {
					color: var(--mwai-backgroundPrimaryColor) !important;
					background: var(--mwai-backgroundUserColor) !important;
					opacity: 0.8;
				}

				&[disabled] {
					color: var(--mwai-backgroundPrimaryColor) !important;
					background: var(--mwai-backgroundUserColor) !important;
					opacity: 0.5;
				}
			}
		}
	}

	.mwai-reply-actions {
		top: 5px;

		.mwai-copy-button {
			padding-top: 4px;

			&:hover {
				fill: var(--mwai-backgroundPrimaryColor);
				background: var(--mwai-backgroundUserColor);
			}
		}
	}
}

// Mobile
.mwai-messages-theme {

	@media (max-width: 760px) {

		&.mwai-window {
			width: calc(100% - 40px);
			z-index: 9999999999;
		}

		.mwai-input {
			flex-direction: column;

			.mwai-input-text {
				width: 100%;
			}

			.mwai-input-submit {
				width: 100%;
				margin: 15px 0 0 0;
			}
		}
	}
}
